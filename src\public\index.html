<!DOCTYPE html>
<html data-theme="dark">

<head>
	<meta charset="UTF-8">
	<meta name="darkreader-lock">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>fount!</title>
	<link href="https://cdn.jsdelivr.net/npm/daisyui/daisyui.css" rel="stylesheet" type="text/css" />
	<link href="/base.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser"></script>
	<script type="module" src="/base.mjs"></script>
	<script>
		const hosturl = window.location.origin
		window.location.href = 'https://steve02081504.github.io/fount/protocol?' + new URLSearchParams({
			hostUrl: hosturl,
			theme: localStorage.getItem('theme') || 'dark'
		})
		setTimeout(() => {
			window.location.href = '/shells/home'
		}, 7000) // 避免网很烂？
	</script>
</head>

<body>
	<div class="flex justify-center items-center h-screen">
		<p class="text-lg">Loading...</p>
	</div>
</body>

</html>
