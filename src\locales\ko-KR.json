{"lang": "ko-KR", "fountConsole": {"server": {"start": "서버를 시작하고 있습니다.", "starting": "서버 시작 중...", "ready": "서버가 시작되었습니다.", "usesdTime": "시작 소요 시간: ${time}s", "showUrl": {"https": "HTTPS 서버가 ${url}에서 실행 중입니다.", "http": "HTTP 서버가 ${url}에서 실행 중입니다."}, "standingBy": "대기 중..."}, "jobs": {"restartingJob": "사용자 ${username}의 ${parttype} ${partname} 작업(${uid})을(를) 다시 시작하는 중입니다."}, "ipc": {"sendCommandFailed": "명령 전송에 실패했습니다: ${error}", "invalidCommand": "잘못된 명령입니다. \"fount runshell <사용자_이름> <셸_이름> <매개변수...>\"를 사용하세요.", "runShellLog": "셸 ${shellname}을(를) ${username}(으)로 실행 중, 매개변수: ${args}", "invokeShellLog": "셸 ${shellname}을(를) ${username}(으)로 호출 중, 매개변수: ${invokedata}", "unsupportedCommand": "지원되지 않는 명령 유형입니다.", "processMessageError": "IPC 메시지 처리 중 오류가 발생했습니다: ${error}", "invalidCommandFormat": "잘못된 명령 형식입니다.", "socketError": "소켓 오류: ${error}", "instanceRunning": "다른 인스턴스가 이미 실행 중입니다.", "serverStartPrefix": "서버 시작", "serverStarted": "IPC 서버가 시작되었습니다.", "parseResponseFailed": "서버 응답 구문 분석에 실패했습니다: ${error}", "cannotParseResponse": "서버 응답을 구문 분석할 수 없습니다.", "unknownError": "알 수 없는 오류가 발생했습니다."}, "partManager": {"git": {"noUpstream": "'${currentBranch}' 브랜치에 업스트림 브랜치가 구성되어 있지 않아 업데이트 확인을 건너뜁니다.", "dirtyWorkingDirectory": "작업 디렉터리가 정리되지 않았습니다. 업데이트하기 전에 변경 사항을 스태시하거나 커밋하세요.", "updating": "원격 리포지토리에서 업데이트하는 중...", "localAhead": "로컬 브랜치가 원격 브랜치보다 최신입니다. 업데이트가 필요하지 않습니다.", "diverged": "로컬 브랜치와 원격 브랜치가 갈라졌습니다. 강제로 업데이트하는 중...", "upToDate": "이미 최신 버전입니다.", "updateFailed": "원격 리포지토리에서 컴포넌트 업데이트에 실패했습니다: ${error}"}, "partInitTime": "${parttype} 컴포넌트 ${partname} 초기화 시간: ${time}s", "partLoadTime": "${parttype} 컴포넌트 ${partname} 로드 시간: ${time}s"}, "web": {"requestReceived": "요청 수신됨: ${method} ${url}"}, "route": {"setLanguagePreference": "사용자 ${username}님이 선호하는 언어를 설정했습니다: ${preferredLanguages}"}, "auth": {"tokenVerifyError": "토큰 검증 오류: ${error}", "refreshTokenError": "리프레시 토큰 오류: ${error}", "logoutRefreshTokenProcessError": "로그아웃 리프레시 토큰 처리 오류: ${error}", "revokeTokenNoJTI": "JTI가 없는 토큰은 해지할 수 없습니다.", "accountLockedLog": "로그인 시도 실패 횟수 초과로 사용자 ${username}님의 계정이 잠겼습니다."}, "verification": {"codeGeneratedLog": "인증 코드: ${code} (60초 후에 만료됩니다).", "codeNotifyTitle": "인증 코드", "codeNotifyBody": "인증 코드: ${code} (60초 후에 만료됩니다)."}, "tray": {"readIconFailed": "아이콘 파일을 읽는 데 실패했습니다: ${error}", "createTrayFailed": "트레이 아이콘 생성에 실패했습니다: ${error}"}, "discordbot": {"botStarted": "캐릭터 ${charname}님이 Discord 봇 ${botusername}에 로그인했습니다."}, "telegrambot": {"botStarted": "캐릭터 ${charname}님이 Telegram 봇 ${botusername}에 로그인했습니다."}}, "protocolhandler": {"title": "Fount 프로토콜 처리", "processing": "프로토콜 처리 중...", "invalidProtocol": "잘못된 프로토콜입니다.", "insufficientParams": "매개변수가 부족합니다.", "unknownCommand": "알 수 없는 명령입니다.", "shellCommandSent": "셸 명령이 전송되었습니다.", "shellCommandFailed": "셸 명령 전송에 실패했습니다.", "shellCommandError": "셸 명령 전송 중 오류가 발생했습니다."}, "auth": {"title": "인증", "subtitle": "사용자 데이터는 로컬에 저장됩니다.", "usernameLabel": "사용자 이름:", "usernameInput": {"placeholder": "사용자 이름을 입력하세요"}, "passwordLabel": "비밀번호:", "passwordInput": {"placeholder": "비밀번호를 입력하세요"}, "confirmPasswordLabel": "비밀번호 확인:", "confirmPasswordInput": {"placeholder": "비밀번호를 다시 입력하세요"}, "verificationCodeLabel": "인증 코드:", "verificationCodeInput": {"placeholder": "인증 코드를 입력하세요"}, "sendCodeButton": "인증 코드 전송", "login": {"title": "로그인", "submitButton": "로그인", "toggleLink": {"text": "계정이 없으신가요?", "link": "지금 회원가입하세요"}}, "register": {"title": "회원가입", "submitButton": "회원가입", "toggleLink": {"text": "이미 계정이 있으신가요?", "link": "지금 로그인하세요"}}, "error": {"passwordMismatch": "비밀번호가 일치하지 않습니다.", "loginError": "로그인 중 오류가 발생했습니다.", "registrationError": "회원가입 중 오류가 발생했습니다.", "verificationCodeError": "인증 코드가 잘못되었거나 만료되었습니다.", "verificationCodeSent": "인증 코드가 성공적으로 전송되었습니다.", "verificationCodeSendError": "인증 코드 전송에 실패했습니다.", "verificationCodeRateLimit": "인증 코드 요청 횟수를 초과했습니다. 잠시 후 다시 시도해주세요.", "lowPasswordStrength": "비밀번호의 보안 수준이 너무 낮습니다.", "accountAlreadyExists": "이미 존재하는 계정입니다."}, "passwordStrength": {"veryWeak": "매우 약함", "weak": "약함", "normal": "보통", "strong": "강함", "veryStrong": "매우 강함"}}, "tutorial": {"title": "튜토리얼 어떠세요?", "modal": {"title": "Fount에 오신 것을 환영합니다!", "instruction": "시작 가이드를 진행하시겠습니까?", "buttons": {"start": "가이드 시작", "skip": "건너뛰기"}}, "endScreen": {"title": "훌륭합니다! 가이드 완료!", "subtitle": "이제 Fount 사용법을 익혔습니다!", "endButton": "시작하기!"}, "progressMessages": {"mouseMove": "손으로 마우스 ${mouseIcon}를 잡고 움직여 보세요.", "keyboardPress": "키보드 ${keyboardIcon}에서 아무 키나 눌러보세요.", "mobileTouchMove": "휴대폰 ${phoneIcon} 화면을 손가락으로 터치한 후 드래그해보세요.", "mobileClick": "휴대폰 ${phoneIcon} 화면을 손가락으로 탭해보세요."}}, "home": {"title": "홈", "escapeConfirm": "Fount를 종료하시겠습니까?", "filterInput": {"placeholder": "검색..."}, "sidebarTitle": "세부 정보", "itemDescription": "자세한 내용을 보려면 여기에서 항목을 선택하세요.", "noDescription": "설명이 없습니다.", "alerts": {"fetchHomeRegistryFailed": "홈 레지스트리 정보를 가져오는 데 실패했습니다."}, "functionMenu": {"icon": {"alt": "기능 메뉴"}}, "chars": {"tab": "캐릭터", "title": "캐릭터 선택", "subtitle": "캐릭터를 선택하고 채팅을 시작하세요!", "none": "표시할 내용이 없습니다", "card": {"refreshButton": {"alt": "새로고침", "title": "새로고침"}, "noTags": "태그 없음", "version": "버전", "author": "제작자", "homepage": "홈페이지", "issuepage": "문제 신고 페이지", "defaultCheckbox": {"title": "기본 캐릭터로 설정"}}}, "worlds": {"tab": "월드", "title": "월드 선택", "subtitle": "월드를 선택하고 몰입해보세요!", "none": "표시할 내용이 없습니다", "card": {"refreshButton": {"alt": "새로고침", "title": "새로고침"}, "noTags": "태그 없음", "version": "버전", "author": "제작자", "homepage": "홈페이지", "issuepage": "문제 신고 페이지", "defaultCheckbox": {"title": "기본 월드로 설정"}}}, "personas": {"tab": "페르소나", "title": "페르소나 선택", "subtitle": "페르소나를 선택하고 다양한 역할을 경험해보세요.", "none": "표시할 내용이 없습니다", "card": {"refreshButton": {"alt": "새로고침", "title": "새로고침"}, "noTags": "태그 없음", "version": "버전", "author": "제작자", "homepage": "홈페이지", "issuepage": "문제 신고 페이지", "defaultCheckbox": {"title": "기본 페르소나로 설정"}}}}, "themeManage": {"title": "테마 관리", "instruction": "테마를 선택하세요!", "themes": {"auto": "자동", "light": "라이트", "dark": "다크", "cupcake": "컵케이크", "bumblebee": "범블비", "emerald": "에메랄드", "corporate": "코퍼레이트", "synthwave": "신스웨이브", "retro": "레트로", "cyberpunk": "사이버 펑크", "valentine": "발렌타인 데이", "halloween": "할로윈", "garden": "가든", "forest": "포레스트", "aqua": "아쿠아", "lofi": "lo-fi", "pastel": "파스텔", "fantasy": "판타지", "wireframe": "와이어 프레임", "black": "블랙", "luxury": "럭셔리", "dracula": "드라큘라", "cmyk": "CMYK", "autumn": "어텀", "business": "사업", "acid": "애시드", "lemonade": "레모네이드", "night": "나이트", "coffee": "커피", "winter": "윈터", "dim": "흐릿한", "nord": "노르딕", "sunset": "선셋", "caramellatte": "카라멜 라떼", "abyss": "어비스", "silk": "실크"}}, "import": {"title": "가져오기", "tabs": {"fileImport": "파일 가져오기", "textImport": "텍스트 가져오기"}, "dropArea": {"icon": {"alt": "업로드 아이콘"}, "text": "여기에 파일을 드래그 앤 드롭하거나 클릭하여 파일을 선택하세요."}, "textArea": {"placeholder": "가져올 텍스트를 입력하세요..."}, "buttons": {"import": "가져오기"}, "alerts": {"importSuccess": "성공적으로 가져왔습니다.", "importFailed": "가져오기에 실패했습니다: ${error}", "unknownError": "알 수 없는 오류가 발생했습니다."}, "errors": {"noFileSelected": "파일을 선택해주세요.", "fileImportFailed": "파일 가져오기에 실패했습니다: ${message}", "noTextContent": "텍스트 내용을 입력해주세요.", "textImportFailed": "텍스트 가져오기에 실패했습니다: ${message}", "unknownError": "알 수 없는 오류가 발생했습니다.", "handler": "핸들러", "error": "오류"}, "fileItem": {"removeButton": {"title": "제거"}, "removeButtonIcon": {"alt": "제거"}}}, "aisource_editor": {"title": "AI 소스 편집기", "fileList": {"title": "AI 소스 목록", "addButton": {"title": "+"}}, "configTitle": "AI 소스 설정", "generatorSelect": {"label": "생성기 선택", "placeholder": "선택해주세요"}, "buttons": {"save": "저장", "delete": "삭제"}, "alerts": {"fetchFileListFailed": "파일 목록을 가져오는 데 실패했습니다: ${error}", "fetchGeneratorListFailed": "생성기 목록을 가져오는 데 실패했습니다: ${error}", "fetchFileDataFailed": "파일 데이터를 가져오는 데 실패했습니다: ${error}", "noFileSelectedSave": "저장할 파일이 선택되지 않았습니다.", "saveFileFailed": "파일 저장에 실패했습니다: ${error}", "noFileSelectedDelete": "삭제할 파일이 선택되지 않았습니다.", "deleteFileFailed": "파일 삭제에 실패했습니다: ${error}", "invalidFileName": "파일 이름에는 다음 문자를 사용할 수 없습니다: / \\ : * ? \" < > |", "addFileFailed": "파일 추가에 실패했습니다: ${error}", "fetchConfigTemplateFailed": "설정 템플릿을 가져오는 데 실패했습니다.", "noGeneratorSelectedSave": "저장하기 전에 생성기를 선택해주세요."}, "confirm": {"unsavedChanges": "저장되지 않은 변경 사항이 있습니다. 변경 사항을 취소하시겠습니까?", "deleteFile": "이 파일을 삭제하시겠습니까?", "unsavedChangesBeforeUnload": "저장되지 않은 변경 사항이 있습니다. 이 페이지를 나가시겠습니까?"}, "prompts": {"newFileName": "새 AI 소스 파일 이름을 입력하세요 (확장자 제외):"}, "editor": {"disabledIndicator": "먼저 생성기를 선택해주세요."}}, "part_config": {"title": "컴포넌트 설정", "pageTitle": "컴포넌트 설정", "labels": {"partType": "컴포넌트 유형 선택", "part": "컴포넌트 선택"}, "placeholders": {"partTypeSelect": "선택해주세요", "partSelect": "선택해주세요"}, "editor": {"title": "컴포넌트 설정", "disabledIndicator": "이 컴포넌트는 설정을 지원하지 않습니다.", "buttons": {"save": "저장"}}, "errorMessage": {"icon": {"alt": "오류 메시지"}}, "alerts": {"fetchPartTypesFailed": "컴포넌트 유형을 가져오는 데 실패했습니다.", "fetchPartsFailed": "컴포넌트 목록을 가져오는 데 실패했습니다.", "loadEditorFailed": "편집기를 로드하는 데 실패했습니다.", "saveConfigFailed": "컴포넌트 설정을 저장하는 데 실패했습니다.", "unsavedChanges": "저장되지 않은 변경 사항이 있습니다. 변경 사항을 취소하시겠습니까?", "beforeUnload": "저장되지 않은 변경 사항이 있습니다. 이 페이지를 나가시겠습니까?"}}, "uninstall": {"title": "제거", "titleWithName": "${type}/${name} 제거", "confirmMessage": "${type}: ${name}을(를) 제거하시겠습니까?", "invalidParamsTitle": "잘못된 매개변수", "infoMessage": {"icon": {"alt": "정보 아이콘"}}, "errorMessage": {"icon": {"alt": "오류 아이콘"}}, "buttons": {"confirm": "제거 확인", "cancel": "취소", "back": "뒤로"}, "alerts": {"success": "${type}: ${name}이(가) 성공적으로 제거되었습니다.", "failed": "제거에 실패했습니다: ${error}", "invalidParams": "잘못된 요청 매개변수입니다.", "httpError": "HTTP 오류! 상태 코드: ${status}"}}, "chat": {"new": {"title": "새 채팅"}, "title": "채팅", "sidebar": {"world": {"icon": {"alt": "월드 아이콘"}, "title": "월드"}, "persona": {"icon": {"alt": "사용자 페르소나 아이콘"}, "title": "사용자 페르소나"}, "charList": {"icon": {"alt": "캐릭터 목록 아이콘"}, "title": "캐릭터 목록", "buttons": {"addChar": {"title": "캐릭터 추가"}, "addCharIcon": {"alt": "캐릭터 추가 아이콘"}}}, "noSelection": "선택되지 않음", "noDescription": "설명 없음"}, "chatArea": {"title": "채팅", "menuButton": {"title": "메뉴"}, "menuButtonIcon": {"alt": "메뉴 아이콘"}, "input": {"placeholder": "메시지를 입력하세요...\\nCtrl+Enter로 전송"}, "sendButton": {"title": "전송"}, "sendButtonIcon": {"alt": "전송 아이콘"}, "uploadButton": {"title": "업로드"}, "uploadButtonIcon": {"alt": "업로드 아이콘"}, "voiceButton": {"title": "음성 입력"}, "voiceButtonIcon": {"alt": "음성 입력 아이콘"}, "photoButton": {"title": "사진"}, "photoButtonIcon": {"alt": "사진 아이콘"}}, "rightSidebar": {"title": "세부 정보"}, "messageList": {"confirmDeleteMessage": "이 메시지를 삭제하시겠습니까?"}, "voiceRecording": {"errorAccessingMicrophone": "마이크에 연결하지 못했습니다."}, "messageView": {"buttons": {"edit": {"title": "편집"}, "editIcon": {"alt": "편집 아이콘"}, "delete": {"title": "삭제"}, "deleteIcon": {"alt": "삭제 아이콘"}}}, "messageEdit": {"input": {"placeholder": "내용을 입력하세요..."}, "buttons": {"confirm": {"title": "확인"}, "confirmIcon": {"alt": "확인 아이콘"}, "cancel": {"title": "취소"}, "cancelIcon": {"alt": "취소 아이콘"}, "upload": {"title": "업로드"}, "uploadIcon": {"alt": "업로드 아이콘"}}}, "attachment": {"buttons": {"download": {"title": "다운로드"}, "downloadIcon": {"alt": "다운로드 아이콘"}, "delete": {"title": "삭제"}, "deleteIcon": {"alt": "삭제 아이콘"}}}, "charCard": {"frequencyLabel": "빈도", "buttons": {"removeChar": {"title": "채팅에서 제거"}, "removeCharIcon": {"alt": "캐릭터 제거 아이콘"}, "forceReply": {"title": "강제 응답"}, "forceReplyIcon": {"alt": "강제 응답 아이콘"}}}}, "chat_history": {"title": "채팅 기록", "pageTitle": "채팅 기록", "sortOptions": {"time_desc": "시간 (최신순)", "time_asc": "시간 (오래된순)"}, "filterInput": {"placeholder": "검색..."}, "selectAll": "모두 선택", "buttons": {"reverseSelect": "선택 반전", "deleteSelected": "선택 항목 삭제", "exportSelected": "선택 항목 내보내기"}, "confirmDeleteChat": "${chars}님과의 채팅 기록을 삭제하시겠습니까?", "confirmDeleteMultiChats": "선택한 ${count}개의 채팅 기록을 삭제하시겠습니까?", "alerts": {"noChatSelectedForDeletion": "삭제할 채팅 기록을 선택해주세요.", "noChatSelectedForExport": "내보낼 채팅 기록을 선택해주세요.", "copyError": "복사 실패", "deleteError": "삭제가 실패했습니다", "exportError": "내보내기 실패"}, "chatItemButtons": {"continue": "계속하기", "copy": "복사", "export": "내보내기", "delete": "삭제"}}, "discord_bots": {"title": "Discord 봇", "cardTitle": "Discord 봇", "buttons": {"newBot": "새로 만들기", "deleteBot": "삭제"}, "configCard": {"title": "봇 설정", "labels": {"character": "캐릭터", "apiKey": "Discord API 키", "config": "설정"}, "charSelectPlaceholder": "캐릭터 선택", "apiKeyInput": {"placeholder": "API 키를 입력하세요"}, "toggleApiKeyIcon": {"alt": "API 키 표시/숨기기"}, "buttons": {"saveConfig": "설정 저장", "startBot": "시작", "stopBot": "중지"}}, "prompts": {"newBotName": "새 봇 이름을 입력하세요:"}, "alerts": {"botExists": "\"${botname}\" 이름의 봇이 이미 존재합니다. 다른 이름을 사용해주세요.", "unsavedChanges": "저장되지 않은 변경 사항이 있습니다. 변경 사항을 취소하시겠습니까?", "configSaved": "설정이 성공적으로 저장되었습니다.", "httpError": "HTTP 오류가 발생했습니다.", "beforeUnload": "저장되지 않은 변경 사항이 있습니다. 이 페이지를 나가시겠습니까?"}}, "telegram_bots": {"title": "텔레그램 봇", "cardTitle": "텔레그램 봇 관리", "buttons": {"newBot": "새로 만들기", "deleteBot": "삭제"}, "configCard": {"title": "봇 설정", "labels": {"character": "연결된 캐릭터", "botToken": "텔레그램 봇 토큰", "config": "설정"}, "charSelectPlaceholder": "캐릭터 선택", "botTokenInput": {"placeholder": "텔레그램 봇 토큰을 입력하세요"}, "toggleBotTokenIcon": {"alt": "봇 토큰 표시/숨기기"}, "buttons": {"saveConfig": "설정 저장", "startBot": "시작", "stopBot": "중지"}}, "prompts": {"newBotName": "새 봇 이름을 입력하세요:"}, "alerts": {"botExists": "\"${botname}\" 이름의 봇이 이미 존재합니다. 다른 이름을 사용해주세요.", "unsavedChanges": "저장되지 않은 변경 사항이 있습니다. 변경 사항을 취소하시겠습니까?", "configSaved": "설정이 성공적으로 저장되었습니다!", "httpError": "HTTP 오류가 발생했습니다.", "beforeUnload": "저장되지 않은 변경 사항이 있습니다. 이 페이지를 나가시겠습니까?"}}, "terminal_assistant": {"title": "터미널 어시스턴트", "initialMessage": "Fount는 코딩 작업을 돕기 위해 즐겨찾는 캐릭터를 터미널에 배포하는 기능을 지원합니다!", "initialMessageLink": "자세한 내용은 여기를 클릭하세요."}, "access": {"title": "다른 기기에서 Fount에 접속", "heading": "다른 기기에서 Fount에 접속하시겠습니까?", "instruction": {"sameLAN": "기기와 Fount 호스트가 동일한 로컬 네트워크에 있는지 확인하세요.", "accessthis": "다음 URL로 접속하세요:"}, "copyButton": "URL 복사", "copied": "URL이 클립보드에 복사되었습니다!"}, "proxy": {"title": "API 프록시", "heading": "OpenAI API 프록시 주소", "instruction": "Fount의 AI 소스를 사용하려면, OpenAI API 형식이 필요한 애플리케이션에 다음 주소를 입력하세요!", "copyButton": "주소 복사", "copied": "주소가 클립보드에 복사되었습니다!"}, "404": {"title": "페이지를 찾을 수 없습니다", "pageNotFoundText": "이런! 존재하지 않는 페이지에 접속한 것 같습니다.", "homepageButton": "홈페이지로 돌아가기", "MineSweeper": {"difficultyLabel": "난이도:", "difficultyEasy": "쉬움", "difficultyMedium": "보통", "difficultyHard": "어려움", "difficultyCustom": "사용자 설정", "minesLeftLabel": "남은 지뢰:", "timeLabel": "시간:", "restartButton": "다시 시작", "rowsLabel": "행:", "colsLabel": "열:", "minesCountLabel": "지뢰 수:", "winMessage": "축하합니다, 승리하셨습니다!", "loseMessage": "게임 오버, 지뢰를 밟았습니다!", "soundOn": "소리 켜짐", "soundOff": "소리 꺼짐"}}, "userSettings": {"title": "사용자 설정", "PageTitle": "사용자 설정", "apiError": "API 요청에 실패했습니다: ${message}", "generalError": "오류가 발생했습니다: ${message}", "userInfo": {"title": "사용자 정보", "usernameLabel": "사용자 이름:", "creationDateLabel": "계정 생성일:", "folderSizeLabel": "사용자 데이터 크기:", "folderPathLabel": "사용자 데이터 경로:", "copyPathBtnTitle": "경로 복사", "copiedAlert": "경로가 클립보드에 복사되었습니다!"}, "changePassword": {"title": "비밀번호 변경", "currentPasswordLabel": "현재 비밀번호:", "newPasswordLabel": "새 비밀번호:", "confirmNewPasswordLabel": "새 비밀번호 확인:", "submitButton": "비밀번호 변경", "errorMismatch": "새 비밀번호가 일치하지 않습니다.", "success": "비밀번호가 성공적으로 변경되었습니다."}, "renameUser": {"title": "사용자 이름 변경", "newUsernameLabel": "새 사용자 이름:", "submitButton": "사용자 이름 변경", "confirmMessage": "사용자 이름을 변경하시겠습니까? 변경 후 다시 로그인해야 합니다.", "success": "사용자 이름이 \"${newUsername}\"(으)로 성공적으로 변경되었습니다. 이제 로그아웃됩니다."}, "userDevices": {"title": "사용자 기기/세션", "refreshButtonTitle": "목록 새로고침", "noDevicesFound": "기기 또는 세션이 없습니다.", "deviceInfo": "기기 ID: ${deviceId}", "thisDevice": "이 기기", "deviceDetails": "마지막 접속: ${lastSeen} | IP: ${ipAddress} | UA: ${userAgent}", "revokeButton": "해지", "revokeConfirm": "이 기기/세션의 액세스 권한을 해지하시겠습니까?", "revokeSuccess": "기기/세션이 성공적으로 해지되었습니다."}, "logout": {"title": "로그아웃", "description": "현재 기기에서 계정을 로그아웃합니다.", "buttonText": "로그아웃", "confirmMessage": "로그아웃하시겠습니까?", "successMessage": "성공적으로 로그아웃되었습니다. 잠시 후 로그인 페이지로 이동합니다..."}, "deleteAccount": {"title": "계정 삭제", "warning": "경고: 이 작업은 계정과 모든 관련 데이터를 영구적으로 삭제하며 복구할 수 없습니다.", "submitButton": "내 계정 삭제", "confirmMessage1": "경고! 계정을 영구적으로 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "confirmMessage2": "삭제를 확인하려면 사용자 이름 \"${username}\"을(를) 입력하세요:", "usernameMismatch": "입력한 사용자 이름이 현재 사용자와 일치하지 않습니다. 삭제 작업이 취소되었습니다.", "success": "계정이 성공적으로 삭제되었습니다. 이제 로그아웃됩니다."}, "passwordConfirm": {"title": "작업 확인", "message": "계속하려면 현재 비밀번호를 입력하세요:", "passwordLabel": "비밀번호:", "confirmButton": "확인", "cancelButton": "취소"}}}