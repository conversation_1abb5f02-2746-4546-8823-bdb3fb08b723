# 桌面应用框架选型分析：Tauri vs Electron

## 框架对比概览

Fount项目当前使用传统的Web服务器模式，但具备桌面应用的特征（系统托盘、协议处理、本地文件访问）。为了提供更好的用户体验，我们需要选择合适的桌面应用框架。

## Tauri 2.0 深度分析

### 1. 架构优势

#### 核心架构
```
┌─────────────────────────────────────┐
│           Frontend (Web)            │
│     HTML + CSS + JavaScript        │
│        (保持现有前端不变)            │
└─────────────────┬───────────────────┘
                  │ IPC Bridge
┌─────────────────▼───────────────────┐
│         Backend (Rust Core)        │
│    • 文件系统访问                   │
│    • 系统API调用                    │
│    • 网络请求                       │
│    • 数据库操作                     │
└─────────────────────────────────────┘
```

#### 与Electron对比
| 特性 | Tauri 2.0 | Electron | 优势 |
|------|-----------|----------|------|
| **运行时** | 系统WebView + Rust | Chromium + Node.js | 更小体积 |
| **内存使用** | ~10-50MB | ~100-300MB | 5-10倍减少 |
| **安装包大小** | ~10-20MB | ~100-200MB | 10倍减少 |
| **启动速度** | 快 | 中等 | 更快启动 |
| **系统集成** | 原生 | 通过Node.js | 更好集成 |
| **安全性** | 沙箱化 | 需要配置 | 默认安全 |

### 2. Tauri 2.0 新特性

#### 多平台支持
- **桌面**: Windows, macOS, Linux
- **移动**: iOS, Android (Beta)
- **Web**: 渐进式Web应用

#### 增强的IPC系统
```rust
// Tauri 2.0 IPC示例
#[tauri::command]
async fn load_character(name: String) -> Result<Character, String> {
    // 调用现有的Rust服务器逻辑
    character_manager::load_char(&name).await
        .map_err(|e| e.to_string())
}

// 前端调用
const character = await invoke('load_character', { name: 'example' });
```

#### 系统托盘集成
```rust
use tauri::{SystemTray, SystemTrayMenu, SystemTrayMenuItem};

let tray_menu = SystemTrayMenu::new()
    .add_item(SystemTrayMenuItem::new("Open Fount", "open"))
    .add_item(SystemTrayMenuItem::new("Settings", "settings"))
    .add_separator()
    .add_item(SystemTrayMenuItem::new("Quit", "quit"));

let system_tray = SystemTray::new().with_menu(tray_menu);
```

### 3. Fount集成策略

#### 架构设计
```rust
// src-tauri/src/main.rs
use fount_server::ServerInstance;

#[tokio::main]
async fn main() {
    // 启动内嵌的Fount服务器
    let server = ServerInstance::new().await.unwrap();
    let server_handle = tokio::spawn(async move {
        server.run().await
    });

    // 启动Tauri应用
    tauri::Builder::default()
        .system_tray(create_system_tray())
        .on_system_tray_event(handle_tray_event)
        .invoke_handler(tauri::generate_handler![
            load_character,
            save_settings,
            open_file_dialog,
        ])
        .setup(|app| {
            // 应用初始化
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

#### 前端保持不变
- 继续使用现有的HTML/CSS/JavaScript
- 保持DaisyUI + TailwindCSS技术栈
- 通过Tauri IPC增强本地功能

#### 渐进式迁移
1. **阶段1**: 包装现有Web应用
2. **阶段2**: 添加系统托盘和协议处理
3. **阶段3**: 增强本地文件访问
4. **阶段4**: 优化性能和用户体验

## 与现有系统的集成

### 1. 服务器集成
```rust
// 内嵌Fount服务器
pub struct TauriApp {
    server: Arc<FountServer>,
    port: u16,
}

impl TauriApp {
    pub async fn new() -> Result<Self> {
        let server = Arc::new(FountServer::new().await?);
        let port = server.start().await?;
        
        Ok(Self { server, port })
    }
    
    pub fn get_url(&self) -> String {
        format!("http://localhost:{}", self.port)
    }
}
```

### 2. 系统托盘功能
```rust
#[tauri::command]
async fn toggle_window(window: tauri::Window) {
    if window.is_visible().unwrap() {
        window.hide().unwrap();
    } else {
        window.show().unwrap();
        window.set_focus().unwrap();
    }
}

fn handle_tray_event(app: &AppHandle, event: SystemTrayEvent) {
    match event {
        SystemTrayEvent::LeftClick { .. } => {
            let window = app.get_window("main").unwrap();
            toggle_window(window).await;
        }
        SystemTrayEvent::MenuItemClick { id, .. } => {
            match id.as_str() {
                "open" => {
                    // 打开主窗口
                }
                "quit" => {
                    app.exit(0);
                }
                _ => {}
            }
        }
        _ => {}
    }
}
```

### 3. 协议处理
```rust
// tauri.conf.json
{
  "tauri": {
    "protocol": {
      "scheme": "fount",
      "handler": "protocol_handler"
    }
  }
}

// Rust处理器
#[tauri::command]
async fn handle_protocol(url: String) -> Result<(), String> {
    // 解析fount://协议URL
    // 调用相应的安装或操作逻辑
    Ok(())
}
```

### 4. 文件系统访问
```rust
#[tauri::command]
async fn select_import_file() -> Result<String, String> {
    use tauri::api::dialog::FileDialogBuilder;
    
    let file_path = FileDialogBuilder::new()
        .add_filter("Fount Files", &["zip", "json"])
        .pick_file()
        .await;
        
    match file_path {
        Some(path) => Ok(path.to_string_lossy().to_string()),
        None => Err("No file selected".to_string()),
    }
}
```

## 配置文件结构

### 1. tauri.conf.json
```json
{
  "build": {
    "beforeDevCommand": "npm run dev",
    "beforeBuildCommand": "npm run build",
    "devPath": "http://localhost:3000",
    "distDir": "../static"
  },
  "package": {
    "productName": "Fount",
    "version": "1.0.0"
  },
  "tauri": {
    "allowlist": {
      "all": false,
      "shell": {
        "all": false,
        "open": true
      },
      "dialog": {
        "all": false,
        "open": true,
        "save": true
      },
      "fs": {
        "all": false,
        "readFile": true,
        "writeFile": true,
        "createDir": true,
        "removeDir": true,
        "copyFile": true,
        "metadata": true
      }
    },
    "bundle": {
      "active": true,
      "targets": "all",
      "identifier": "com.fount.app",
      "icon": [
        "icons/32x32.png",
        "icons/128x128.png",
        "icons/icon.icns",
        "icons/icon.ico"
      ]
    },
    "security": {
      "csp": null
    },
    "updater": {
      "active": false
    },
    "windows": [
      {
        "fullscreen": false,
        "resizable": true,
        "title": "Fount",
        "width": 1200,
        "height": 800,
        "minWidth": 800,
        "minHeight": 600
      }
    ],
    "systemTray": {
      "iconPath": "icons/icon.png",
      "iconAsTemplate": true
    }
  }
}
```

### 2. Cargo.toml (Tauri部分)
```toml
[package]
name = "fount-desktop"
version = "1.0.0"
edition = "2021"

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
tauri = { version = "2.0", features = ["system-tray", "protocol-all"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }

# 引用现有的Fount服务器
fount-server = { path = "../fount-server" }
fount-types = { path = "../fount-types" }
fount-utils = { path = "../fount-utils" }
```

## 部署和分发

### 1. 构建配置
```bash
# 开发模式
cargo tauri dev

# 生产构建
cargo tauri build

# 跨平台构建
cargo tauri build --target x86_64-pc-windows-msvc
cargo tauri build --target x86_64-apple-darwin
cargo tauri build --target x86_64-unknown-linux-gnu
```

### 2. 安装包生成
- **Windows**: MSI安装包
- **macOS**: DMG磁盘映像
- **Linux**: AppImage, DEB, RPM

### 3. 自动更新
```rust
// 可选的自动更新功能
use tauri::updater;

#[tauri::command]
async fn check_for_updates() -> Result<bool, String> {
    match updater::check().await {
        Ok(update) => {
            if update.is_update_available() {
                update.download_and_install().await
                    .map_err(|e| e.to_string())?;
                Ok(true)
            } else {
                Ok(false)
            }
        }
        Err(e) => Err(e.to_string())
    }
}
```

## 迁移优势

### 1. 用户体验提升
- **原生感觉**: 系统托盘、原生菜单、文件对话框
- **更快启动**: 减少启动时间
- **更少资源**: 显著降低内存和CPU使用
- **离线能力**: 更好的离线功能支持

### 2. 开发体验
- **代码复用**: 前端代码完全保持不变
- **类型安全**: Rust后端提供类型安全
- **调试友好**: 更好的调试工具和错误信息
- **性能监控**: 内置性能分析工具

### 3. 部署优势
- **更小体积**: 显著减少分发包大小
- **更快下载**: 用户下载和安装更快
- **更好兼容**: 更好的系统兼容性
- **安全性**: 默认的安全沙箱

## 实施计划

### 阶段1: 基础Tauri集成 (1-2周)
- [ ] 创建Tauri项目结构
- [ ] 集成现有Fount服务器
- [ ] 基础窗口和托盘功能
- [ ] 协议处理器注册

### 阶段2: 功能增强 (2-3周)
- [ ] 文件系统访问优化
- [ ] 系统通知集成
- [ ] 自动启动功能
- [ ] 设置和配置管理

### 阶段3: 优化和测试 (1-2周)
- [ ] 性能优化
- [ ] 跨平台测试
- [ ] 安装包生成
- [ ] 文档和用户指南

这个选择确保了我们能够以最小的前端改动获得显著的性能提升和更好的用户体验，同时保持代码的可维护性和扩展性。
