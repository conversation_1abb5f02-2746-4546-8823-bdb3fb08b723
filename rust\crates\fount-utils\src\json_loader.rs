//! JSON文件加载工具
//! 对应原文件: src/scripts/json_loader.mjs

use serde::{Deserialize, Serialize};
use std::path::Path;
use tokio::fs;
use anyhow::Result;

/// JSON文件加载器
pub struct JsonLoader;

impl JsonLoader {
    /// 加载JSON文件
    pub async fn load<T, P>(path: P) -> Result<T>
    where
        T: for<'de> Deserialize<'de>,
        P: AsRef<Path>,
    {
        let content = fs::read_to_string(path).await?;
        let data = serde_json::from_str(&content)?;
        Ok(data)
    }
    
    /// 保存JSON文件
    pub async fn save<T, P>(path: P, data: &T) -> Result<()>
    where
        T: Serialize,
        P: AsRef<Path>,
    {
        let content = serde_json::to_string_pretty(data)?;
        fs::write(path, content).await?;
        Ok(())
    }
    
    /// 加载JSON文件（同步版本）
    pub fn load_sync<T, P>(path: P) -> Result<T>
    where
        T: for<'de> Deserialize<'de>,
        P: AsRef<Path>,
    {
        let content = std::fs::read_to_string(path)?;
        let data = serde_json::from_str(&content)?;
        Ok(data)
    }
    
    /// 保存JSON文件（同步版本）
    pub fn save_sync<T, P>(path: P, data: &T) -> Result<()>
    where
        T: Serialize,
        P: AsRef<Path>,
    {
        let content = serde_json::to_string_pretty(data)?;
        std::fs::write(path, content)?;
        Ok(())
    }
}
