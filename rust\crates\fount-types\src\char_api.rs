//! 角色API类型定义
//! 对应原文件: src/decl/charAPI.ts

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use crate::{ApiResponse, Status};

/// 角色信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Character {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub avatar_url: Option<String>,
    pub personality: String,
    pub background: String,
    pub greeting: String,
    pub example_conversations: Vec<String>,
    pub tags: Vec<String>,
    pub creator_id: String,
    pub status: Status,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 角色创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateCharacterRequest {
    pub name: String,
    pub description: String,
    pub avatar_url: Option<String>,
    pub personality: String,
    pub background: String,
    pub greeting: String,
    pub example_conversations: Vec<String>,
    pub tags: Vec<String>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// 角色更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateCharacterRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub avatar_url: Option<String>,
    pub personality: Option<String>,
    pub background: Option<String>,
    pub greeting: Option<String>,
    pub example_conversations: Option<Vec<String>>,
    pub tags: Option<Vec<String>>,
    pub status: Option<Status>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// 角色搜索请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchCharacterRequest {
    pub query: Option<String>,
    pub tags: Option<Vec<String>>,
    pub creator_id: Option<String>,
    pub status: Option<Status>,
    pub page: Option<u32>,
    pub per_page: Option<u32>,
}

/// 角色列表响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterListResponse {
    pub characters: Vec<Character>,
    pub pagination: crate::Pagination,
}

/// 角色统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterStats {
    pub total_characters: u64,
    pub active_characters: u64,
    pub total_conversations: u64,
    pub total_messages: u64,
}

/// 角色API响应类型别名
pub type CharacterResponse = ApiResponse<Character>;
pub type CharacterListApiResponse = ApiResponse<CharacterListResponse>;
pub type CharacterStatsResponse = ApiResponse<CharacterStats>;
