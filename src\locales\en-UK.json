{"lang": "en-UK", "fountConsole": {"server": {"start": "Starting server", "starting": "Server starting...", "ready": "Server is ready", "usesdTime": "Startup time: ${time}s", "showUrl": {"https": "HTTPS server running on ${url}", "http": "HTTP server running on ${url}"}, "standingBy": "Standing by..."}, "jobs": {"restartingJob": "Restarting ${parttype} ${partname} job for user ${username}: ${uid}"}, "ipc": {"sendCommandFailed": "Failed to send command: ${error}", "invalidCommand": "Invalid command. Please use \"fount runshell <username> <shellname> <parameters...>\"", "runShellLog": "Running shell ${shellname} as ${username}, parameters: ${args}", "invokeShellLog": "Invoking shell ${shellname} as ${username}, parameters: ${invokedata}", "unsupportedCommand": "Unsupported command type", "processMessageError": "Error processing IPC message: ${error}", "invalidCommandFormat": "Invalid command format", "socketError": "Socket Error: ${error}", "instanceRunning": "Another instance is already running", "serverStartPrefix": "Server start", "serverStarted": "IPC server started", "parseResponseFailed": "Failed to parse server response: ${error}", "cannotParseResponse": "Cannot parse server response", "unknownError": "Unknown error"}, "partManager": {"git": {"noUpstream": "No upstream branch configured for branch '${currentBranch}', skipping update check.", "dirtyWorkingDirectory": "Working directory is not clean. Please stash or commit your changes before updating.", "updating": "Updating from remote repository...", "localAhead": "Local branch is ahead of remote branch. No update needed.", "diverged": "Local and remote branches have diverged. Forcing update...", "upToDate": "Already up-to-date.", "updateFailed": "Failed to update components from remote repository: ${error}"}, "partInitTime": "${parttype} Component ${partname} Initialisation time: ${time}s", "partLoadTime": "${parttype} Component ${partname} Loading time: ${time}s"}, "web": {"requestReceived": "Request received: ${method} ${url}"}, "route": {"setLanguagePreference": "User ${username} set preferred language: ${preferredLanguages}"}, "auth": {"tokenVerifyError": "Token verification error: ${error}", "refreshTokenError": "Refresh token error: ${error}", "logoutRefreshTokenProcessError": "Logout refresh token process error: ${error}", "revokeTokenNoJTI": "Cannot revoke token without JTI.", "accountLockedLog": "User account ${username} locked due to multiple failed login attempts."}, "verification": {"codeGeneratedLog": "Verification code: ${code} (expires in 60 seconds)", "codeNotifyTitle": "Verification Code", "codeNotifyBody": "Verification code: ${code} (expires in 60 seconds)"}, "tray": {"readIconFailed": "Failed to read icon file: ${error}", "createTrayFailed": "Failed to create tray: ${error}"}, "discordbot": {"botStarted": "Character ${charname} has logged in to the Discord bot ${botusername}."}, "telegrambot": {"botStarted": "Character ${charname} has logged in to the Telegram bot ${botusername}."}}, "protocolhandler": {"title": "Handling Fount Protocol", "processing": "Processing protocol...", "invalidProtocol": "Invalid protocol", "insufficientParams": "Insufficient parameters", "unknownCommand": "Unknown command", "shellCommandSent": "Shell command sent", "shellCommandFailed": "Failed to send shell command", "shellCommandError": "Error sending shell command"}, "auth": {"title": "Authentication", "subtitle": "User data is stored locally", "usernameLabel": "Username:", "usernameInput": {"placeholder": "Enter username"}, "passwordLabel": "Password:", "passwordInput": {"placeholder": "Enter password"}, "confirmPasswordLabel": "Confirm Password:", "confirmPasswordInput": {"placeholder": "Enter password again"}, "verificationCodeLabel": "Verification Code:", "verificationCodeInput": {"placeholder": "Enter verification code"}, "sendCodeButton": "Send Code", "login": {"title": "Log In", "submitButton": "Log In", "toggleLink": {"text": "No account?", "link": "Register now"}}, "register": {"title": "Register", "submitButton": "Register", "toggleLink": {"text": "Already have an account?", "link": "Log in now"}}, "error": {"passwordMismatch": "Passwords do not match.", "loginError": "Login error.", "registrationError": "Registration error.", "verificationCodeError": "Verification code is incorrect or has expired.", "verificationCodeSent": "Verification code sent successfully.", "verificationCodeSendError": "Failed to send verification code.", "verificationCodeRateLimit": "Too many verification code requests. Please try again later.", "lowPasswordStrength": "Password strength is too low.", "accountAlreadyExists": "Account already exists."}, "passwordStrength": {"veryWeak": "Very weak", "weak": "Weak", "normal": "Normal", "strong": "Strong", "veryStrong": "Very strong"}}, "tutorial": {"title": "Tutorial?", "modal": {"title": "Welcome to Fount!", "instruction": "Would you like to take the introductory tutorial?", "buttons": {"start": "Start Tutorial", "skip": "<PERSON><PERSON>"}}, "endScreen": {"title": "Awesome! Tutorial Completed!", "subtitle": "You're now familiar with the basics!", "endButton": "Get Started!"}, "progressMessages": {"mouseMove": "Please try moving the mouse ${mouseIcon} using your hand.", "keyboardPress": "Press a key on your keyboard ${keyboardIcon}.", "mobileTouchMove": "Try touching and dragging your finger on the screen of your phone ${phoneIcon}.", "mobileClick": "Try tapping the screen of your phone ${phoneIcon} with your finger."}}, "home": {"title": "Home", "escapeConfirm": "Are you sure you want to leave Fount?", "filterInput": {"placeholder": "Search..."}, "sidebarTitle": "Details", "itemDescription": "Select an item here to view details.", "noDescription": "No description available.", "alerts": {"fetchHomeRegistryFailed": "Failed to fetch home registry information."}, "functionMenu": {"icon": {"alt": "Function Menu"}}, "chars": {"tab": "Characters", "title": "Character Selection", "subtitle": "Select a character—then start chatting!", "none": "Nothing to show", "card": {"refreshButton": {"alt": "Refresh", "title": "Refresh"}, "noTags": "No tags", "version": "Version", "author": "Author", "homepage": "Homepage", "issuepage": "Issue Page", "defaultCheckbox": {"title": "Set as default character"}}}, "worlds": {"tab": "Worlds", "title": "World Selection", "subtitle": "Choose a world, and immerse yourself!", "none": "Nothing to show", "card": {"refreshButton": {"alt": "Refresh", "title": "Refresh"}, "noTags": "No tags", "version": "Version", "author": "Author", "homepage": "Homepage", "issuepage": "Issue Page", "defaultCheckbox": {"title": "Set as default world"}}}, "personas": {"tab": "Personas", "title": "Persona Selection", "subtitle": "Select a persona, experience life.", "none": "Nothing to show", "card": {"refreshButton": {"alt": "Refresh", "title": "Refresh"}, "noTags": "No tags", "version": "Version", "author": "Author", "homepage": "Homepage", "issuepage": "Issue Page", "defaultCheckbox": {"title": "Set as default persona"}}}}, "themeManage": {"title": "Theme Management", "instruction": "Choose a theme!", "themes": {"auto": "Automatic", "light": "Light", "dark": "Dark", "cupcake": "Cupcake", "bumblebee": "Bumblebee", "emerald": "Emerald", "corporate": "Corporate", "synthwave": "Synthwave", "retro": "Retro", "cyberpunk": "Cyberpunk", "valentine": "Valentine's Day", "halloween": "Halloween", "garden": "Garden", "forest": "Forest", "aqua": "Aqua", "lofi": "lo-fi", "pastel": "Pastel", "fantasy": "Fantasy", "wireframe": "Wireframe", "black": "Black", "luxury": "Luxury", "dracula": "Dracula", "cmyk": "CMYK", "autumn": "Autumn", "business": "Business", "acid": "Acid", "lemonade": "Lemonade", "night": "Night", "coffee": "Coffee", "winter": "Winter", "dim": "<PERSON><PERSON>", "nord": "Nord", "sunset": "Sunset", "caramellatte": "Caramel Latte", "abyss": "Abyss", "silk": "Silk"}}, "import": {"title": "Import", "tabs": {"fileImport": "File Import", "textImport": "Text Import"}, "dropArea": {"icon": {"alt": "Upload Icon"}, "text": "Drag and drop files here or click to select files"}, "textArea": {"placeholder": "Enter text to import..."}, "buttons": {"import": "Import"}, "alerts": {"importSuccess": "Import successful.", "importFailed": "Import failed: ${error}", "unknownError": "Unknown error."}, "errors": {"noFileSelected": "Please select a file.", "fileImportFailed": "File import failed: ${message}", "noTextContent": "Please enter text content.", "textImportFailed": "Text import failed: ${message}", "unknownError": "Unknown error.", "handler": "Handler", "error": "Error"}, "fileItem": {"removeButton": {"title": "Remove"}, "removeButtonIcon": {"alt": "Remove"}}}, "aisource_editor": {"title": "AI Source Editor", "fileList": {"title": "AI Source List", "addButton": {"title": "+"}}, "configTitle": "AI Source Configuration", "generatorSelect": {"label": "Select Generator", "placeholder": "Please select"}, "buttons": {"save": "Save", "delete": "Delete"}, "alerts": {"fetchFileListFailed": "Failed to get file list: ${error}", "fetchGeneratorListFailed": "Failed to get generator list: ${error}", "fetchFileDataFailed": "Failed to get file data: ${error}", "noFileSelectedSave": "No file selected to save.", "saveFileFailed": "Failed to save file: ${error}", "noFileSelectedDelete": "No file selected to delete.", "deleteFileFailed": "Failed to delete file: ${error}", "invalidFileName": "File name cannot contain the following characters: / \\ : * ? \" < > |", "addFileFailed": "Failed to add file: ${error}", "fetchConfigTemplateFailed": "Failed to fetch config template.", "noGeneratorSelectedSave": "Please select a generator before saving."}, "confirm": {"unsavedChanges": "You have unsaved changes. Do you want to discard changes?", "deleteFile": "Are you sure you want to delete the file?", "unsavedChangesBeforeUnload": "You have unsaved changes. Are you sure you want to leave this page?"}, "prompts": {"newFileName": "Please enter a new AI source file name (without extension):"}, "editor": {"disabledIndicator": "Please select a generator first."}}, "part_config": {"title": "Component Configuration", "pageTitle": "Component Configuration", "labels": {"partType": "Select Component Type", "part": "Select Component"}, "placeholders": {"partTypeSelect": "Please select", "partSelect": "Please select"}, "editor": {"title": "Component Configuration", "disabledIndicator": "This component does not support configuration.", "buttons": {"save": "Save"}}, "errorMessage": {"icon": {"alt": "Error Prompt"}}, "alerts": {"fetchPartTypesFailed": "Failed to get component types.", "fetchPartsFailed": "Failed to get component list.", "loadEditorFailed": "Failed to load editor.", "saveConfigFailed": "Failed to save component configuration.", "unsavedChanges": "You have unsaved changes. Do you want to discard changes?", "beforeUnload": "You have unsaved changes. Are you sure you want to leave?"}}, "uninstall": {"title": "Uninstall", "titleWithName": "Uninstall ${type}/${name}", "confirmMessage": "Are you sure you want to uninstall ${type}: ${name}?", "invalidParamsTitle": "Invalid Parameters", "infoMessage": {"icon": {"alt": "Info Icon"}}, "errorMessage": {"icon": {"alt": "<PERSON>rror <PERSON>"}}, "buttons": {"confirm": "Confirm Uninstall", "cancel": "Cancel", "back": "Back"}, "alerts": {"success": "${type}: ${name} uninstalled successfully.", "failed": "Uninstall failed: ${error}", "invalidParams": "Invalid request parameters.", "httpError": "HTTP Error! Status code: ${status}"}}, "chat": {"new": {"title": "New Chat"}, "title": "Cha<PERSON>", "sidebar": {"world": {"icon": {"alt": "World Icon"}, "title": "World"}, "persona": {"icon": {"alt": "User Persona Icon"}, "title": "User Persona"}, "charList": {"icon": {"alt": "Character List Icon"}, "title": "Character List", "buttons": {"addChar": {"title": "Add Character"}, "addCharIcon": {"alt": "Add Character Icon"}}}, "noSelection": "Not selected", "noDescription": "No description available."}, "chatArea": {"title": "Cha<PERSON>", "menuButton": {"title": "<PERSON><PERSON>"}, "menuButtonIcon": {"alt": "Menu Icon"}, "input": {"placeholder": "Enter message...\\nCtrl+Enter to send"}, "sendButton": {"title": "Send"}, "sendButtonIcon": {"alt": "Send Icon"}, "uploadButton": {"title": "Upload"}, "uploadButtonIcon": {"alt": "Upload Icon"}, "voiceButton": {"title": "Voice Input"}, "voiceButtonIcon": {"alt": "Voice Input Icon"}, "photoButton": {"title": "Photo"}, "photoButtonIcon": {"alt": "Photo Icon"}}, "rightSidebar": {"title": "Details"}, "messageList": {"confirmDeleteMessage": "Are you sure you want to delete this message?"}, "voiceRecording": {"errorAccessingMicrophone": "Failed to access microphone."}, "messageView": {"buttons": {"edit": {"title": "Edit"}, "editIcon": {"alt": "Edit Icon"}, "delete": {"title": "Delete"}, "deleteIcon": {"alt": "Delete Icon"}}}, "messageEdit": {"input": {"placeholder": "Enter content..."}, "buttons": {"confirm": {"title": "Confirm"}, "confirmIcon": {"alt": "Confirm Icon"}, "cancel": {"title": "Cancel"}, "cancelIcon": {"alt": "Cancel Icon"}, "upload": {"title": "Upload"}, "uploadIcon": {"alt": "Upload Icon"}}}, "attachment": {"buttons": {"download": {"title": "Download"}, "downloadIcon": {"alt": "Download Icon"}, "delete": {"title": "Delete"}, "deleteIcon": {"alt": "Delete Icon"}}}, "charCard": {"frequencyLabel": "Frequency", "buttons": {"removeChar": {"title": "Remove from Chat"}, "removeCharIcon": {"alt": "Remove Character Icon"}, "forceReply": {"title": "Force Reply"}, "forceReplyIcon": {"alt": "Force Reply Icon"}}}}, "chat_history": {"title": "Chat History", "pageTitle": "Chat History", "sortOptions": {"time_desc": "Time Descending", "time_asc": "Time Ascending"}, "filterInput": {"placeholder": "Search..."}, "selectAll": "Select All", "buttons": {"reverseSelect": "Invert Selection", "deleteSelected": "Delete Selected", "exportSelected": "Export Selected"}, "confirmDeleteChat": "Are you sure you want to delete the chat history with ${chars}?", "confirmDeleteMultiChats": "Are you sure you want to delete the selected ${count} chat histories?", "alerts": {"noChatSelectedForDeletion": "Please select chat histories to delete.", "noChatSelectedForExport": "Please select chat histories to export.", "copyError": "Co<PERSON> failed", "deleteError": "Deletion failed", "exportError": "Export failed"}, "chatItemButtons": {"continue": "Continue", "copy": "Copy", "export": "Export", "delete": "Delete"}}, "discord_bots": {"title": "<PERSON>rd <PERSON>", "cardTitle": "<PERSON>rd <PERSON>", "buttons": {"newBot": "New", "deleteBot": "Delete"}, "configCard": {"title": "Bot Configuration", "labels": {"character": "Character", "apiKey": "Discord API Key", "config": "Configuration"}, "charSelectPlaceholder": "Select Character", "apiKeyInput": {"placeholder": "Enter API Key"}, "toggleApiKeyIcon": {"alt": "Toggle API Key Visibility"}, "buttons": {"saveConfig": "Save Configuration", "startBot": "Start", "stopBot": "Stop"}}, "prompts": {"newBotName": "Please enter a new <PERSON><PERSON> name:"}, "alerts": {"botExists": "A bot named \"${botname}\" already exists. Please use another name.", "unsavedChanges": "You have unsaved changes. Do you want to discard these changes?", "configSaved": "Configuration saved successfully.", "httpError": "HTTP Error.", "beforeUnload": "You have unsaved changes. Are you sure you want to leave?"}}, "telegram_bots": {"title": "Telegram Bots", "cardTitle": "Telegram Bot Management", "buttons": {"newBot": "New", "deleteBot": "Delete"}, "configCard": {"title": "Bot Configuration", "labels": {"character": "Bound Character", "botToken": "Telegram Bot Token", "config": "Configuration"}, "charSelectPlaceholder": "Select Character", "botTokenInput": {"placeholder": "Enter Telegram Bot Token"}, "toggleBotTokenIcon": {"alt": "Toggle Bot Token Visibility"}, "buttons": {"saveConfig": "Save Configuration", "startBot": "Start", "stopBot": "Stop"}}, "prompts": {"newBotName": "Please enter a new <PERSON><PERSON> name:"}, "alerts": {"botExists": "A bot named \"${botname}\" already exists. Please use a different name.", "unsavedChanges": "You have unsaved changes. Are you sure you want to discard these changes?", "configSaved": "Configuration has been saved successfully!", "httpError": "HTTP Error.", "beforeUnload": "You have unsaved changes. Are you sure you want to leave the current page?"}}, "terminal_assistant": {"title": "Terminal Assistant", "initialMessage": "Fount supports deploying your favourite characters to your terminal to assist you in coding!", "initialMessageLink": "Click here to learn more."}, "access": {"title": "Access Fount on other devices", "heading": "Want to access Fount on other devices?", "instruction": {"sameLAN": "Ensure the device and Fount host are on the same local network.", "accessthis": "Visit this URL:"}, "copyButton": "Copy URL", "copied": "URL copied to clipboard!"}, "proxy": {"title": "API Proxy", "heading": "OpenAI API Proxy Address", "instruction": "Enter the following address into any application that requires the OpenAI API format to use the AI sources in Fount!", "copyButton": "Copy Address", "copied": "Address copied to clipboard!"}, "404": {"title": "Page Not Found", "pageNotFoundText": "Oops! It seems you've stumbled upon a page that doesn't exist.", "homepageButton": "Back to Homepage", "MineSweeper": {"difficultyLabel": "Difficulty:", "difficultyEasy": "Easy", "difficultyMedium": "Medium", "difficultyHard": "Hard", "difficultyCustom": "Custom", "minesLeftLabel": "Mines Left:", "timeLabel": "Time:", "restartButton": "<PERSON><PERSON>", "rowsLabel": "Rows:", "colsLabel": "Columns:", "minesCountLabel": "Mines Count:", "winMessage": "Congratulations, you won!", "loseMessage": "Game Over, you hit a mine!", "soundOn": "Sound On", "soundOff": "Sound Off"}}, "userSettings": {"title": "User Settings", "PageTitle": "User Settings", "apiError": "API request failed: ${message}", "generalError": "An error occurred: ${message}", "userInfo": {"title": "User Information", "usernameLabel": "Username:", "creationDateLabel": "Account Creation Date:", "folderSizeLabel": "User Data Size:", "folderPathLabel": "User Data Path:", "copyPathBtnTitle": "Copy Path", "copiedAlert": "Path copied to clipboard!"}, "changePassword": {"title": "Change Password", "currentPasswordLabel": "Current Password:", "newPasswordLabel": "New Password:", "confirmNewPasswordLabel": "Confirm New Password:", "submitButton": "Change Password", "errorMismatch": "Passwords do not match.", "success": "Password changed successfully."}, "renameUser": {"title": "Rename User", "newUsernameLabel": "New Username:", "submitButton": "Rename User", "confirmMessage": "Are you sure you want to change your username? This will require you to log in again.", "success": "User successfully renamed to \"${newUsername}\". You will now be logged out."}, "userDevices": {"title": "User Devices/Sessions", "refreshButtonTitle": "Refresh List", "noDevicesFound": "No devices or sessions found.", "deviceInfo": "Device ID: ${deviceId}", "thisDevice": "This Device", "deviceDetails": "Last online: ${lastSeen} | IP: ${ipAddress} | UA: ${userAgent}", "revokeButton": "Revoke", "revokeConfirm": "Are you sure you want to revoke this device/session?", "revokeSuccess": "Device/session successfully revoked."}, "logout": {"title": "Log Out", "description": "This will log you out of your account on the current device.", "buttonText": "Log Out", "confirmMessage": "Are you sure you want to log out?", "successMessage": "Logged out successfully. Redirecting to the login page..."}, "deleteAccount": {"title": "Delete Account", "warning": "Warning: This action will permanently delete your account and all related data, and it cannot be recovered.", "submitButton": "Delete My Account", "confirmMessage1": "Warning! Are you sure you want to permanently delete your account? This action cannot be undone.", "confirmMessage2": "To confirm deletion, please enter your username \"${username}\":", "usernameMismatch": "The entered username does not match the current user. Deletion has been cancelled.", "success": "Account successfully deleted. You will now be logged out."}, "passwordConfirm": {"title": "Confirm Operation", "message": "To continue, please enter your current password:", "passwordLabel": "Password:", "confirmButton": "Confirm", "cancelButton": "Cancel"}}}