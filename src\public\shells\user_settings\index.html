<!DOCTYPE html>
<html data-theme="dark">

<head>
	<meta charset="UTF-8">
	<meta name="darkreader-lock">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title data-i18n="userSettings.title"></title>
	<link href="https://cdn.jsdelivr.net/npm/daisyui/daisyui.css" rel="stylesheet" type="text/css" />
	<link href="/base.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser"></script>
	<script type="module" src="/base.mjs"></script>
	<link rel="stylesheet" href="./index.css" type="text/css" />
</head>

<body>
	<div class="container mx-auto p-4">
		<header class="mb-8">
			<h1 class="text-3xl font-bold" data-i18n="userSettings.PageTitle"></h1>
		</header>

		<main class="space-y-8">
			<!-- 用户信息 -->
			<section class="card bg-base-200 shadow-xl">
				<div class="card-body">
					<h2 class="card-title" data-i18n="userSettings.userInfo.title"></h2>
					<div class="space-y-2">
						<p><strong data-i18n="userSettings.userInfo.usernameLabel"></strong> <span id="userInfoUsername" class="font-mono"></span></p>
						<p><strong data-i18n="userSettings.userInfo.creationDateLabel"></strong> <span id="userInfoCreationDate"></span></p>
						<p><strong data-i18n="userSettings.userInfo.folderSizeLabel"></strong> <span id="userInfoFolderSize"></span></p>
						<div class="flex items-center flex-wrap">
							<strong data-i18n="userSettings.userInfo.folderPathLabel" class="mr-2"></strong>
							<code id="userInfoFolderPath" class="p-1 bg-base-300 rounded text-sm break-all flex-grow min-w-[200px]"></code>
							<button id="copyFolderPathBtn" class="btn btn-sm btn-ghost ml-2" data-i18n-title="userSettings.userInfo.copyPathBtnTitle">
								<img src="https://api.iconify.design/material-symbols/content-copy-outline.svg" class="w-4 h-4" data-i18n-alt="userSettings.userInfo.copyPathBtnTitle" />
							</button>
						</div>
					</div>
				</div>
			</section>

			<div class="grid md:grid-cols-2 gap-8">
				<!-- 修改密码 -->
				<section class="card bg-base-200 shadow-xl">
					<div class="card-body">
						<h2 class="card-title" data-i18n="userSettings.changePassword.title"></h2>
						<form id="changePasswordForm" class="space-y-4">
							<div class="form-control">
								<label class="label"><span class="label-text" data-i18n="userSettings.changePassword.currentPasswordLabel"></span></label>
								<input type="password" id="currentPassword" name="currentPassword" class="input input-bordered" required autocomplete="current-password" />
							</div>
							<div class="form-control">
								<label class="label"><span class="label-text" data-i18n="userSettings.changePassword.newPasswordLabel"></span></label>
								<input type="password" id="newPassword" name="newPassword" class="input input-bordered" required autocomplete="new-password" />
							</div>
							<div class="form-control">
								<label class="label"><span class="label-text" data-i18n="userSettings.changePassword.confirmNewPasswordLabel"></span></label>
								<input type="password" id="confirmNewPassword" name="confirmNewPassword" class="input input-bordered" required autocomplete="new-password" />
							</div>
							<div class="card-actions justify-end">
								<button type="submit" class="btn btn-primary" data-i18n="userSettings.changePassword.submitButton"></button>
							</div>
						</form>
					</div>
				</section>

				<!-- 重命名用户 -->
				<section class="card bg-base-200 shadow-xl">
					<div class="card-body">
						<h2 class="card-title" data-i18n="userSettings.renameUser.title"></h2>
						<form id="renameUserForm" class="space-y-4">
							<div class="form-control">
								<label class="label"><span class="label-text" data-i18n="userSettings.renameUser.newUsernameLabel"></span></label>
								<input type="text" id="newUsernameRename" name="newUsernameRename" class="input input-bordered" required />
							</div>
							<div class="card-actions justify-end">
								<button type="submit" class="btn btn-warning" data-i18n="userSettings.renameUser.submitButton"></button>
							</div>
						</form>
					</div>
				</section>
			</div>

			<!-- 用户设备/会话 -->
			<section class="card bg-base-200 shadow-xl">
				<div class="card-body">
					<div class="flex justify-between items-center mb-4">
						<h2 class="card-title" data-i18n="userSettings.userDevices.title"></h2>
						<button id="refreshDevicesBtn" class="btn btn-sm btn-ghost" data-i18n-title="userSettings.userDevices.refreshButtonTitle">
							<img src="https://api.iconify.design/mdi/refresh.svg" class="w-5 h-5" data-i18n-alt="userSettings.userDevices.refreshButtonTitle" />
						</button>
					</div>
					<div id="deviceListContainer" class="max-h-96 overflow-y-auto">
						<p id="noDevicesText" class="text-center hidden py-4" data-i18n="userSettings.userDevices.noDevicesFound"></p>
						<div id="deviceList" class="space-y-3"></div>
					</div>
				</div>
			</section>

			<!-- 登出 -->
			<section class="card bg-base-200 shadow-xl">
				<div class="card-body">
					<h2 class="card-title" data-i18n="userSettings.logout.title"></h2>
					<p data-i18n="userSettings.logout.description"></p>
					<div class="card-actions justify-end">
						<button id="logoutBtn" class="btn btn-accent" data-i18n="userSettings.logout.buttonText"></button>
					</div>
				</div>
			</section>

			<!-- 删除账户 -->
			<section class="card bg-error text-error-content shadow-xl">
				<div class="card-body">
					<h2 class="card-title" data-i18n="userSettings.deleteAccount.title"></h2>
					<p data-i18n="userSettings.deleteAccount.warning"></p>
					<div class="card-actions justify-end">
						<button id="deleteAccountBtn" class="btn btn-outline" data-i18n="userSettings.deleteAccount.submitButton"></button>
					</div>
				</div>
			</section>
		</main>
	</div>

	<!-- 密码确认模态框 -->
	<dialog id="passwordConfirmationModal" class="modal">
		<div class="modal-box">
			<form method="dialog"> <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button></form>
			<h3 class="font-bold text-lg" data-i18n="userSettings.passwordConfirm.title"></h3>
			<p class="py-4" data-i18n="userSettings.passwordConfirm.message"></p>
			<div class="form-control">
				<label class="label"><span class="label-text" data-i18n="userSettings.passwordConfirm.passwordLabel"></span></label>
				<input type="password" id="confirmationPassword" class="input input-bordered w-full" autocomplete="current-password" />
			</div>
			<div class="modal-action">
				<button id="confirmPasswordBtn" class="btn btn-primary" data-i18n="userSettings.passwordConfirm.confirmButton"></button>
				<button id="cancelPasswordBtn" class="btn" data-i18n="userSettings.passwordConfirm.cancelButton"></button>
			</div>
		</div>
		<form method="dialog" class="modal-backdrop"><button>close</button></form>
	</dialog>

	<!-- 提示消息容器 -->
	<div id="alertContainer" class="toast toast-bottom toast-end z-[100]"></div>

	<script type="module" src="./index.mjs"></script>
</body>

</html>
