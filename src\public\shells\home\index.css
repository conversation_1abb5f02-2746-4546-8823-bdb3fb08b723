body {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	margin: 0;
}

/* Card Styles */
.card-container {
	display: flex;
	overflow: hidden;
	transition: width 0.3s ease;
	width: 300px;
	max-height: 300px;
	border: 2px solid transparent;
	/*  for highlighting */
}

.card-content {
	width: 300px;
	height: 300px;
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	padding: 0.5rem;
}

.card-skeleton {
	width: 300px;
	height: 300px;
}

.text-content {
	color: var(--color-neutral-content);
	background-color: color-mix(in srgb, var(--color-neutral), transparent 50%);
	padding-top: 0.5rem;
}

.details-container {
	width: 300px;
	display: none;
	position: relative;
	flex-direction: column;
	flex: 1;
	min-height: 0;
	/* Allow for scrolling */
}

.tags-container {
	max-height: 200px;
	overflow-y: auto;
}

.char-details-container {
	overflow: hidden;
}

.card-actions .flex {
	min-width: 0;
	width: 100%;
	overflow-x: auto;
}

/* Desktop Hover Effect */
@media (min-width: 1024px) {
	.card-container:hover {
		width: 600px;
	}

	.card-container:hover .details-container {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.drawer-side {
		z-index: 50;
	}
}

/* Mobile Styles */
@media (max-width: 1023px) {
	.card-container {
		width: 100%;
		flex-wrap: wrap;
	}

	.card-content {
		width: 50%;
		aspect-ratio: 1 / 1;
	}

	.details-container {
		width: 50%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.drawer-side {
		z-index: 60;
		background-color: var(--color-base-100);
	}
}

.refresh-button {
	position: absolute;
	top: 10px;
	right: 10px;
	background: none;
	border: none;
	padding: 0;
	margin: 0;
	cursor: pointer;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.card-container:hover .refresh-button {
	opacity: 1;
}

/* Highlighting Selected Item */
.selected-item {
	border-color: hsl(var(--color-primary, currentColor));
	border-width: 2px;
}

.default-checkbox {
	position: absolute;
	top: 10px;
	right: 10px;
	background: none;
	padding: 0;
	margin: 0;
	cursor: pointer;
}
