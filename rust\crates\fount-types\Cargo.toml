[package]
name = "fount-types"
version = "0.1.0"
edition = "2021"
description = "Type definitions for Fount"
license = "MIT"
authors = ["Fount Team"]
repository = "https://github.com/steve02081504/fount"

[dependencies]
# 序列化
serde = { workspace = true }
serde_json = { workspace = true }

# 时间
chrono = { workspace = true }

# 工具
uuid = { workspace = true }
indexmap = { workspace = true }

# 异步
async-trait = { workspace = true }
futures = { workspace = true }

# 错误处理
thiserror = { workspace = true }
anyhow = { workspace = true }
