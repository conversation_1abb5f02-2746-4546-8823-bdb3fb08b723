<!DOCTYPE html>
<html data-theme="dark">

<head>
	<meta charset="UTF-8">
	<meta name="darkreader-lock">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title data-i18n="access.title"></title>
	<link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
	<script type="module" src="/base.mjs"></script>
	<link rel="stylesheet" href="./index.css" type="text/css" />
</head>

<body class="flex flex-col h-screen items-center justify-center p-4">
	<div class="max-w-md w-full bg-base-200 rounded-lg shadow-lg p-6">
		<h1 class="text-2xl font-bold text-center mb-4" data-i18n="access.heading"></h1>
		<p class="text-center mb-6">
			<span data-i18n="access.instruction.sameLAN"></span>
			<br />
			<span data-i18n="access.instruction.accessthis"></span>
		</p>

		<div class="form-control w-full max-w-xs mx-auto">
			<div class="join w-full">
				<input type="text" id="accessUrl" class="input input-bordered join-item w-full text-center" readonly />
				<button class="btn btn-primary join-item" id="copyButton" data-i18n="access.copyButton"></button>
			</div>
		</div>

		<div class="mt-6 text-center">
			<div id="qrcode" class="mx-auto w-48 h-48"></div>
		</div>
	</div>
	<div id="toast" class="toast toast-center invisible fixed">
		<div class="alert alert-success">
			<span data-i18n="access.copied"></span>
		</div>
	</div>
	<script type="module" src="./index.mjs"></script>
</body>

</html>
