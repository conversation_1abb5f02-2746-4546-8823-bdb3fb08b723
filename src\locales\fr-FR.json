{"lang": "fr-FR", "fountConsole": {"server": {"start": "Démarrage du serveur", "starting": "Serveur en cours de démarrage...", "ready": "<PERSON><PERSON><PERSON>.", "usesdTime": "Temps de démarrage : ${time}s", "showUrl": {"https": "Serveur HTTPS en cours d'exécution sur ${url}", "http": "Serveur HTTP en cours d'exécution sur ${url}"}, "standingBy": "En attente..."}, "jobs": {"restartingJob": "Redémarrage de la tâche ${partname} (${parttype}) pour l'utilisateur ${username} : ${uid}"}, "ipc": {"sendCommandFailed": "Échec de l'envoi de la commande : ${error}", "invalidCommand": "Commande invalide. Veuillez utiliser \"fount runshell <nom_utilisateur> <nom_shell> <paramètres...>\"", "runShellLog": "Exécution du shell ${shellname} en tant que ${username}, paramètres : ${args}", "invokeShellLog": "Appel du shell ${shellname} en tant que ${username}, paramètres : ${invokedata}", "unsupportedCommand": "Type de commande non pris en charge.", "processMessageError": "Erreur lors du traitement du message IPC : ${error}", "invalidCommandFormat": "Format de commande invalide.", "socketError": "Erreur de socket : ${error}", "instanceRunning": "Une autre instance est déjà en cours d'exécution.", "serverStartPrefix": "Démarrage du serveur", "serverStarted": "Serveur IPC démarré.", "parseResponseFailed": "Échec de l'analyse de la réponse du serveur : ${error}", "cannotParseResponse": "Impossible d'analyser la réponse du serveur.", "unknownError": "<PERSON><PERSON><PERSON> inconnue."}, "partManager": {"git": {"noUpstream": "Aucune branche de suivi configurée pour la branche '${currentBranch}', vérification des mises à jour ignorée.", "dirtyWorkingDirectory": "Le répertoire de travail n'est pas propre. Veuillez faire un 'stash' ou 'commit' de vos modifications avant de mettre à jour.", "updating": "Mise à jour depuis le dép<PERSON> distant...", "localAhead": "La branche locale est en avance sur la branche distante. Aucune mise à jour nécessaire.", "diverged": "Les branches locale et distante ont divergé. Mise à jour forcée...", "upToDate": "<PERSON><PERSON><PERSON><PERSON> à jour.", "updateFailed": "Échec de la mise à jour des composants depuis le dépôt distant : ${error}"}, "partInitTime": "${parttype} composant ${partname} temps d'initialisation : ${time}s", "partLoadTime": "${parttype} composant ${partname} temps de chargement : ${time}s"}, "web": {"requestReceived": "Requête reçue : ${method} ${url}"}, "route": {"setLanguagePreference": "L'utilisateur ${username} a défini sa langue préférée : ${preferredLanguages}"}, "auth": {"tokenVerifyError": "Erreur de vérification du jeton : ${error}", "refreshTokenError": "Erreur du jeton de rafraîchissement : ${error}", "logoutRefreshTokenProcessError": "Erreur lors du traitement du jeton de rafraîchissement de déconnexion : ${error}", "revokeTokenNoJTI": "Impossible de révoquer le jeton sans JTI.", "accountLockedLog": "Compte utilisateur ${username} verrouillé suite à de multiples tentatives de connexion infructueuses."}, "verification": {"codeGeneratedLog": "Code de vérification : ${code} (expire dans 60 secondes).", "codeNotifyTitle": "Code de vérification", "codeNotifyBody": "Code de vérification : ${code} (expire dans 60 secondes)."}, "tray": {"readIconFailed": "Erreur lors de la lecture du fichier d'icône : ${error}", "createTrayFailed": "Erreur lors de la création de l'icône dans la zone de notification : ${error}"}, "discordbot": {"botStarted": "Le personnage ${charname} s'est connecté au bot Discord ${botusername}."}, "telegrambot": {"botStarted": "Le personnage ${charname} s'est connecté au bot Telegram ${botusername}."}}, "protocolhandler": {"title": "Traitement du protocole Fount", "processing": "Traitement du protocole en cours...", "invalidProtocol": "Protocole invalide.", "insufficientParams": "Paramètres insuffisants.", "unknownCommand": "Commande inconnue.", "shellCommandSent": "Commande shell envoyée.", "shellCommandFailed": "Échec de l'envoi de la commande shell.", "shellCommandError": "Erreur lors de l'envoi de la commande shell."}, "auth": {"title": "Authentification", "subtitle": "Les données utilisateur sont stockées localement.", "usernameLabel": "Nom d'utilisateur :", "usernameInput": {"placeholder": "Saisissez votre nom d'utilisateur"}, "passwordLabel": "Mot de passe :", "passwordInput": {"placeholder": "Saisissez votre mot de passe"}, "confirmPasswordLabel": "Confirmer le mot de passe :", "confirmPasswordInput": {"placeholder": "Saisissez à nouveau votre mot de passe"}, "verificationCodeLabel": "Code de vérification :", "verificationCodeInput": {"placeholder": "Saisissez le code de vérification"}, "sendCodeButton": "Envoyer le code", "login": {"title": "Se connecter", "submitButton": "Se connecter", "toggleLink": {"text": "Pas de compte ?", "link": "S'inscrire maintenant"}}, "register": {"title": "S'inscrire", "submitButton": "S'inscrire", "toggleLink": {"text": "Vous avez déjà un compte ?", "link": "Se connecter maintenant"}}, "error": {"passwordMismatch": "Les mots de passe ne correspondent pas.", "loginError": "Erreur de connexion.", "registrationError": "E<PERSON>ur d'inscription.", "verificationCodeError": "Le code de vérification est incorrect ou a expiré.", "verificationCodeSent": "Code de vérification envoyé avec succès.", "verificationCodeSendError": "Échec de l'envoi du code de vérification.", "verificationCodeRateLimit": "Trop de demandes de code de vérification. Veuillez réessayer plus tard.", "lowPasswordStrength": "Le mot de passe est trop faible.", "accountAlreadyExists": "Ce compte existe déjà."}, "passwordStrength": {"veryWeak": "<PERSON><PERSON><PERSON> faible", "weak": "Faible", "normal": "Normal", "strong": "Fort", "veryStrong": "Très fort"}}, "tutorial": {"title": "Un tutoriel ?", "modal": {"title": "Bienvenue sur Fount !", "instruction": "Vou<PERSON>z-vous suivre le tutoriel d'introduction ?", "buttons": {"start": "Commence<PERSON> le <PERSON>iel", "skip": "Passer"}}, "endScreen": {"title": "Bravo ! Tu<PERSON>iel terminé !", "subtitle": "Vous maî<PERSON><PERSON>z maintenant les bases de l'application !", "endButton": "C'est parti !"}, "progressMessages": {"mouseMove": "Essayez de déplacer la souris ${mouseIcon} avec votre main.", "keyboardPress": "Appuyez sur une touche de votre clavier ${keyboardIcon}.", "mobileTouchMove": "Touchez l'écran de votre téléphone ${phoneIcon} avec un doigt, puis faites-le glisser.", "mobileClick": "Appuyez sur l'écran de votre téléphone ${phoneIcon} avec un doigt."}}, "home": {"title": "Accueil", "escapeConfirm": "Êtes-vous sûr de vouloir quitter Fount ?", "filterInput": {"placeholder": "Rechercher..."}, "sidebarTitle": "Détails", "itemDescription": "Sélectionnez un élément ici pour afficher les détails.", "noDescription": "Aucune description disponible.", "alerts": {"fetchHomeRegistryFailed": "Échec de la récupération des informations du registre d'accueil."}, "functionMenu": {"icon": {"alt": "Menu des fonctions"}}, "chars": {"tab": "Personnages", "title": "Sélection de personnage", "subtitle": "Sélectionnez un personnage, puis commencez à discuter !", "none": "<PERSON><PERSON> afficher", "card": {"refreshButton": {"alt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "noTags": "Aucune éti<PERSON>", "version": "Version", "author": "<PERSON><PERSON><PERSON>", "homepage": "Page d'accueil", "issuepage": "Page des problèmes", "defaultCheckbox": {"title": "Définir comme personnage par défaut"}}}, "worlds": {"tab": "<PERSON><PERSON>", "title": "Sélection de monde", "subtitle": "Choisissez un monde et plongez au cœur de l'action !", "none": "<PERSON><PERSON> afficher", "card": {"refreshButton": {"alt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "noTags": "Aucune éti<PERSON>", "version": "Version", "author": "<PERSON><PERSON><PERSON>", "homepage": "Page d'accueil", "issuepage": "Page des problèmes", "defaultCheckbox": {"title": "Définir comme monde par défaut"}}}, "personas": {"tab": "Personas", "title": "Sélection de persona", "subtitle": "Sélectionnez une persona et vivez une nouvelle expérience.", "none": "<PERSON><PERSON> afficher", "card": {"refreshButton": {"alt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "noTags": "Aucune éti<PERSON>", "version": "Version", "author": "<PERSON><PERSON><PERSON>", "homepage": "Page d'accueil", "issuepage": "Page des problèmes", "defaultCheckbox": {"title": "Définir comme persona par défaut"}}}}, "themeManage": {"title": "Gestion des thèmes", "instruction": "Choi<PERSON><PERSON>z un thème !", "themes": {"auto": "Automatique", "light": "<PERSON>", "dark": "Sombre", "cupcake": "Cupcake", "bumblebee": "Bumblebee", "emerald": "<PERSON><PERSON><PERSON><PERSON>", "corporate": "Corporate", "synthwave": "Synthwave", "retro": "<PERSON><PERSON><PERSON>", "cyberpunk": "Cyberpunk", "valentine": "<PERSON>", "halloween": "Halloween", "garden": "Jardin", "forest": "<PERSON><PERSON><PERSON>", "aqua": "Aqua", "lofi": "lo-fi", "pastel": "Pastel", "fantasy": "Fantaisie", "wireframe": "<PERSON><PERSON><PERSON>", "black": "Noir", "luxury": "Luxe", "dracula": "Dracula", "cmyk": "CMYK", "autumn": "Automne", "business": "Entreprise", "acid": "Acid", "lemonade": "Limonade", "night": "Nuit", "coffee": "Café", "winter": "Hiver", "dim": "<PERSON><PERSON><PERSON>", "nord": "Nordique", "sunset": "Sunset", "caramellatte": "Caramel Latte", "abyss": "Abysse", "silk": "<PERSON><PERSON>"}}, "import": {"title": "Importer", "tabs": {"fileImport": "Importer un fichier", "textImport": "Importer du texte"}, "dropArea": {"icon": {"alt": "Icône d'importation"}, "text": "Faites glisser et déposez des fichiers ici ou cliquez pour sélectionner des fichiers."}, "textArea": {"placeholder": "Saisissez le texte à importer..."}, "buttons": {"import": "Importer"}, "alerts": {"importSuccess": "Importation réussie.", "importFailed": "Échec de l'importation : ${error}", "unknownError": "<PERSON><PERSON><PERSON> inconnue."}, "errors": {"noFileSelected": "Veuillez sélectionner un fichier.", "fileImportFailed": "Échec de l'importation du fichier : ${message}", "noTextContent": "Veuillez saisir du contenu texte.", "textImportFailed": "Échec de l'importation du texte : ${message}", "unknownError": "<PERSON><PERSON><PERSON> inconnue.", "handler": "Gestionnaire", "error": "<PERSON><PERSON><PERSON>"}, "fileItem": {"removeButton": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "removeButtonIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON>"}}}, "aisource_editor": {"title": "Éditeur de source d'IA", "fileList": {"title": "Liste des sources d'IA", "addButton": {"title": "+"}}, "configTitle": "Configuration de la source d'IA", "generatorSelect": {"label": "Sélectionner un générateur", "placeholder": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner"}, "buttons": {"save": "Enregistrer", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "alerts": {"fetchFileListFailed": "Échec de la récupération de la liste des fichiers : ${error}", "fetchGeneratorListFailed": "Échec de la récupération de la liste des générateurs : ${error}", "fetchFileDataFailed": "Échec de la récupération des données du fichier : ${error}", "noFileSelectedSave": "Aucun fichier sélectionné à enregistrer.", "saveFileFailed": "Échec de l'enregistrement du fichier : ${error}", "noFileSelectedDelete": "Aucun fichier sélectionné à supprimer.", "deleteFileFailed": "Échec de la suppression du fichier : ${error}", "invalidFileName": "Le nom de fichier ne peut pas contenir les caractères suivants : / \\ : * ? \" < > |", "addFileFailed": "Échec de l'ajout du fichier : ${error}", "fetchConfigTemplateFailed": "Échec de la récupération du modèle de configuration.", "noGeneratorSelectedSave": "Veuillez sélectionner un générateur avant d'enregistrer."}, "confirm": {"unsavedChanges": "Vous avez des modifications non enregistrées. Voulez-vous annuler les modifications ?", "deleteFile": "Êtes-vous sûr de vouloir supprimer le fichier ?", "unsavedChangesBeforeUnload": "Vous avez des modifications non enregistrées. Êtes-vous sûr de vouloir quitter cette page ?"}, "prompts": {"newFileName": "Veuillez saisir un nouveau nom de fichier pour la source d'IA (sans extension) :"}, "editor": {"disabledIndicator": "Veuillez d'abord sélectionner un générateur."}}, "part_config": {"title": "Configuration des composants", "pageTitle": "Configuration des composants", "labels": {"partType": "Sélectionner le type de composant", "part": "Sélectionner le composant"}, "placeholders": {"partTypeSelect": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner", "partSelect": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner"}, "editor": {"title": "Configuration du composant", "disabledIndicator": "Ce composant ne prend pas en charge la configuration.", "buttons": {"save": "Enregistrer"}}, "errorMessage": {"icon": {"alt": "Message d'erreur"}}, "alerts": {"fetchPartTypesFailed": "Échec de la récupération des types de composants.", "fetchPartsFailed": "Échec de la récupération de la liste des composants.", "loadEditorFailed": "Échec du chargement de l'éditeur.", "saveConfigFailed": "Échec de l'enregistrement de la configuration du composant.", "unsavedChanges": "Vous avez des modifications non enregistrées. Voulez-vous annuler les modifications ?", "beforeUnload": "Vous avez des modifications non enregistrées. Êtes-vous sûr de vouloir quitter ?"}}, "uninstall": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "titleWithName": "Désinstaller ${type}/${name}", "confirmMessage": "Êtes-vous sûr de vouloir désinstaller ${type} : ${name} ?", "invalidParamsTitle": "Paramètres invalides", "infoMessage": {"icon": {"alt": "Icône d'information"}}, "errorMessage": {"icon": {"alt": "Icône d'erreur"}}, "buttons": {"confirm": "Confirmer la désinstallation", "cancel": "Annuler", "back": "Retour"}, "alerts": {"success": "${type} : ${name} désinstallé avec succès.", "failed": "Échec de la désinstallation : ${error}", "invalidParams": "Paramètres de requête non valides.", "httpError": "Erreur HTTP ! Code d'état : ${status}"}}, "chat": {"new": {"title": "Nouveau chat"}, "title": "Cha<PERSON>", "sidebar": {"world": {"icon": {"alt": "Icône Monde"}, "title": "Monde"}, "persona": {"icon": {"alt": "Icône Persona Utilisateur"}, "title": "<PERSON><PERSON>"}, "charList": {"icon": {"alt": "Icône Liste des personnages"}, "title": "Liste des personnages", "buttons": {"addChar": {"title": "Ajouter un personnage"}, "addCharIcon": {"alt": "Icône Ajouter un personnage"}}}, "noSelection": "<PERSON><PERSON>né", "noDescription": "Aucune description disponible."}, "chatArea": {"title": "Cha<PERSON>", "menuButton": {"title": "<PERSON><PERSON>"}, "menuButtonIcon": {"alt": "Icône Menu"}, "input": {"placeholder": "Saisissez un message...\\nCtrl+Entrée pour envoyer"}, "sendButton": {"title": "Envoyer"}, "sendButtonIcon": {"alt": "<PERSON><PERSON><PERSON>"}, "uploadButton": {"title": "Importer"}, "uploadButtonIcon": {"alt": "Icône Importer"}, "voiceButton": {"title": "Message vocal"}, "voiceButtonIcon": {"alt": "Icône Message vocal"}, "photoButton": {"title": "Photo"}, "photoButtonIcon": {"alt": "Icône Photo"}}, "rightSidebar": {"title": "Détails"}, "messageList": {"confirmDeleteMessage": "Supprimer ce message ?"}, "voiceRecording": {"errorAccessingMicrophone": "Échec de l'accès au microphone."}, "messageView": {"buttons": {"edit": {"title": "Modifier"}, "editIcon": {"alt": "Icône Modifier"}, "delete": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "deleteIcon": {"alt": "Icône Supprimer"}}}, "messageEdit": {"input": {"placeholder": "<PERSON><PERSON> le contenu..."}, "buttons": {"confirm": {"title": "Confirmer"}, "confirmIcon": {"alt": "Ic<PERSON> Confirmer"}, "cancel": {"title": "Annuler"}, "cancelIcon": {"alt": "Icône Annuler"}, "upload": {"title": "Importer"}, "uploadIcon": {"alt": "Icône Importer"}}}, "attachment": {"buttons": {"download": {"title": "Télécharger"}, "downloadIcon": {"alt": "Icône Télécharger"}, "delete": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "deleteIcon": {"alt": "Icône Supprimer"}}}, "charCard": {"frequencyLabel": "<PERSON><PERSON><PERSON>", "buttons": {"removeChar": {"title": "<PERSON><PERSON><PERSON> du chat"}, "removeCharIcon": {"alt": "Icône Retirer le personnage"}, "forceReply": {"title": "Forcer la réponse"}, "forceReplyIcon": {"alt": "Icône Forcer la réponse"}}}}, "chat_history": {"title": "Historique des chats", "pageTitle": "Historique des chats", "sortOptions": {"time_desc": "Date décroissante", "time_asc": "Date croissante"}, "filterInput": {"placeholder": "Rechercher..."}, "selectAll": "<PERSON><PERSON>", "buttons": {"reverseSelect": "Inverser la sélection", "deleteSelected": "Supprimer la sélection", "exportSelected": "Exporter la sélection"}, "confirmDeleteChat": "Êtes-vous sûr de vouloir supprimer l'historique des chats avec ${chars} ?", "confirmDeleteMultiChats": "Êtes-vous sûr de vouloir supprimer les ${count} historiques de chats sélectionnés ?", "alerts": {"noChatSelectedForDeletion": "<PERSON><PERSON><PERSON>z sélectionner les historiques de chats à supprimer.", "noChatSelectedForExport": "Veuillez sélectionner les historiques de chats à exporter.", "copyError": "Échec de la copie", "deleteError": "La suppression a échoué", "exportError": "Échec de l'exportation"}, "chatItemButtons": {"continue": "<PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "export": "Exporter", "delete": "<PERSON><PERSON><PERSON><PERSON>"}}, "discord_bots": {"title": "<PERSON><PERSON>", "cardTitle": "<PERSON><PERSON>", "buttons": {"newBot": "Nouveau", "deleteBot": "<PERSON><PERSON><PERSON><PERSON>"}, "configCard": {"title": "Configuration du bot", "labels": {"character": "Personnage", "apiKey": "Clé API Discord", "config": "Configuration"}, "charSelectPlaceholder": "Sélectionner un personnage", "apiKeyInput": {"placeholder": "Saisir la clé API"}, "toggleApiKeyIcon": {"alt": "Afficher/Masquer la clé API"}, "buttons": {"saveConfig": "Enregistrer la configuration", "startBot": "<PERSON><PERSON><PERSON><PERSON>", "stopBot": "<PERSON><PERSON><PERSON><PERSON>"}}, "prompts": {"newBotName": "Veuillez saisir un nouveau nom pour le bot :"}, "alerts": {"botExists": "Un bot nommé \"${botname}\" existe déjà. Veuillez utiliser un autre nom.", "unsavedChanges": "Vous avez des modifications non enregistrées. Voulez-vous annuler les modifications ?", "configSaved": "Configuration enregistrée avec succès.", "httpError": "Erreur HTTP.", "beforeUnload": "Vous avez des modifications non enregistrées. Êtes-vous sûr de vouloir quitter ?"}}, "telegram_bots": {"title": "Bots Telegram", "cardTitle": "Gestion des bots Telegram", "buttons": {"newBot": "Nouveau", "deleteBot": "<PERSON><PERSON><PERSON><PERSON>"}, "configCard": {"title": "Configuration du bot", "labels": {"character": "Personnage lié", "botToken": "Jeton du bot Telegram", "config": "Configuration"}, "charSelectPlaceholder": "Sélectionner un personnage", "botTokenInput": {"placeholder": "Saisissez le jeton du bot Telegram"}, "toggleBotTokenIcon": {"alt": "Aff<PERSON>r/Masquer le jeton du bot"}, "buttons": {"saveConfig": "Enregistrer la configuration", "startBot": "<PERSON><PERSON><PERSON><PERSON>", "stopBot": "<PERSON><PERSON><PERSON><PERSON>"}}, "prompts": {"newBotName": "Veuillez saisir un nouveau nom pour le bot :"}, "alerts": {"botExists": "Un bot nommé \"${botname}\" existe déjà. Veuillez utiliser un autre nom.", "unsavedChanges": "Vous avez des modifications non enregistrées. Voulez-vous vraiment annuler ces modifications ?", "configSaved": "La configuration a été enregistrée avec succès !", "httpError": "Erreur HTTP.", "beforeUnload": "Vous avez des modifications non enregistrées. Êtes-vous sûr de vouloir quitter cette page ?"}}, "terminal_assistant": {"title": "Assistant Terminal", "initialMessage": "Fount prend en charge le déploiement de vos personnages préférés dans votre terminal pour vous assister lors du codage !", "initialMessageLink": "Cliquez ici pour en savoir plus."}, "access": {"title": "Accéder à Fount sur d'autres appareils", "heading": "Vous voulez accéder à Fount sur d'autres appareils ?", "instruction": {"sameLAN": "Assurez-vous que l'appareil et l'hôte Fount sont sur le même réseau local.", "accessthis": "Visitez cette URL :"}, "copyButton": "Copier l'URL", "copied": "URL copiée dans le presse-papiers !"}, "proxy": {"title": "Proxy API", "heading": "Adresse du proxy API OpenAI", "instruction": "Saisissez l'adresse suivante dans toute application nécessitant le format API OpenAI pour utiliser les sources d'IA dans Fount !", "copyButton": "Co<PERSON>r l'adresse", "copied": "Adresse copiée dans le presse-papiers !"}, "404": {"title": "Page non trouvée", "pageNotFoundText": "Oups ! Il semble que vous soyez arrivé sur une page qui n'existe pas.", "homepageButton": "Retour à la page d'accueil", "MineSweeper": {"difficultyLabel": "Difficulté :", "difficultyEasy": "Facile", "difficultyMedium": "<PERSON><PERSON><PERSON>", "difficultyHard": "Difficile", "difficultyCustom": "Personnalisée", "minesLeftLabel": "Mines restantes :", "timeLabel": "Temps :", "restartButton": "Recommencer", "rowsLabel": "Lignes :", "colsLabel": "Colonnes :", "minesCountLabel": "Nombre de mines :", "winMessage": "Félicitations, vous avez gagné !", "loseMessage": "<PERSON>ie terminée, vous avez touché une mine !", "soundOn": "Son activé", "soundOff": "Son d<PERSON><PERSON><PERSON><PERSON>"}}, "userSettings": {"title": "Paramètres utilisateur", "PageTitle": "Paramètres utilisateur", "apiError": "La requête API a échoué : ${message}", "generalError": "Une erreur s'est produite : ${message}", "userInfo": {"title": "Informations utilisateur", "usernameLabel": "Nom d'utilisateur :", "creationDateLabel": "Date de création du compte :", "folderSizeLabel": "Taille des données utilisateur :", "folderPathLabel": "Chemin des données utilisateur :", "copyPathBtnTitle": "<PERSON><PERSON><PERSON> le chemin", "copiedAlert": "Chemin copié dans le presse-papiers !"}, "changePassword": {"title": "Modifier le mot de passe", "currentPasswordLabel": "Mot de passe actuel :", "newPasswordLabel": "Nouveau mot de passe :", "confirmNewPasswordLabel": "Confirmer le nouveau mot de passe :", "submitButton": "Modifier le mot de passe", "errorMismatch": "Les nouveaux mots de passe ne correspondent pas.", "success": "Mot de passe modifié avec succès."}, "renameUser": {"title": "Renommer l'utilisateur", "newUsernameLabel": "Nouveau nom d'utilisateur :", "submitButton": "Renommer l'utilisateur", "confirmMessage": "Êtes-vous sûr de vouloir changer votre nom d'utilisateur ? Cela nécessitera une nouvelle connexion.", "success": "L'utilisateur a été renommé \"${newUsername}\" avec succès. Vous allez maintenant être déconnecté."}, "userDevices": {"title": "Appareils et sessions utilisateur", "refreshButtonTitle": "Actualiser la liste", "noDevicesFound": "<PERSON><PERSON><PERSON> appareil ou session trouvé.", "deviceInfo": "ID de l'appareil : ${deviceId}", "thisDevice": "Cet appareil", "deviceDetails": "Dernière connexion : ${lastSeen} | IP : ${ipAddress} | UA : ${userAgent}", "revokeButton": "Révoquer", "revokeConfirm": "Révoquer l'accès pour cet appareil/cette session ?", "revokeSuccess": "Appareil/session révoqué avec succès."}, "logout": {"title": "Se déconnecter", "description": "Ceci déconnectera votre compte de l'appareil actuel.", "buttonText": "Se déconnecter", "confirmMessage": "Êtes-vous sûr de vouloir vous déconnecter ?", "successMessage": "Déconnexion réussie. Redirection vers la page de connexion en cours..."}, "deleteAccount": {"title": "Supprimer le compte", "warning": "Attention : Cette action supprimera définitivement votre compte et toutes les données associées. Elle est irréversible.", "submitButton": "Supprimer mon compte", "confirmMessage1": "Attention ! Êtes-vous sûr de vouloir supprimer définitivement votre compte ? Cette action est irréversible.", "confirmMessage2": "Pour confirmer la suppression, veuillez saisir votre nom d'utilisateur \"${username}\" :", "usernameMismatch": "Le nom d'utilisateur saisi ne correspond pas à l'utilisateur actuel. La suppression a été annulée.", "success": "Le compte a été supprimé avec succès. Vous allez maintenant être déconnecté."}, "passwordConfirm": {"title": "Confirmer l'opération", "message": "Pour continuer, veuillez saisir votre mot de passe actuel :", "passwordLabel": "Mot de passe :", "confirmButton": "Confirmer", "cancelButton": "Annuler"}}}