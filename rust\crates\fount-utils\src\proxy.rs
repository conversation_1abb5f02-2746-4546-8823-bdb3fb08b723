//! 代理工具
//! 对应原文件: src/scripts/proxy.mjs

use reqwest::Client;
use anyhow::Result;

/// HTTP代理客户端
pub struct ProxyClient {
    client: Client,
}

impl ProxyClient {
    pub fn new() -> Self {
        Self {
            client: Client::new(),
        }
    }
    
    pub async fn forward_request(&self, url: &str) -> Result<String> {
        let response = self.client.get(url).send().await?;
        let text = response.text().await?;
        Ok(text)
    }
}
