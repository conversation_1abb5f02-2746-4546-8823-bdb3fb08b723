name: Build and Push Container

permissions:
  contents: read
  packages: write

on:
  schedule:
    - cron: '0 8 * * *'
  workflow_dispatch:
jobs:
  build-and-push:
    runs-on: [ubuntu-latest]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub Container Registry
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: check ls -l
        run: ls -l
      - name: Build and Push Containers
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            steve02081504/fount:latest
            steve02081504/fount:${{ github.sha }}
      - name: <PERSON>gin to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and Push Containers
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ghcr.io/steve02081504/fount:latest
            ghcr.io/steve02081504/fount:${{ github.sha }}
