<!DOCTYPE html>
<html data-theme="dark">

<head>
	<meta charset="UTF-8">
	<meta name="darkreader-lock">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title data-i18n="aisource_editor.title"></title>
	<link href="https://cdn.jsdelivr.net/npm/daisyui/daisyui.css" rel="stylesheet" type="text/css" />
	<link href="/base.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser"></script>
	<script type="module" src="/base.mjs"></script>
	<link rel="stylesheet" href="./index.css" type="text/css" />
</head>

<body>
	<div class="container mx-auto p-4 flex flex-col md:flex-row min-h-screen">
		<!-- 文件列表区域 -->
		<div class="w-full md:w-1/4 mb-4 flex flex-col">
			<div class="flex justify-between items-center m-2">
				<h2 class="text-xl font-semibold" data-i18n="aisource_editor.fileList.title"></h2>
				<button id="addFileButton" class="btn btn-sm btn-circle btn-primary" data-i18n="aisource_editor.fileList.addButton.title">+</button>
			</div>
			<div id="fileList" class="file-list bg-base-100 rounded-lg flex-grow overflow-y-auto">
				<!-- 文件列表项将会动态生成 -->
			</div>
		</div>

		<!-- 编辑区域 -->
		<div class="w-full md:w-3/4 mb-4 relative">
			<h2 class="text-xl font-semibold m-2" data-i18n="aisource_editor.configTitle"></h2>
			<!-- 生成器选择 -->
			<div class="mb-4">
				<label for="generatorSelect" class="label">
					<span class="label-text" data-i18n="aisource_editor.generatorSelect.label"></span>
				</label>
				<select id="generatorSelect" class="select w-full">
					<option disabled selected data-i18n="aisource_editor.generatorSelect.placeholder"></option>
				</select>
			</div>
			<!-- JSON 编辑器容器 -->
			<div id="jsonEditor" class="jsoneditor-container relative" style="height: 400px; overflow: auto">
				<div id="disabledIndicator" class="absolute inset-0 z-10 bg-base-200 opacity-80 flex justify-center items-center rounded-lg hidden">
					<span class="text-lg text-error" data-i18n="aisource_editor.editor.disabledIndicator"></span>
				</div>
			</div>

			<div class="mt-4 flex justify-end">
				<button id="saveButton" class="btn btn-primary mr-2">
					<img id="saveStatusIcon" src="" class="w-6 h-6 mr-2 hidden" />
					<span id="saveStatusText" data-i18n="aisource_editor.buttons.save"></span>
				</button>
				<button id="deleteButton" class="btn btn-error ml-2" data-i18n="aisource_editor.buttons.delete"></button>
			</div>
		</div>
	</div>

	<script type="module" src="./index.mjs"></script>
</body>

</html>
