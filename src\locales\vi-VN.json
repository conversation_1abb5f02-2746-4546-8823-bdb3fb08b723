{"lang": "vi-VN", "fountConsole": {"server": {"start": "<PERSON>ang khởi động máy chủ.", "starting": "<PERSON><PERSON>y chủ đang khởi động...", "ready": "<PERSON><PERSON>y chủ đã sẵn sàng.", "usesdTime": "Thời gian khởi động: ${time}s", "showUrl": {"https": "Máy chủ HTTPS đang chạy tại ${url}", "http": "Máy chủ HTTP đang chạy tại ${url}"}, "standingBy": "<PERSON><PERSON> chờ..."}, "jobs": {"restartingJob": "<PERSON><PERSON> khởi động lại tác vụ ${partname} (${parttype}) của người dùng ${username}: ${uid}."}, "ipc": {"sendCommandFailed": "<PERSON><PERSON><PERSON> lệnh thất bại: ${error}", "invalidCommand": "<PERSON><PERSON><PERSON> k<PERSON>, vui lòng sử dụng \"fount runshell <tên_người_dùng> <tên_shell> <tham_số...>\"", "runShellLog": "Chạy shell ${shellname} với tư cách ${username}, tham số: ${args}", "invokeShellLog": "Gọi shell ${shellname} với tư cách ${username}, tham số: ${invokedata}", "unsupportedCommand": "<PERSON><PERSON><PERSON> lệnh không được hỗ trợ.", "processMessageError": "<PERSON><PERSON> xảy ra lỗi khi xử lý tin nhắn IPC: ${error}", "invalidCommandFormat": "<PERSON><PERSON><PERSON> dạng lệnh không hợp lệ.", "socketError": "Lỗi Socket: ${error}", "instanceRunning": "<PERSON><PERSON><PERSON> tiến trình khác đang chạy.", "serverStartPrefix": "Khởi động máy chủ", "serverStarted": "M<PERSON>y chủ IPC đã khởi động.", "parseResponseFailed": "<PERSON><PERSON> tích phản hồi từ máy chủ thất bại: ${error}", "cannotParseResponse": "<PERSON><PERSON><PERSON><PERSON> thể phân tích phản hồi từ máy chủ.", "unknownError": "Lỗi không xác định."}, "partManager": {"git": {"noUpstream": "<PERSON><PERSON><PERSON><PERSON> có nh<PERSON>h upstream nào được cấu hình cho nhánh '${currentBranch}', bỏ qua kiểm tra cập nhật.", "dirtyWorkingDirectory": "<PERSON><PERSON><PERSON> mục làm việc chưa sạch. <PERSON><PERSON> l<PERSON> 'stash' hoặc 'commit' các thay đổi của bạn trước khi cập nhật.", "updating": "<PERSON><PERSON> cập nhật từ kho lưu trữ từ xa...", "localAhead": "<PERSON><PERSON><PERSON><PERSON> cục bộ đang ở phiên bản mới hơn nhánh từ xa. <PERSON><PERSON><PERSON><PERSON> cần cập nhật.", "diverged": "<PERSON><PERSON><PERSON><PERSON> cục bộ và nhánh từ xa đã khác nhau. <PERSON><PERSON> bu<PERSON><PERSON> cập nhật...", "upToDate": "<PERSON><PERSON> là phiên bản mới nhất.", "updateFailed": "<PERSON><PERSON><PERSON> nhật thành phần từ kho lưu trữ từ xa thất bại: ${error}"}, "partInitTime": "Th<PERSON>i gian khởi tạo thành phần ${parttype} ${partname}: ${time}s", "partLoadTime": "Th<PERSON>i gian tải thành phần ${parttype} ${partname}: ${time}s"}, "web": {"requestReceived": "<PERSON><PERSON> nhận đ<PERSON><PERSON><PERSON> yêu cầu: ${method} ${url}"}, "route": {"setLanguagePreference": "Người dùng ${username} đã đặt ngôn ngữ ưu tiên: ${preferredLanguages}"}, "auth": {"tokenVerifyError": "Lỗi xác minh mã thông báo: ${error}", "refreshTokenError": "Lỗi làm mới mã thông báo: ${error}", "logoutRefreshTokenProcessError": "Lỗi xử lý mã thông báo làm mới khi đăng xuất: ${error}", "revokeTokenNoJTI": "<PERSON><PERSON><PERSON>ng thể thu hồi mã thông báo không có JTI.", "accountLockedLog": "T<PERSON><PERSON> khoản của người dùng ${username} đã bị khóa do nhiều lần đăng nhập thất bại."}, "verification": {"codeGeneratedLog": "Mã xác minh: ${code} (hết hạn sau 60 giây).", "codeNotifyTitle": "<PERSON><PERSON> x<PERSON>c <PERSON>h", "codeNotifyBody": "Mã xác minh: ${code} (hết hạn sau 60 giây)."}, "tray": {"readIconFailed": "<PERSON><PERSON><PERSON> tệp biểu tượng thất bại: ${error}", "createTrayFailed": "<PERSON><PERSON><PERSON> biểu tượng trên khay hệ thống thất bại: ${error}"}, "discordbot": {"botStarted": "Nhân vật ${charname} đã đăng nhập vào bot Discord ${botusername}."}, "telegrambot": {"botStarted": "Nhân vật ${charname} đã đăng nhập vào bot Telegram ${botusername}."}}, "protocolhandler": {"title": "<PERSON><PERSON> lý giao thứ<PERSON>nt", "processing": "<PERSON><PERSON> xử lý giao thức...", "invalidProtocol": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> kh<PERSON>ng h<PERSON>p l<PERSON>.", "insufficientParams": "<PERSON><PERSON><PERSON><PERSON> đủ tham số.", "unknownCommand": "<PERSON><PERSON><PERSON> không x<PERSON>c đ<PERSON>nh.", "shellCommandSent": "Lệnh Shell đã đư<PERSON><PERSON> g<PERSON>.", "shellCommandFailed": "<PERSON><PERSON><PERSON> l<PERSON>nh <PERSON> thất bại.", "shellCommandError": "<PERSON><PERSON> x<PERSON>y ra lỗi khi gửi l<PERSON>nh <PERSON>."}, "auth": {"title": "<PERSON><PERSON><PERSON> th<PERSON>c", "subtitle": "<PERSON><PERSON> liệu người dùng đ<PERSON><PERSON><PERSON> lưu trữ cục bộ.", "usernameLabel": "<PERSON><PERSON><PERSON><PERSON> dùng:", "usernameInput": {"placeholder": "<PERSON><PERSON> lòng nhập tên người dùng"}, "passwordLabel": "<PERSON><PERSON><PERSON>:", "passwordInput": {"placeholder": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u"}, "confirmPasswordLabel": "<PERSON><PERSON><PERSON> nh<PERSON>n mật kh<PERSON>u:", "confirmPasswordInput": {"placeholder": "<PERSON><PERSON> lòng nhập lại mật khẩu"}, "verificationCodeLabel": "<PERSON><PERSON> xác minh:", "verificationCodeInput": {"placeholder": "<PERSON><PERSON> lòng nhập mã x<PERSON>c minh"}, "sendCodeButton": "<PERSON><PERSON><PERSON> mã", "login": {"title": "<PERSON><PERSON><PERSON>", "submitButton": "<PERSON><PERSON><PERSON>", "toggleLink": {"text": "Chưa có tài k<PERSON>n?", "link": "<PERSON><PERSON><PERSON> ký ngay"}}, "register": {"title": "<PERSON><PERSON><PERSON> ký", "submitButton": "<PERSON><PERSON><PERSON> ký", "toggleLink": {"text": "Đã có tài k<PERSON>n?", "link": "<PERSON><PERSON><PERSON> nh<PERSON> ngay"}}, "error": {"passwordMismatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp.", "loginError": "Lỗi đăng nhập.", "registrationError": "Lỗi đăng ký.", "verificationCodeError": "<PERSON>ã xác minh không chính xác hoặc đã hết hạn.", "verificationCodeSent": "<PERSON><PERSON> xác minh đã đư<PERSON>c gửi thành công.", "verificationCodeSendError": "<PERSON><PERSON><PERSON> mã xác minh thất bại.", "verificationCodeRateLimit": "Bạn đã yêu cầu gửi mã xác minh quá nhiều lần. <PERSON><PERSON> lòng thử lại sau.", "lowPasswordStrength": "<PERSON><PERSON><PERSON> kh<PERSON>u quá yếu.", "accountAlreadyExists": "<PERSON><PERSON><PERSON> khoản đã tồn tại."}, "passwordStrength": {"veryWeak": "<PERSON><PERSON><PERSON>", "weak": "<PERSON><PERSON><PERSON>", "normal": "<PERSON>rung bình", "strong": "Mạnh", "veryStrong": "<PERSON><PERSON><PERSON>"}}, "tutorial": {"title": "Thử hướng dẫn nhé?", "modal": {"title": "Chào mừng bạn đến với Fount!", "instruction": "Bạn có muốn xem hướng dẫn cho người mới bắt đầu không?", "buttons": {"start": "<PERSON><PERSON><PERSON> đầu hướng dẫn", "skip": "Bỏ qua"}}, "endScreen": {"title": "Tuyệt vời! Hướng dẫn đã hoàn tất!", "subtitle": "B<PERSON>y giờ bạn đã biết cách thao tác rồi!", "endButton": "<PERSON><PERSON>t đầu nào!"}, "progressMessages": {"mouseMove": "Vui lòng dùng tay giữ chuột ${mouseIcon} và di chuyển.", "keyboardPress": "Vui lòng tìm bàn phím ${keyboardIcon} của bạn<br/>và thử nhấn một phím bất kỳ.", "mobileTouchMove": "<PERSON>ui lòng dùng ngón tay chạm vào màn hình điện thoại ${phoneIcon} r<PERSON><PERSON> vuốt.", "mobileClick": "<PERSON>ui lòng dùng ngón tay chạm vào màn hình điện thoại ${phoneIcon}."}}, "home": {"title": "Trang chủ", "escapeConfirm": "Bạn có chắc chắn muốn tho<PERSON>t Fount không?", "filterInput": {"placeholder": "<PERSON><PERSON><PERSON> k<PERSON>..."}, "sidebarTitle": "<PERSON> ti<PERSON>", "itemDescription": "<PERSON><PERSON><PERSON> một mục ở đây để xem chi tiết.", "noDescription": "<PERSON><PERSON><PERSON><PERSON> có mô tả.", "alerts": {"fetchHomeRegistryFailed": "<PERSON><PERSON><PERSON> thông tin đăng ký trang chủ thất bại."}, "functionMenu": {"icon": {"alt": "<PERSON><PERSON> n<PERSON>ng"}}, "chars": {"tab": "Nhân vật", "title": "<PERSON><PERSON><PERSON> nhân vật", "subtitle": "<PERSON>ọn một nhân vật - r<PERSON>i bắt đầu trò chuyện!", "none": "<PERSON><PERSON><PERSON><PERSON> có gì để hiển thị", "card": {"refreshButton": {"alt": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>"}, "noTags": "<PERSON><PERSON><PERSON>ng có thẻ", "version": "<PERSON><PERSON><PERSON>", "author": "Tác g<PERSON>", "homepage": "Trang chủ", "issuepage": "Báo cáo sự cố", "defaultCheckbox": {"title": "Đặt làm nhân vật mặc định"}}}, "worlds": {"tab": "<PERSON><PERSON><PERSON> giới", "title": "<PERSON><PERSON><PERSON> thế giới", "subtitle": "<PERSON><PERSON><PERSON> một thế giới và đắm chìm trong đó!", "none": "<PERSON><PERSON><PERSON><PERSON> có gì để hiển thị", "card": {"refreshButton": {"alt": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>"}, "noTags": "<PERSON><PERSON><PERSON>ng có thẻ", "version": "<PERSON><PERSON><PERSON>", "author": "Tác g<PERSON>", "homepage": "Trang chủ", "issuepage": "Báo cáo sự cố", "defaultCheckbox": {"title": "Đặt làm thế giới mặc định"}}}, "personas": {"tab": "<PERSON>a", "title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> một Persona và trải nghiệm cuộc sống.", "none": "<PERSON><PERSON><PERSON><PERSON> có gì để hiển thị", "card": {"refreshButton": {"alt": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>"}, "noTags": "<PERSON><PERSON><PERSON>ng có thẻ", "version": "<PERSON><PERSON><PERSON>", "author": "Tác g<PERSON>", "homepage": "Trang chủ", "issuepage": "Báo cáo sự cố", "defaultCheckbox": {"title": "Đặt làm Persona mặc định"}}}}, "themeManage": {"title": "<PERSON><PERSON><PERSON><PERSON> lý chủ đề", "instruction": "<PERSON><PERSON><PERSON> một chủ đề!", "themes": {"auto": "<PERSON><PERSON> động", "light": "<PERSON><PERSON><PERSON>", "dark": "<PERSON><PERSON><PERSON>", "cupcake": "Cupcake", "bumblebee": "Bumblebee", "emerald": "<PERSON><PERSON><PERSON> b<PERSON>", "corporate": "Corporate", "synthwave": "Synthwave", "retro": "Retro", "cyberpunk": "Cyberpunk", "valentine": "<PERSON><PERSON><PERSON> l<PERSON> tình nhân", "halloween": "Halloween", "garden": "Vườn", "forest": "R<PERSON><PERSON>", "aqua": "Aqua", "lofi": "LO-FI", "pastel": "Pastel", "fantasy": "<PERSON><PERSON><PERSON><PERSON>", "wireframe": "<PERSON><PERSON><PERSON>", "black": "<PERSON><PERSON>", "luxury": "Luxury", "dracula": "Dracula", "cmyk": "CMYK", "autumn": "<PERSON>hu", "business": "<PERSON><PERSON><PERSON><PERSON> <PERSON>h do<PERSON>h", "acid": "Acid", "lemonade": "Limonada", "night": "<PERSON><PERSON><PERSON>", "coffee": "<PERSON>à phê", "winter": "<PERSON><PERSON><PERSON>", "dim": "Mờ", "nord": "Nordic", "sunset": "<PERSON><PERSON><PERSON> hôn", "caramellatte": "Caramel Latte", "abyss": "Vực thẳm", "silk": "Lụa"}}, "import": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tabs": {"fileImport": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>p", "textImport": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> bản"}, "dropArea": {"icon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tư<PERSON> tải lên"}, "text": "<PERSON><PERSON><PERSON> và thả tệp vào đây hoặc nhấp để chọn tệp."}, "textArea": {"placeholder": "<PERSON><PERSON><PERSON><PERSON> văn bản cần nhập..."}, "buttons": {"import": "<PERSON><PERSON><PERSON><PERSON>"}, "alerts": {"importSuccess": "<PERSON><PERSON><PERSON><PERSON> thành công.", "importFailed": "<PERSON><PERSON><PERSON><PERSON> thất bại: ${error}", "unknownError": "Lỗi không xác định."}, "errors": {"noFileSelected": "<PERSON><PERSON> lòng chọn một tệp.", "fileImportFailed": "<PERSON><PERSON><PERSON><PERSON> tệp thất bại: ${message}", "noTextContent": "<PERSON><PERSON> lòng nhập nội dung văn bản.", "textImportFailed": "<PERSON><PERSON><PERSON><PERSON> văn bản thất bại: ${message}", "unknownError": "Lỗi không xác định.", "handler": "<PERSON><PERSON><PERSON><PERSON> lý", "error": "Lỗi"}, "fileItem": {"removeButton": {"title": "Xóa"}, "removeButtonIcon": {"alt": "xóa"}}}, "aisource_editor": {"title": "<PERSON><PERSON><PERSON><PERSON> chỉnh sửa nguồn AI", "fileList": {"title": "<PERSON><PERSON> ng<PERSON>", "addButton": {"title": "+"}}, "configTitle": "<PERSON><PERSON><PERSON> h<PERSON>nh ng<PERSON>", "generatorSelect": {"label": "<PERSON><PERSON><PERSON> trình tạo", "placeholder": "<PERSON><PERSON> lòng ch<PERSON>n"}, "buttons": {"save": "<PERSON><PERSON><PERSON>", "delete": "Xóa"}, "alerts": {"fetchFileListFailed": "<PERSON><PERSON><PERSON> danh sách tệp thất bại: ${error}", "fetchGeneratorListFailed": "<PERSON><PERSON><PERSON> danh sách trình tạo thất bại: ${error}", "fetchFileDataFailed": "<PERSON><PERSON><PERSON> dữ liệu tệp thất bại: ${error}", "noFileSelectedSave": "<PERSON><PERSON><PERSON> chọn tệp để lư<PERSON>.", "saveFileFailed": "<PERSON><PERSON><PERSON> tệp thất bại: ${error}", "noFileSelectedDelete": "<PERSON><PERSON><PERSON> chọn tệp để xóa.", "deleteFileFailed": "<PERSON><PERSON><PERSON> tệp thất bại: ${error}", "invalidFileName": "<PERSON><PERSON><PERSON> tệ<PERSON> không đ<PERSON><PERSON><PERSON> chứa các ký tự sau: / \\ : * ? \" < > |", "addFileFailed": "<PERSON><PERSON><PERSON><PERSON> tệp thất bại: ${error}", "fetchConfigTemplateFailed": "<PERSON><PERSON><PERSON> mẫu cấu hình thất bại.", "noGeneratorSelectedSave": "<PERSON><PERSON> lòng chọn một trình tạo trư<PERSON><PERSON> khi lưu."}, "confirm": {"unsavedChanges": "Bạn có những thay đổi chưa đư<PERSON><PERSON> lư<PERSON>. Bạn có muốn hủy bỏ những thay đổi này không?", "deleteFile": "Bạn có chắc chắn muốn xóa tệp này không?", "unsavedChangesBeforeUnload": "Bạn có những thay đổi chưa đư<PERSON><PERSON> lư<PERSON>. Bạn có chắc chắn muốn rời khỏi trang này không?"}, "prompts": {"newFileName": "<PERSON><PERSON> lòng nhập tên tệp nguồn AI mới (không bao gồm phần mở rộng):"}, "editor": {"disabledIndicator": "<PERSON><PERSON> lòng chọn một trình tạo trước."}}, "part_config": {"title": "<PERSON><PERSON><PERSON> hình thành phần", "pageTitle": "<PERSON><PERSON><PERSON> hình thành phần", "labels": {"partType": "<PERSON><PERSON><PERSON> lo<PERSON>i thành phần", "part": "<PERSON><PERSON><PERSON> thành phần"}, "placeholders": {"partTypeSelect": "<PERSON><PERSON> lòng ch<PERSON>n", "partSelect": "<PERSON><PERSON> lòng ch<PERSON>n"}, "editor": {"title": "<PERSON><PERSON><PERSON> hình thành phần", "disabledIndicator": "<PERSON>h<PERSON><PERSON> phần này không hỗ trợ cấu hình.", "buttons": {"save": "<PERSON><PERSON><PERSON>"}}, "errorMessage": {"icon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tượng lỗi"}}, "alerts": {"fetchPartTypesFailed": "<PERSON><PERSON><PERSON> lo<PERSON>i thành phần thất bại.", "fetchPartsFailed": "<PERSON><PERSON><PERSON> danh sách thành phần thất bại.", "loadEditorFailed": "<PERSON><PERSON><PERSON> trình chỉnh sửa thất bại.", "saveConfigFailed": "<PERSON><PERSON><PERSON> cấu hình thành phần thất bại.", "unsavedChanges": "Bạn có những thay đổi chưa đư<PERSON><PERSON> lư<PERSON>. Bạn có muốn hủy bỏ những thay đổi này không?", "beforeUnload": "Bạn có những thay đổi chưa đ<PERSON><PERSON><PERSON> lư<PERSON>. Bạn có chắc chắn muốn rời đi không?"}}, "uninstall": {"title": "Gỡ cài đặt", "titleWithName": "Gỡ cài đặt ${type}/${name}", "confirmMessage": "Bạn có chắc chắn muốn gỡ cài đặt ${type}: ${name} không?", "invalidParamsTitle": "<PERSON><PERSON> số không hợp lệ", "infoMessage": {"icon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tư<PERSON>ng thông tin"}}, "errorMessage": {"icon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tượng lỗi"}}, "buttons": {"confirm": "<PERSON><PERSON><PERSON> nhận gỡ cài đặt", "cancel": "<PERSON><PERSON><PERSON>", "back": "Quay lại"}, "alerts": {"success": "Đã gỡ cài đặt thành công ${type}: ${name}.", "failed": "Gỡ cài đặt thất bại: ${error}", "invalidParams": "<PERSON><PERSON> số yêu c<PERSON><PERSON> không hợp lệ.", "httpError": "Lỗi HTTP! Mã trạng thái: ${status}"}}, "chat": {"new": {"title": "<PERSON><PERSON><PERSON> ch<PERSON>n mới"}, "title": "<PERSON><PERSON><PERSON>", "sidebar": {"world": {"icon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tượng thế giới"}, "title": "<PERSON><PERSON><PERSON> giới"}, "persona": {"icon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tượng <PERSON>a người dùng"}, "title": "Persona ng<PERSON><PERSON>i dùng"}, "charList": {"icon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tư<PERSON> danh sách nhân vật"}, "title": "<PERSON><PERSON> s<PERSON>ch nh<PERSON> vật", "buttons": {"addChar": {"title": "<PERSON><PERSON><PERSON><PERSON> nhân vật"}, "addCharIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tượ<PERSON> thêm nhân vật"}}}, "noSelection": "<PERSON><PERSON><PERSON>", "noDescription": "<PERSON><PERSON><PERSON><PERSON> có mô tả."}, "chatArea": {"title": "<PERSON><PERSON><PERSON>", "menuButton": {"title": "<PERSON><PERSON>"}, "menuButtonIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> menu"}, "input": {"placeholder": "Nhập tin nhắn...\\nCtrl+Enter để gửi"}, "sendButton": {"title": "<PERSON><PERSON><PERSON>"}, "sendButtonIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>i"}, "uploadButton": {"title": "<PERSON><PERSON><PERSON>"}, "uploadButtonIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tư<PERSON> tải lên"}, "voiceButton": {"title": "<PERSON><PERSON> <PERSON><PERSON>"}, "voiceButtonIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> ghi <PERSON>"}, "photoButton": {"title": "Ảnh"}, "photoButtonIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON>"}}, "rightSidebar": {"title": "<PERSON> ti<PERSON>"}, "messageList": {"confirmDeleteMessage": "Bạn có chắc chắn muốn xóa tin nhắn này không?"}, "voiceRecording": {"errorAccessingMicrophone": "<PERSON><PERSON><PERSON><PERSON> thể truy cập micrô."}, "messageView": {"buttons": {"edit": {"title": "Chỉnh sửa"}, "editIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tượng chỉnh sửa"}, "delete": {"title": "Xóa"}, "deleteIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> x<PERSON>a"}}}, "messageEdit": {"input": {"placeholder": "<PERSON><PERSON><PERSON><PERSON> nội dung..."}, "buttons": {"confirm": {"title": "<PERSON><PERSON><PERSON>"}, "confirmIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> x<PERSON>n"}, "cancel": {"title": "<PERSON><PERSON><PERSON>"}, "cancelIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> h<PERSON>"}, "upload": {"title": "<PERSON><PERSON><PERSON>"}, "uploadIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tư<PERSON> tải lên"}}}, "attachment": {"buttons": {"download": {"title": "<PERSON><PERSON><PERSON>"}, "downloadIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tượ<PERSON> tải xuống"}, "delete": {"title": "Xóa"}, "deleteIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> x<PERSON>a"}}}, "charCard": {"frequencyLabel": "<PERSON><PERSON><PERSON>", "buttons": {"removeChar": {"title": "Xóa khỏi cuộc trò chuyện"}, "removeCharIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tượng xóa nhân vật"}, "forceReply": {"title": "<PERSON><PERSON><PERSON><PERSON> trả lời"}, "forceReplyIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON> tư<PERSON><PERSON> buộc trả lời"}}}}, "chat_history": {"title": "<PERSON><PERSON><PERSON> sử trò chuy<PERSON>n", "pageTitle": "<PERSON><PERSON><PERSON> sử trò chuy<PERSON>n", "sortOptions": {"time_desc": "<PERSON><PERSON><PERSON><PERSON> gian (mới nhất trước)", "time_asc": "<PERSON><PERSON><PERSON><PERSON> gian (c<PERSON> nhất trước)"}, "filterInput": {"placeholder": "<PERSON><PERSON><PERSON> k<PERSON>..."}, "selectAll": "<PERSON><PERSON><PERSON> tất cả", "buttons": {"reverseSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON> ch<PERSON>n", "deleteSelected": "<PERSON><PERSON><PERSON> mục đã ch<PERSON>n", "exportSelected": "<PERSON><PERSON><PERSON> mục đã ch<PERSON>n"}, "confirmDeleteChat": "Bạn có chắc chắn muốn xóa lịch sử trò chuyện với ${chars} không?", "confirmDeleteMultiChats": "Bạn có chắc chắn muốn xóa ${count} cuộc trò chuyện đã chọn không?", "alerts": {"noChatSelectedForDeletion": "<PERSON><PERSON> lòng chọn lịch sử trò chuyện cần xóa.", "noChatSelectedForExport": "<PERSON><PERSON> lòng chọn lịch sử trò chuyện cần xuất.", "copyError": "<PERSON><PERSON> chép không thành công", "deleteError": "<PERSON><PERSON><PERSON> không thành công", "exportError": "<PERSON><PERSON><PERSON> kh<PERSON>u không thành công"}, "chatItemButtons": {"continue": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Sao chép", "export": "<PERSON><PERSON><PERSON>", "delete": "Xóa"}}, "discord_bots": {"title": "<PERSON><PERSON>", "cardTitle": "<PERSON><PERSON>", "buttons": {"newBot": "<PERSON><PERSON><PERSON> mới", "deleteBot": "Xóa"}, "configCard": {"title": "<PERSON><PERSON><PERSON> <PERSON>", "labels": {"character": "Nhân vật", "apiKey": "Khóa API Discord", "config": "<PERSON><PERSON><PERSON> h<PERSON>nh"}, "charSelectPlaceholder": "<PERSON><PERSON><PERSON> nhân vật", "apiKeyInput": {"placeholder": "Nhập Khóa API"}, "toggleApiKeyIcon": {"alt": "Hiện/ẩn <PERSON>"}, "buttons": {"saveConfig": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "startBot": "Khởi động", "stopBot": "Dừng"}}, "prompts": {"newBotName": "<PERSON><PERSON> lòng nhập tên <PERSON> mới:"}, "alerts": {"botExists": "<PERSON>t có tên \"${botname}\" đã tồn tại, vui lòng sử dụng tên kh<PERSON>c.", "unsavedChanges": "Bạn có những thay đổi chưa đư<PERSON><PERSON> lư<PERSON>. Bạn có muốn hủy bỏ những thay đổi này không?", "configSaved": "<PERSON><PERSON>u hình đã đư<PERSON><PERSON> lưu thành công.", "httpError": "Lỗi HTTP.", "beforeUnload": "Bạn có những thay đổi chưa đ<PERSON><PERSON><PERSON> lư<PERSON>. Bạn có chắc chắn muốn rời đi không?"}}, "telegram_bots": {"title": "Bot Telegram", "cardTitle": "Q<PERSON>ản lý Bot Telegram", "buttons": {"newBot": "<PERSON><PERSON><PERSON> mới", "deleteBot": "Xóa"}, "configCard": {"title": "<PERSON><PERSON><PERSON> <PERSON>", "labels": {"character": "<PERSON><PERSON>ân vật đã liên kết", "botToken": "Token Bot Telegram", "config": "<PERSON><PERSON><PERSON> h<PERSON>nh"}, "charSelectPlaceholder": "<PERSON><PERSON><PERSON> nhân vật", "botTokenInput": {"placeholder": "Nhập Token Bot Telegram"}, "toggleBotTokenIcon": {"alt": "Hiện/ẩn token bot"}, "buttons": {"saveConfig": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "startBot": "Khởi động", "stopBot": "Dừng"}}, "prompts": {"newBotName": "<PERSON><PERSON> lòng nhập tên <PERSON> mới:"}, "alerts": {"botExists": "<PERSON>t với tên \"${botname}\" đã tồn tại, vui lòng sử dụng tên kh<PERSON>c.", "unsavedChanges": "Bạn có những thay đổi chưa đư<PERSON><PERSON> lư<PERSON>. Bạn có chắc chắn muốn hủy bỏ những thay đổi này không?", "configSaved": "Cấu hình đã đư<PERSON>c lưu thành công!", "httpError": "Lỗi HTTP.", "beforeUnload": "Bạn có những thay đổi chưa đư<PERSON><PERSON> lư<PERSON>. Bạn có chắc chắn muốn rời khỏi trang này không?"}}, "terminal_assistant": {"title": "Trợ lý Terminal", "initialMessage": "Fount hỗ trợ triển khai các nhân vật yêu thích của bạn vào terminal để trợ giúp bạn trong quá trình lập trình!", "initialMessageLink": "<PERSON><PERSON><PERSON><PERSON> vào đây để tìm hiểu thêm."}, "access": {"title": "<PERSON><PERSON><PERSON> c<PERSON><PERSON>nt trên các thiết bị kh<PERSON>c", "heading": "Bạn muốn truy cập Fount trên các thiết bị kh<PERSON>c?", "instruction": {"sameLAN": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> thiết bị và máy chủ Fount đang ở trên cùng một mạng LAN.", "accessthis": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> địa chỉ sau:"}, "copyButton": "<PERSON>o ch<PERSON>p địa chỉ", "copied": "Địa chỉ đã được sao chép vào bộ nhớ tạm!"}, "proxy": {"title": "Proxy API", "heading": "Địa chỉ Proxy API OpenAI", "instruction": "<PERSON>hậ<PERSON> địa chỉ sau vào bất kỳ ứng dụng nào yêu cầu định dạng API OpenAI để sử dụng các nguồn AI trong Fount!", "copyButton": "<PERSON>o ch<PERSON>p địa chỉ", "copied": "Địa chỉ đã được sao chép vào bộ nhớ tạm!"}, "404": {"title": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang", "pageNotFoundText": "Ối! Có vẻ như bạn đã truy cập vào một trang không tồn tại.", "homepageButton": "Quay lại trang chủ", "MineSweeper": {"difficultyLabel": "<PERSON><PERSON> khó:", "difficultyEasy": "Dễ", "difficultyMedium": "<PERSON>rung bình", "difficultyHard": "<PERSON><PERSON><PERSON>", "difficultyCustom": "<PERSON><PERSON><PERSON> chỉnh", "minesLeftLabel": "Số mìn còn lại:", "timeLabel": "<PERSON>h<PERSON><PERSON> gian:", "restartButton": "<PERSON><PERSON><PERSON> lại", "rowsLabel": "Số hàng:", "colsLabel": "Số cột:", "minesCountLabel": "Số mìn:", "winMessage": "<PERSON><PERSON><PERSON> mừ<PERSON>, bạn đã thắng!", "loseMessage": "<PERSON><PERSON><PERSON> đã thua, h<PERSON><PERSON> thử lại nhé!", "soundOn": "<PERSON><PERSON><PERSON>", "soundOff": "<PERSON><PERSON><PERSON> â<PERSON>h"}}, "userSettings": {"title": "Cài đặt người dùng", "PageTitle": "Cài đặt người dùng", "apiError": "<PERSON><PERSON><PERSON> cầu API thất bại: ${message}", "generalError": "Đã có lỗi xảy ra: ${message}", "userInfo": {"title": "Thông tin người dùng", "usernameLabel": "<PERSON><PERSON><PERSON><PERSON> dùng:", "creationDateLabel": "<PERSON><PERSON><PERSON> t<PERSON>o tà<PERSON>:", "folderSizeLabel": "<PERSON><PERSON><PERSON> thư<PERSON> dữ liệu người dùng:", "folderPathLabel": "Đường dẫn dữ liệu người dùng:", "copyPathBtnTitle": "<PERSON>o chép đường dẫn", "copiedAlert": "Đường dẫn đã được sao chép vào bộ nhớ tạm!"}, "changePassword": {"title": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "currentPasswordLabel": "<PERSON><PERSON><PERSON> <PERSON><PERSON> hiện tại:", "newPasswordLabel": "<PERSON><PERSON><PERSON> k<PERSON>u mới:", "confirmNewPasswordLabel": "<PERSON><PERSON><PERSON> nhận mật khẩu mới:", "submitButton": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "errorMismatch": "<PERSON><PERSON><PERSON> kh<PERSON>u mới không khớp.", "success": "<PERSON><PERSON><PERSON> mật kh<PERSON>u thành công."}, "renameUser": {"title": "<PERSON><PERSON><PERSON> tên người dùng", "newUsernameLabel": "<PERSON><PERSON>n ng<PERSON>ời dùng mới:", "submitButton": "<PERSON><PERSON><PERSON> tên người dùng", "confirmMessage": "Bạn có chắc chắn muốn đổi tên người dùng không? <PERSON><PERSON> khi đổi, bạn sẽ cần đăng nhập lại.", "success": "Tên người dùng đã được đổi thành \"${newUsername}\" thành công. B<PERSON>y giờ bạn sẽ bị đăng xuất."}, "userDevices": {"title": "<PERSON><PERSON><PERSON><PERSON> bị/<PERSON>ên hoạt động", "refreshButtonTitle": "<PERSON><PERSON><PERSON> mới danh s<PERSON>ch", "noDevicesFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thiết bị hoặc phiên hoạt động nào.", "deviceInfo": "ID thiết bị: ${deviceId}", "thisDevice": "<PERSON><PERSON><PERSON><PERSON> bị <PERSON>y", "deviceDetails": "<PERSON><PERSON><PERSON> động lần cuối: ${lastSeen} | IP: ${ipAddress} | UA: ${userAgent}", "revokeButton": "<PERSON><PERSON> h<PERSON>", "revokeConfirm": "Bạn có chắc chắn muốn thu hồi quyền truy cập cho thiết bị/phiên này không?", "revokeSuccess": "<PERSON><PERSON><PERSON><PERSON> bị/<PERSON><PERSON><PERSON> hoạt động đã đư<PERSON><PERSON> thu hồi thành công."}, "logout": {"title": "<PERSON><PERSON><PERSON> xu<PERSON>", "description": "<PERSON><PERSON> tác này sẽ đăng xuất tài khoản của bạn khỏi thiết bị hiện tại.", "buttonText": "<PERSON><PERSON><PERSON> xu<PERSON>", "confirmMessage": "Bạn có chắc chắn muốn đăng xuất không?", "successMessage": "<PERSON><PERSON>ng xuất thành công. Bạn sẽ được chuyển đến trang đăng nhập sau giây lát..."}, "deleteAccount": {"title": "<PERSON><PERSON><PERSON> t<PERSON>", "warning": "Cảnh báo: <PERSON>à<PERSON> động này sẽ xóa vĩnh viễn tài khoản của bạn cùng tất cả dữ liệu liên quan và không thể khôi phục.", "submitButton": "<PERSON><PERSON><PERSON> tài k<PERSON>n của tôi", "confirmMessage1": "Cảnh báo! Bạn có chắc chắn muốn xóa vĩnh viễn tài khoản của mình không? Hành động này không thể hoàn tác.", "confirmMessage2": "<PERSON><PERSON> xác nhận vi<PERSON><PERSON> x<PERSON>, vui lòng nhập tên người dùng \"${username}\" của bạn:", "usernameMismatch": "Tên người dùng đã nhập không khớp với người dùng hiện tại. <PERSON><PERSON> tác xóa đã bị hủy.", "success": "<PERSON><PERSON><PERSON> khoản đã được xóa thành công. B<PERSON>y giờ bạn sẽ bị đăng xuất."}, "passwordConfirm": {"title": "<PERSON><PERSON><PERSON> nh<PERSON>n thao tác", "message": "<PERSON><PERSON> tiế<PERSON> t<PERSON>, vui lòng nhập mật khẩu hiện tại của bạn:", "passwordLabel": "<PERSON><PERSON><PERSON>:", "confirmButton": "<PERSON><PERSON><PERSON>", "cancelButton": "<PERSON><PERSON><PERSON>"}}}