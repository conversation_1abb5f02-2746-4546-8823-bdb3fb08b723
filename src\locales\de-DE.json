{"lang": "de-DE", "fountConsole": {"server": {"start": "Server wird gestartet", "starting": "Server startet...", "ready": "Server ist gestartet", "usesdTime": "Hochfahrzeit: ${time}s", "showUrl": {"https": "HTTPS-Server läuft auf ${url}", "http": "HTTP-Server läuft auf ${url}"}, "standingBy": "In Bereitschaft..."}, "jobs": {"restartingJob": "Starte ${parttype}-Aufgabe ${partname} für Benutzer ${username} neu: ${uid}"}, "ipc": {"sendCommandFailed": "Senden des Befehls fehlgeschlagen: ${error}", "invalidCommand": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bitte verwenden <PERSON> \"fount runshell <Benutzername> <Shell-Name> <Parameter...>\"", "runShellLog": "Führe Shell ${shellname} als ${username} aus, Parameter: ${args}", "invokeShellLog": "Rufe Shell ${shellname} als ${username} auf, Parameter: ${invokedata}", "unsupportedCommand": "Nicht unterstützter Befehlstyp", "processMessageError": "Fehler beim Verarbeiten der IPC-Nachricht: ${error}", "invalidCommandFormat": "Ungültiges Befehlsformat", "socketError": "Socket-Fehler: ${error}", "instanceRunning": "Eine andere Instanz wird bereits ausgeführt", "serverStartPrefix": "Serverstart", "serverStarted": "IPC-Server gestartet", "parseResponseFailed": "<PERSON><PERSON> be<PERSON> Serverantwort: ${error}", "cannotParseResponse": "Serverantwort kann nicht geparst werden", "unknownError": "Unbekannter Fehler"}, "partManager": {"git": {"noUpstream": "<PERSON><PERSON>stream-Branch für Branch '${currentBranch}' konfiguriert, Update-Prüfung wird übersprungen.", "dirtyWorkingDirectory": "Arbeitsverzeichnis ist nicht sauber. Bitte stashen oder committen Sie Ihre Änderungen vor dem Update.", "updating": "Wird vom Remote-Repository aktualisiert...", "localAhead": "Lokaler Branch ist dem Remote-Branch voraus. Kein Update er<PERSON><PERSON>lich.", "diverged": "Lokaler und Remote-Branch sind auseinandergelaufen. Erzwinge Update...", "upToDate": "Bereits aktuell.", "updateFailed": "Fehler beim Aktualisieren von Komponenten vom Remote-Repository: ${error}"}, "partInitTime": "${parttype}-Komponente ${partname} Initialisierungszeit: ${time}s", "partLoadTime": "${parttype}-Komponente ${partname} Ladezeit: ${time}s"}, "web": {"requestReceived": "Anfrage erhalten: ${method} ${url}"}, "route": {"setLanguagePreference": "Benutzer ${username} hat die bevorzugte Sprache eingestellt: ${preferredLanguages}"}, "auth": {"tokenVerifyError": "Token-Verifizierungsfehler: ${error}", "refreshTokenError": "Fehler beim Aktualisieren des Tokens: ${error}", "logoutRefreshTokenProcessError": "Fehler beim Verarbeiten des Logout-Refresh-Tokens: ${error}", "revokeTokenNoJTI": "Token ohne JTI kann nicht widerrufen werden.", "accountLockedLog": "Benutzerkonto ${username} wurde aufgrund zu vieler fehlgeschlagener Anmeldeversuche gesperrt."}, "verification": {"codeGeneratedLog": "Verifizierungscode: ${code} (läuft nach 60 Sekunden ab)", "codeNotifyTitle": "Verifizierungscode", "codeNotifyBody": "Verifizierungscode: ${code} (läuft nach 60 Sekunden ab)"}, "tray": {"readIconFailed": "<PERSON><PERSON> beim Lesen der Icon-Datei: ${error}", "createTrayFailed": "<PERSON><PERSON> beim Erstellen des Trays: ${error}"}, "discordbot": {"botStarted": "Char ${charname} hat sich beim Discord-Bot ${botusername} angemeldet."}, "telegrambot": {"botStarted": "Char ${charname} hat sich beim Telegram-Bot ${botusername} angemeldet."}}, "protocolhandler": {"title": "Fount-<PERSON><PERSON><PERSON> verar<PERSON>", "processing": "Protokoll wird verarbeitet...", "invalidProtocol": "Ungültiges Protokoll", "insufficientParams": "Unzureichende Parameter", "unknownCommand": "Unbekannter Befehl", "shellCommandSent": "Shell-Befehl gesendet", "shellCommandFailed": "Senden des Shell-Befehls fehlgeschlagen", "shellCommandError": "Fehler beim Senden des Shell-Befehls"}, "auth": {"title": "Authentifizierung", "subtitle": "Benutzerdaten werden lokal gespeichert", "usernameLabel": "Benutzername:", "usernameInput": {"placeholder": "Bitte Benutzernamen eingeben"}, "passwordLabel": "Passwort:", "passwordInput": {"placeholder": "Bitte Passwort eingeben"}, "confirmPasswordLabel": "Passwort bestätigen:", "confirmPasswordInput": {"placeholder": "Bitte Passwort erneut eingeben"}, "verificationCodeLabel": "Verifizierungscode:", "verificationCodeInput": {"placeholder": "Bitte Verifizierungscode eingeben"}, "sendCodeButton": "Code senden", "login": {"title": "Anmelden", "submitButton": "Anmelden", "toggleLink": {"text": "<PERSON><PERSON>?", "link": "Jetzt registrieren"}}, "register": {"title": "Registrieren", "submitButton": "Registrieren", "toggleLink": {"text": "Bereits ein Konto?", "link": "Jetzt anmelden"}}, "error": {"passwordMismatch": "Passwörter stimmen nicht überein.", "loginError": "Fehler beim An<PERSON>den.", "registrationError": "Fehler bei der Registrierung.", "verificationCodeError": "Verifizierungscode falsch oder abgelaufen.", "verificationCodeSent": "Verifizierungscode erfolgreich gesendet.", "verificationCodeSendError": "Verifizierungscode konnte nicht gesendet werden.", "verificationCodeRateLimit": "<PERSON>u viele Anfragen zum Senden des Verifizierungscodes. Bitte später erneut versuchen.", "lowPasswordStrength": "Passwort ist zu schwach.", "accountAlreadyExists": "Konto existiert bereits"}, "passwordStrength": {"veryWeak": "<PERSON><PERSON> schwach", "weak": "<PERSON><PERSON><PERSON>", "normal": "Normal", "strong": "<PERSON>", "veryStrong": "<PERSON><PERSON> <PERSON>"}}, "tutorial": {"title": "Tutorial?", "modal": {"title": "Willkommen bei Fount!", "instruction": "Möchten Sie das Einsteiger-Tutorial durchgehen?", "buttons": {"start": "Tutorial starten", "skip": "Überspringen"}}, "endScreen": {"title": "Fantastisch! Tutorial abgeschlossen!", "subtitle": "<PERSON><PERSON>t wissen Si<PERSON>, wie man es bedient!", "endButton": "<PERSON>ter!"}, "progressMessages": {"mouseMove": "Bitte bewegen Sie die Maus ${mouseIcon} mit Ihrer Hand.", "keyboardPress": "Drücken Sie eine Taste auf Ihrer Tastatur ${keyboardIcon}.", "mobileTouchMove": "Berühren Sie den Bildschirm Ihres Telefons ${phoneIcon} mit einem Finger und bewegen Sie ihn dann.", "mobileClick": "<PERSON><PERSON><PERSON> mit einem Finger auf den Bildschirm Ihres Telefons ${phoneIcon}."}}, "home": {"title": "Startseite", "escapeConfirm": "Sind <PERSON> sicher, dass Si<PERSON> Fount verlassen möchten?", "filterInput": {"placeholder": "Suchen..."}, "sidebarTitle": "Details", "itemDescription": "<PERSON><PERSON>hle ein Element aus, um Details anzuzeigen.", "noDescription": "<PERSON><PERSON>", "alerts": {"fetchHomeRegistryFailed": "Fehler beim Abrufen der Startseitenregistrierung"}, "functionMenu": {"icon": {"alt": "Funktionsmenü"}}, "chars": {"tab": "Charaktere", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Wähle einen Charakter – und starte den Chat!", "none": "Nichts vorhanden", "card": {"refreshButton": {"alt": "Aktualisieren", "title": "Aktualisieren"}, "noTags": "Keine <PERSON>s", "version": "Version", "author": "Autor", "homepage": "Homepage", "issuepage": "Problem melden", "defaultCheckbox": {"title": "Als Standardcharakter festlegen"}}}, "worlds": {"tab": "Welten", "title": "Weltenauswahl", "subtitle": "W<PERSON>hle eine Welt und tauche ein!", "none": "Nichts vorhanden", "card": {"refreshButton": {"alt": "Aktualisieren", "title": "Aktualisieren"}, "noTags": "Keine <PERSON>s", "version": "Version", "author": "Autor", "homepage": "Homepage", "issuepage": "Problem melden", "defaultCheckbox": {"title": "Als Standardwelt festlegen"}}}, "personas": {"tab": "Personas", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON>hle eine Persona und erlebe das Leben.", "none": "Nichts vorhanden", "card": {"refreshButton": {"alt": "Aktualisieren", "title": "Aktualisieren"}, "noTags": "Keine <PERSON>s", "version": "Version", "author": "Autor", "homepage": "Homepage", "issuepage": "Problem melden", "defaultCheckbox": {"title": "Als Standardpersona festlegen"}}}}, "themeManage": {"title": "Themenverwaltung", "instruction": "W<PERSON>hle ein Thema!", "themes": {"auto": "Automatisch", "light": "Hell", "dark": "<PERSON><PERSON><PERSON>", "cupcake": "Tassenkuchen", "bumblebee": "<PERSON><PERSON><PERSON>", "emerald": "Smaragd", "corporate": "Corporate", "synthwave": "Synthwave", "retro": "Retro", "cyberpunk": "Cyberpunk", "valentine": "Valentinstag", "halloween": "Halloween", "garden": "Garte<PERSON>", "forest": "<PERSON><PERSON>", "aqua": "Aqua", "lofi": "Lo-Fi", "pastel": "<PERSON><PERSON>", "fantasy": "Fantasie", "wireframe": "Wireframe", "black": "<PERSON><PERSON><PERSON>", "luxury": "Luxus", "dracula": "Dracula", "cmyk": "CMYK", "autumn": "<PERSON><PERSON>", "business": "Geschäft", "acid": "Acid", "lemonade": "Limonade", "night": "<PERSON><PERSON>", "coffee": "<PERSON><PERSON><PERSON>", "winter": "Winter", "dim": "Gedimmt", "nord": "Nord", "sunset": "Sonnenuntergang", "caramellatte": "Caramel Latte", "abyss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "silk": "<PERSON><PERSON>"}}, "import": {"title": "Importieren", "tabs": {"fileImport": "Datei importieren", "textImport": "Text importieren"}, "dropArea": {"icon": {"alt": "Upload-Symbol"}, "text": "<PERSON><PERSON> hierher ziehen oder zum Auswählen klicken"}, "textArea": {"placeholder": "Text zum Importieren eingeben..."}, "buttons": {"import": "Importieren"}, "alerts": {"importSuccess": "Import er<PERSON><PERSON>g<PERSON>ich", "importFailed": "Import fehlgeschlagen: ${error}", "unknownError": "Unbekannter Fehler"}, "errors": {"noFileSelected": "Bitte wählen Si<PERSON> eine Datei aus", "fileImportFailed": "Dateiimport fehlgeschlagen: ${message}", "noTextContent": "<PERSON>te geben Sie Textinhalt ein", "textImportFailed": "Textimport fehlgeschlagen: ${message}", "unknownError": "Unbekannter Fehler", "handler": "Handler", "error": "<PERSON><PERSON>"}, "fileItem": {"removeButton": {"title": "Entfernen"}, "removeButtonIcon": {"alt": "Entfernen"}}}, "aisource_editor": {"title": "KI-Quelleneditor", "fileList": {"title": "KI-Quellenliste", "addButton": {"title": "+"}}, "configTitle": "KI-Quellenkonfiguration", "generatorSelect": {"label": "Generator auswählen", "placeholder": "Bitte auswählen"}, "buttons": {"save": "Speichern", "delete": "Löschen"}, "alerts": {"fetchFileListFailed": "Fehler beim Abrufen der Dateiliste: ${error}", "fetchGeneratorListFailed": "Fehler beim Abrufen der Generatorliste: ${error}", "fetchFileDataFailed": "Fehler beim Abrufen der Dateidaten: ${error}", "noFileSelectedSave": "<PERSON><PERSON> Datei zum Speichern ausgewählt.", "saveFileFailed": "Fehler beim Speichern der Datei: ${error}", "noFileSelectedDelete": "<PERSON><PERSON> Datei zum Löschen ausgewählt.", "deleteFileFailed": "Fehler beim Löschen der Datei: ${error}", "invalidFileName": "Der Dateiname darf folgende Zeichen nicht enthalten: / \\ : * ? \" < > |", "addFileFailed": "Fehler beim Hinzufügen der Datei: ${error}", "fetchConfigTemplateFailed": "Fehler beim Abrufen der Konfigurationsvorlage", "noGeneratorSelectedSave": "<PERSON>te wählen Si<PERSON> zu<PERSON>t einen Generator aus, bevor <PERSON>."}, "confirm": {"unsavedChanges": "Sie haben ungespeicherte Änderungen. Möchten Sie die Änderungen verwerfen?", "deleteFile": "Sind <PERSON> sicher, dass Sie die Datei löschen möchten?", "unsavedChangesBeforeUnload": "Sie haben ungespeicherte Änderungen. Sind Si<PERSON> sicher, dass Sie diese Seite verlassen möchten?"}, "prompts": {"newFileName": "Bitte geben Si<PERSON> einen neuen KI-Quelldatei-<PERSON><PERSON> ein (ohne Erweiterung):"}, "editor": {"disabledIndicator": "Bitte wählen Sie zuerst einen Generator aus."}}, "part_config": {"title": "Komponentenkonfiguration", "pageTitle": "Komponentenkonfiguration", "labels": {"partType": "Komponententyp auswählen", "part": "Komponente auswählen"}, "placeholders": {"partTypeSelect": "Bitte auswählen", "partSelect": "Bitte auswählen"}, "editor": {"title": "Komponentenkonfiguration", "disabledIndicator": "Diese Komponente unterstützt keine Konfiguration.", "buttons": {"save": "Speichern"}}, "errorMessage": {"icon": {"alt": "Fehlerhinweis"}}, "alerts": {"fetchPartTypesFailed": "Fehler beim Abrufen der Komponententypen.", "fetchPartsFailed": "Fehler beim Abrufen der Komponentenliste.", "loadEditorFailed": "<PERSON><PERSON> beim Laden des Editors.", "saveConfigFailed": "Fehler beim Speichern der Komponentenkonfiguration.", "unsavedChanges": "Sie haben ungespeicherte Änderungen. Möchten Sie die Änderungen verwerfen?", "beforeUnload": "Sie haben ungespeicherte Änderungen. Sind Si<PERSON> sicher, dass Sie die Se<PERSON> verlassen möchten?"}}, "uninstall": {"title": "Deinstallieren", "titleWithName": "Deinstalliere ${type}/${name}", "confirmMessage": "Sind <PERSON> sic<PERSON>, dass Sie ${type}: ${name} deinstallieren möchten?", "invalidParamsTitle": "Ungültige Parameter", "infoMessage": {"icon": {"alt": "Info-Symbol"}}, "errorMessage": {"icon": {"alt": "Fehler-Symbol"}}, "buttons": {"confirm": "Deinstallation bestätigen", "cancel": "Abbrechen", "back": "Zurück"}, "alerts": {"success": "${type}: ${name} erfolgreich deinstalliert", "failed": "Deinstallation fehlgeschlagen: ${error}", "invalidParams": "Ungültige Anfrageparameter.", "httpError": "HTTP-Fehler! Statuscode: ${status}"}}, "chat": {"new": {"title": "<PERSON><PERSON><PERSON>"}, "title": "Cha<PERSON>", "sidebar": {"world": {"icon": {"alt": "Weltsymbol"}, "title": "Welt"}, "persona": {"icon": {"alt": "Benutzer-Persona-Symbol"}, "title": "Benutzer-Persona"}, "charList": {"icon": {"alt": "Charakterlistensymbol"}, "title": "Cha<PERSON><PERSON><PERSON><PERSON>", "buttons": {"addChar": {"title": "Cha<PERSON><PERSON>"}, "addCharIcon": {"alt": "Charakter hinzufügen-Symbol"}}}, "noSelection": "Nichts ausgewählt", "noDescription": "<PERSON><PERSON>"}, "chatArea": {"title": "Cha<PERSON>", "menuButton": {"title": "<PERSON><PERSON>"}, "menuButtonIcon": {"alt": "Menüsymbol"}, "input": {"placeholder": "Na<PERSON>richt eingeben...\\nStrg+Enter zum Senden"}, "sendButton": {"title": "Senden"}, "sendButtonIcon": {"alt": "Senden-Symbol"}, "uploadButton": {"title": "Hochladen"}, "uploadButtonIcon": {"alt": "Hochladen-Symbol"}, "voiceButton": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "voiceButtonIcon": {"alt": "Spracheingabe-Symbol"}, "photoButton": {"title": "Bild"}, "photoButtonIcon": {"alt": "Bild-Symbol"}}, "rightSidebar": {"title": "Beschreibung"}, "messageList": {"confirmDeleteMessage": "Na<PERSON><PERSON>t wirklich löschen?"}, "voiceRecording": {"errorAccessingMicrophone": "Fehler beim Zugriff auf das Mikrofon."}, "messageView": {"buttons": {"edit": {"title": "<PERSON><PERSON><PERSON>"}, "editIcon": {"alt": "Bearbeiten-Symbol"}, "delete": {"title": "Löschen"}, "deleteIcon": {"alt": "Löschen-Symbol"}}}, "messageEdit": {"input": {"placeholder": "Inhalt eingeben..."}, "buttons": {"confirm": {"title": "Bestätigen"}, "confirmIcon": {"alt": "Bestätigen-Symbol"}, "cancel": {"title": "Abbrechen"}, "cancelIcon": {"alt": "Abbrechen-Symbol"}, "upload": {"title": "Hochladen"}, "uploadIcon": {"alt": "Hochladen-Symbol"}}}, "attachment": {"buttons": {"download": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "downloadIcon": {"alt": "Herunterladen-Symbol"}, "delete": {"title": "Löschen"}, "deleteIcon": {"alt": "Löschen-Symbol"}}}, "charCard": {"frequencyLabel": "<PERSON><PERSON><PERSON><PERSON>", "buttons": {"removeChar": {"title": "Aus Chat entfernen"}, "removeCharIcon": {"alt": "Charakter entfernen-Symbol"}, "forceReply": {"title": "Antwort erzwingen"}, "forceReplyIcon": {"alt": "Antwort erzwingen-Symbol"}}}}, "chat_history": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sortOptions": {"time_desc": "Zeit absteigend", "time_asc": "Zeit aufsteigend"}, "filterInput": {"placeholder": "Suchen..."}, "selectAll": "Alle auswählen", "buttons": {"reverseSelect": "Auswahl umkehren", "deleteSelected": "Ausgewählte löschen", "exportSelected": "Ausgewählte exportieren"}, "confirmDeleteChat": "Möchten Sie den Chatverlauf mit ${chars} wirklich löschen?", "confirmDeleteMultiChats": "Möchten Sie die ausgewählten ${count} Chatverläufe wirklich löschen?", "alerts": {"noChatSelectedForDeletion": "Bitte wählen Sie Chatverläufe zum Löschen aus.", "noChatSelectedForExport": "Bitte wählen Sie Chatverläufe zum Exportieren aus.", "copyError": "Kopie fehlgeschlagen", "deleteError": "Löschung fehlgeschlagen", "exportError": "Export fehlgeschlagen"}, "chatItemButtons": {"continue": "Fortsetzen", "copy": "<PERSON><PERSON><PERSON>", "export": "Exportieren", "delete": "Löschen"}}, "discord_bots": {"title": "Discord-<PERSON><PERSON>", "cardTitle": "Discord-<PERSON><PERSON>", "buttons": {"newBot": "<PERSON>eu", "deleteBot": "Löschen"}, "configCard": {"title": "Bot-Konfiguration", "labels": {"character": "<PERSON><PERSON><PERSON>", "apiKey": "Discord API-Schlüssel", "config": "Konfiguration"}, "charSelectPlaceholder": "Charakter auswählen", "apiKeyInput": {"placeholder": "API-Schlüssel eingeben"}, "toggleApiKeyIcon": {"alt": "API-Schlüssel-Sichtbarkeit umschalten"}, "buttons": {"saveConfig": "Konfiguration speichern", "startBot": "Starten", "stopBot": "Stoppen"}}, "prompts": {"newBotName": "<PERSON>te gib einen neuen Bot-Namen ein:"}, "alerts": {"botExists": "Ein Bot namens \"${botname}\" existiert bereits, bitte verwende einen anderen Namen.", "unsavedChanges": "Sie haben ungespeicherte Änderungen. Möchten Sie die Änderungen verwerfen?", "configSaved": "Konfiguration erfolgreich gespeichert.", "httpError": "HTTP-<PERSON><PERSON>", "beforeUnload": "Sie haben ungespeicherte Änderungen. Sind Si<PERSON> sicher, dass Sie die Se<PERSON> verlassen möchten?"}}, "telegram_bots": {"title": "Telegram-Bots", "cardTitle": "Telegram-Bot-Verwaltung", "buttons": {"newBot": "<PERSON>eu", "deleteBot": "Löschen"}, "configCard": {"title": "Bot-Konfiguration", "labels": {"character": "Gebundener Charakter", "botToken": "Telegram Bot-Token", "config": "Konfiguration"}, "charSelectPlaceholder": "Charakter auswählen", "botTokenInput": {"placeholder": "Telegram Bot-Token eingeben"}, "toggleBotTokenIcon": {"alt": "Bot-Token-Sichtbarkeit umschalten"}, "buttons": {"saveConfig": "Konfiguration speichern", "startBot": "Starten", "stopBot": "Stoppen"}}, "prompts": {"newBotName": "<PERSON>te gib einen neuen Bot-Namen ein:"}, "alerts": {"botExists": "Ein Bot namens \"${botname}\" existiert bereits, bitte verwende einen anderen Namen.", "unsavedChanges": "Sie haben ungespeicherte Änderungen. Möchten Sie diese Änderungen verwerfen?", "configSaved": "Die Konfiguration wurde erfolgreich gespeichert!", "httpError": "HTTP-<PERSON><PERSON>", "beforeUnload": "Sie haben ungespeicherte Änderungen. Sind Si<PERSON> sicher, dass Sie die aktuelle Seite verlassen möchten?"}}, "terminal_assistant": {"title": "Terminal-Assistent", "initialMessage": "Fount unterstützt die Bereitstellung Ihrer Lieblingscharaktere in Ihrem Terminal, um Sie beim Programmieren zu unterstützen!", "initialMessageLink": "<PERSON><PERSON><PERSON> hier, um mehr zu erfahren"}, "access": {"title": "Auf anderen Geräten auf Fount zugreifen", "heading": "<PERSON>öchten Sie auf anderen Geräten auf Fount zugreifen?", "instruction": {"sameLAN": "<PERSON><PERSON><PERSON> sic<PERSON>, dass sich das Gerät und der Fount-Host im selben lokalen Netzwerk befinden.", "accessthis": "Rufen Sie folgende URL auf:"}, "copyButton": "URL kopieren", "copied": "URL in die Zwischenablage kopiert!"}, "proxy": {"title": "API-Proxy", "heading": "OpenAI API Proxy-Adresse", "instruction": "Fügen Sie die folgende Adresse in jede Anwendung ein, die das OpenAI-API-Format benötigt, um die KI-Quellen in Fount zu verwenden!", "copyButton": "<PERSON><PERSON><PERSON> k<PERSON>", "copied": "Adresse in die Zwischenablage kopiert!"}, "404": {"title": "Seite nicht gefunden", "pageNotFoundText": "Hoppla! Sie scheinen auf einer Seite gelandet zu sein, die nicht existiert.", "homepageButton": "Zur Startseite zurückkehren", "MineSweeper": {"difficultyLabel": "Schwierigkeitsgrad:", "difficultyEasy": "<PERSON><PERSON><PERSON>", "difficultyMedium": "<PERSON><PERSON><PERSON>", "difficultyHard": "Schwer", "difficultyCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minesLeftLabel": "Verbleibende Minen:", "timeLabel": "Zeit:", "restartButton": "Neustart", "rowsLabel": "Zeilen:", "colsLabel": "Spalten:", "minesCountLabel": "Minenanzahl:", "winMessage": "<PERSON>z<PERSON><PERSON> Glückwunsch, <PERSON><PERSON> haben gewonnen!", "loseMessage": "<PERSON>pie<PERSON> vorbei, <PERSON><PERSON> haben eine Mine getroffen!", "soundOn": "Ton ein", "soundOff": "Ton aus"}}, "userSettings": {"title": "Benutzereinstellungen", "PageTitle": "Benutzereinstellungen", "apiError": "API-Anfrage fehlgeschlagen: ${message}", "generalError": "Es ist ein Fehler aufgetreten: ${message}", "userInfo": {"title": "Benutzerinformationen", "usernameLabel": "Benutzername:", "creationDateLabel": "Kontoerstellungsdatum:", "folderSizeLabel": "Benutzerdatengröße:", "folderPathLabel": "Benutzerdatenpfad:", "copyPathBtnTitle": "Pfad kopieren", "copiedAlert": "Der Pfad wurde in die Zwischenablage kopiert!"}, "changePassword": {"title": "Passwort ändern", "currentPasswordLabel": "Aktuelles Passwort:", "newPasswordLabel": "Neues Passwort:", "confirmNewPasswordLabel": "Neues Passwort bestätigen:", "submitButton": "Passwort ändern", "errorMismatch": "Die neuen Passwörter stimmen nicht überein.", "success": "Passwort erfolgreich geändert."}, "renameUser": {"title": "Benutzer umbenennen", "newUsernameLabel": "<PERSON>euer Benutzername:", "submitButton": "Benutzer umbenennen", "confirmMessage": "Sind <PERSON> sicher, dass Sie Ihren Benutzernamen ändern möchten? Sie müssen sich danach neu anmelden.", "success": "Der Benutzer wurde erfolgreich in \"${newUsername}\" umbenannt. Sie werden jetzt abgemeldet."}, "userDevices": {"title": "Benutzergeräte/-sitzungen", "refreshButtonTitle": "Liste aktualisieren", "noDevicesFound": "<PERSON>s wurden keine Geräte oder Sitzungen gefunden.", "deviceInfo": "Geräte-ID: ${deviceId}", "thisDevice": "<PERSON><PERSON>", "deviceDetails": "Zuletzt online: ${lastSeen} | IP: ${ipAddress} | UA: ${userAgent}", "revokeButton": "Widerrufen", "revokeConfirm": "Sind <PERSON> sicher, dass Sie sich von diesem Gerät/dieser Sitzung abmelden möchten?", "revokeSuccess": "Das Gerät/Die Sitzung wurde erfolgreich widerrufen."}, "logout": {"title": "Abmelden", "description": "Dadurch wird Ihr Konto vom aktuellen Gerät abgemeldet.", "buttonText": "Abmelden", "confirmMessage": "Sind Sie sicher, dass Sie sich abmelden möchten?", "successMessage": "Erfolgreich abgemeldet. Sie werden zur Anmeldeseite weitergeleitet..."}, "deleteAccount": {"title": "Konto löschen", "warning": "Warnung: Diese Aktion löscht Ihr Konto und alle zugehörigen Daten dauerhaft und kann nicht wiederhergestellt werden.", "submitButton": "Mein Ko<PERSON> löschen", "confirmMessage1": "Warnung! Sind <PERSON> sicher, dass Sie Ihr Konto dauerhaft löschen möchten? Dieser Vorgang kann nicht rückgängig gemacht werden.", "confirmMessage2": "Um die Löschung zu bestätigen, geben Sie bitte Ihren Benutzernamen \"${username}\" ein:", "usernameMismatch": "Der eingegebene Benutzername stimmt nicht mit dem aktuellen Benutzer überein. Der Löschvorgang wurde abgebrochen.", "success": "Das Konto wurde erfolgreich gelöscht. Sie werden jetzt abgemeldet."}, "passwordConfirm": {"title": "Vorgang bestätigen", "message": "Um diesen Vorgang fortzusetzen, geben Sie bitte Ihr aktuelles Passwort ein:", "passwordLabel": "Passwort:", "confirmButton": "Bestätigen", "cancelButton": "Abbrechen"}}}