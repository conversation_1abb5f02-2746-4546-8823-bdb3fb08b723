<!DOCTYPE html>
<html data-theme="dark">

<head>
	<meta charset="UTF-8">
	<meta name="darkreader-lock">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title data-i18n="chat_history.title"></title>
	<link href="https://cdn.jsdelivr.net/npm/daisyui/daisyui.css" rel="stylesheet" type="text/css" />
	<link href="/base.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser"></script>
	<script type="module" src="/base.mjs"></script>
	<link rel="stylesheet" href="./index.css" type="text/css" />
</head>

<body>
	<div class="container mx-auto p-4">
		<div class="flex justify-between items-center mb-4 flex-wrap">
			<h1 class="text-2xl" data-i18n="chat_history.pageTitle"></h1>
			<div class="flex gap-2">
				<select id="sort-select" class="select select-bordered"> <!-- Added select-bordered for better visual -->
					<option value="time_desc" data-i18n="chat_history.sortOptions.time_desc"></option>
					<option value="time_asc" data-i18n="chat_history.sortOptions.time_asc"></option>
				</select>
				<input type="text" id="filter-input" class="input input-bordered" data-i18n="chat_history.filterInput" /> <!-- Added input-bordered for better visual -->
			</div>
		</div>
		<div class="flex items-center mb-4 gap-2">
			<input type="checkbox" id="select-all-checkbox" class="checkbox" />
			<label for="select-all-checkbox" class="cursor-pointer select-none" data-i18n="chat_history.selectAll"></label>
			<button id="reverse-select-button" class="btn btn-xs" data-i18n="chat_history.buttons.reverseSelect"></button>
			<button id="delete-selected-button" class="btn btn-xs btn-error" data-i18n="chat_history.buttons.deleteSelected"></button>
			<button id="export-selected-button" class="btn btn-xs" data-i18n="chat_history.buttons.exportSelected"></button>
		</div>
		<div id="chat-list-container">
		</div>
	</div>

	<script type="module" src="./index.mjs"></script>
</body>

</html>
