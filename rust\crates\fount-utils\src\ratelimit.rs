//! 速率限制工具
//! 对应原文件: src/scripts/ratelimit.mjs

use governor::{Quota, RateLimiter};
use nonzero_ext::*;
use std::time::Duration;

/// 速率限制器
pub struct RateLimit {
    limiter: RateLimiter<String, dashmap::DashMap<String, governor::InMemoryState>, governor::clock::DefaultClock>,
}

impl RateLimit {
    pub fn new(requests_per_second: u32) -> Self {
        let quota = Quota::per_second(nonzero!(requests_per_second));
        let limiter = RateLimiter::keyed(quota);
        Self { limiter }
    }
    
    pub async fn check(&self, key: &str) -> bool {
        self.limiter.check_key(key).is_ok()
    }
}
