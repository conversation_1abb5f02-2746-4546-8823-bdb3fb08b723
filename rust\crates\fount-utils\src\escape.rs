//! 字符串转义工具
//! 对应原文件: src/scripts/escape.mjs

/// HTML转义
pub fn escape_html(input: &str) -> String {
    input
        .replace('&', "&amp;")
        .replace('<', "&lt;")
        .replace('>', "&gt;")
        .replace('"', "&quot;")
        .replace('\'', "&#x27;")
}

/// JSON转义
pub fn escape_json(input: &str) -> String {
    serde_json::to_string(input).unwrap_or_else(|_| "\"\"".to_string())
}

/// URL转义
pub fn escape_url(input: &str) -> String {
    urlencoding::encode(input).to_string()
}
