<!DOCTYPE html>
<html data-theme="dark">

<head>
	<meta charset="UTF-8">
	<meta name="darkreader-lock">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title data-i18n="themeManage.title"></title>
	<link href="https://cdn.jsdelivr.net/npm/daisyui/daisyui.css" rel="stylesheet" type="text/css" />
	<link href="/base.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser"></script>
	<script type="module" src="/base.mjs"></script>
	<link rel="stylesheet" href="./index.css" type="text/css" />
</head>

<body>
	<div class="flex flex-col justify-center items-center min-h-screen">
		<h1 class="text-4xl font-bold mb-4 px-4 text-center" data-i18n="themeManage.instruction"></h1>

		<div class="flex flex-col lg:flex-row gap-8 w-full px-4">
			<!-- Preview Area -->
			<div id="preview-area" class="lg:w-1/2 lg:sticky lg:top-4 max-h-[80vh] overflow-y-auto p-4 lg:p-6 rounded-box bg-base-100/25 shadow-md">
				<div class="w-full space-y-4">
					<h2 class="text-2xl font-bold text-center">Preview</h2>

					<!-- 预览内容保持不变 -->
					<div class="flex justify-center space-x-2">
						<button class="btn btn-primary">Primary</button>
						<button class="btn btn-secondary">Secondary</button>
						<button class="btn btn-accent">Accent</button>
						<button class="btn btn-ghost">Ghost</button>
					</div>
					<div class="rounded-box border border-base-300 p-2">
						<ul class="menu">
							<li><a>Item 1</a></li>
							<li><a>Item 2</a></li>
							<li><a>Item 3</a></li>
						</ul>
					</div>
					<fieldset class="fieldset">
						<select class="select">
							<option disabled selected>1000-7 = ?</option>
							<option>993</option>
							<option>hanbaobao</option>
							<option>1000-7</option>
						</select>
					</fieldset>

					<label class="label cursor-pointer flex items-center">
						<input type="checkbox" class="checkbox checkbox-primary" checked />
						<span class="ml-2">我将身来在了大街前 把你来想念</span>
						<span class="ml-2">只惜今生恐难再相见</span>
					</label>


					<div class="flex justify-center">
						<div class="avatar">
							<div class="w-24 rounded-xl">
								<img src="https://avatars.githubusercontent.com/u/31927825?v=4" />
							</div>
						</div>
					</div>
					<div class="flex space-x-4 items-center">
						<div class="skeleton h-6 w-1/4"></div>
						<div class="skeleton h-4 w-1/2"></div>
						<div class="skeleton h-8 w-1/4"></div>
					</div>

					<fieldset class="fieldset">
						<input type="text" placeholder="Type here" class="input" />
					</fieldset>

					<div class="flex justify-center space-x-4">
						<fieldset class="fieldset">
							<label class="label cursor-pointer">
								<input type="radio" name="radio-group" class="radio" checked />
								<span class="ml-2">Radio 1</span>
							</label>
						</fieldset>
						<fieldset class="fieldset">
							<label class="label cursor-pointer">
								<input type="radio" name="radio-group" class="radio" />
								<span class="ml-2">Radio 2</span>
							</label>
						</fieldset>
					</div>
					<progress class="progress progress-primary w-full"></progress>
					<div class="flex justify-center space-x-2">
						<div class="badge badge-primary">Primary</div>
						<div class="badge badge-secondary">Secondary</div>
						<div class="badge badge-accent">Accent</div>
						<div class="badge badge-ghost">Ghost</div>
					</div>
					<div class="alert alert-info">
						<div>
							<img src="https://api.iconify.design/line-md/alert-circle.svg" />
							<span>Info alert!</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Theme List -->
			<div id="theme-list" class="lg:w-1/2 grid gap-4 max-h-[80vh] overflow-y-auto grid-cols-[repeat(auto-fit,minmax(160px,1fr))] p-4 lg:p-6"></div>
		</div>
	</div>

	<script type="module" src="./index.mjs"></script>
</body>

</html>
