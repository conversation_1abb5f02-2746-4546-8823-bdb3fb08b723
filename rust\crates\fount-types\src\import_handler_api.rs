//! 导入处理器API类型定义
//! 对应原文件: src/decl/importHandlerAPI.ts

use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 导入处理器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportHandler {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub supported_formats: Vec<String>,
}

/// 导入请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportRequest {
    pub handler_id: Uuid,
    pub file_data: Vec<u8>,
    pub file_name: String,
    pub options: serde_json::Value,
}
