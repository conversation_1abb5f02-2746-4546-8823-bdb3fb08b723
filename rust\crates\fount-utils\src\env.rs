//! 环境检测工具
//! 对应原文件: src/scripts/env.mjs

use sysinfo::{System, SystemExt};

/// 获取系统信息
pub fn get_system_info() -> SystemInfo {
    let sys = System::new_all();
    
    SystemInfo {
        os: std::env::consts::OS.to_string(),
        arch: std::env::consts::ARCH.to_string(),
        total_memory: sys.total_memory(),
        available_memory: sys.available_memory(),
        cpu_count: sys.cpus().len(),
    }
}

#[derive(Debug)]
pub struct SystemInfo {
    pub os: String,
    pub arch: String,
    pub total_memory: u64,
    pub available_memory: u64,
    pub cpu_count: usize,
}
