{"lang": "it-IT", "fountConsole": {"server": {"start": "Avvio del server...", "starting": "Server in fase di avvio...", "ready": "Server avviato.", "usesdTime": "Tempo di avvio: ${time}s", "showUrl": {"https": "Server HTTPS in esecuzione su ${url}", "http": "Server HTTP in esecuzione su ${url}"}, "standingBy": "In attesa..."}, "jobs": {"restartingJob": "Riavvio attività ${parttype} ${partname} per l'utente ${username}: ${uid}."}, "ipc": {"sendCommandFailed": "Impossibile inviare il comando: ${error}", "invalidCommand": "Comando non valido, usa \"fount runshell <nome_utente> <nome_shell> <parametri...>\"", "runShellLog": "Esecuzione shell ${shellname} come ${username}, parametri: ${args}.", "invokeShellLog": "Chiamata shell ${shellname} come ${username}, parametri: ${invokedata}.", "unsupportedCommand": "Tipo di comando non supportato.", "processMessageError": "Errore durante l'elaborazione del messaggio IPC: ${error}", "invalidCommandFormat": "Formato comando non valido.", "socketError": "Errore socket: ${error}", "instanceRunning": "Un'altra istanza è già in esecuzione.", "serverStartPrefix": "Avvio server", "serverStarted": "Server IPC avviato.", "parseResponseFailed": "Impossibile analizzare la risposta del server: ${error}", "cannotParseResponse": "Impossibile analizzare la risposta del server.", "unknownError": "<PERSON><PERSON><PERSON> sconos<PERSON>."}, "partManager": {"git": {"noUpstream": "Nessun branch upstream configurato per il branch '${currentBranch}', verifica degli aggiornamenti saltata.", "dirtyWorkingDirectory": "La directory di lavoro non è pulita. Esegui 'stash' o 'commit' delle modifiche prima di aggiornare.", "updating": "Aggiornamento dal repository remoto...", "localAhead": "Il branch locale è più avanti del branch remoto. Nessun aggiornamento necessario.", "diverged": "I branch locale e remoto sono divergenti. Forzatura dell'aggiornamento...", "upToDate": "Già aggiornato.", "updateFailed": "Impossibile aggiornare i componenti dal repository remoto: ${error}"}, "partInitTime": "${parttype} componente ${partname} tempo di inizializzazione: ${time}s", "partLoadTime": "${parttype} componente ${partname} tempo di caricamento: ${time}s"}, "web": {"requestReceived": "Richiesta ricevuta: ${method} ${url}"}, "route": {"setLanguagePreference": "L'utente ${username} ha impostato la lingua preferita: ${preferredLanguages}"}, "auth": {"tokenVerifyError": "Errore di verifica del token: ${error}", "refreshTokenError": "Errore token di aggiornamento: ${error}", "logoutRefreshTokenProcessError": "Errore durante l'elaborazione del token di aggiornamento per il logout: ${error}", "revokeTokenNoJTI": "Impossibile revocare il token senza JTI.", "accountLockedLog": "L'account dell'utente ${username} è stato bloccato a causa di molteplici tentativi di accesso falliti."}, "verification": {"codeGeneratedLog": "Codice di verifica: ${code} (scade tra 60 secondi).", "codeNotifyTitle": "Codice di verifica", "codeNotifyBody": "Codice di verifica: ${code} (scade tra 60 secondi)."}, "tray": {"readIconFailed": "Impossibile leggere il file dell'icona: ${error}", "createTrayFailed": "Impossibile creare l'icona nell'area di notifica: ${error}"}, "discordbot": {"botStarted": "Il personaggio ${charname} ha effettuato l'accesso al bot Discord ${botusername}."}, "telegrambot": {"botStarted": "Il personaggio ${charname} ha effettuato l'accesso al bot Telegram ${botusername}."}}, "protocolhandler": {"title": "Gestione protocollo Fount", "processing": "Elaborazione del protocollo in corso...", "invalidProtocol": "Protocollo non valido.", "insufficientParams": "Parametri insufficienti.", "unknownCommand": "<PERSON><PERSON><PERSON>.", "shellCommandSent": "Comando shell inviato.", "shellCommandFailed": "Impossibile inviare il comando shell.", "shellCommandError": "Errore durante l'invio del comando shell."}, "auth": {"title": "Autenticazione", "subtitle": "I dati utente sono archiviati localmente.", "usernameLabel": "Nome utente:", "usernameInput": {"placeholder": "Inser<PERSON>ci il tuo nome utente"}, "passwordLabel": "Password:", "passwordInput": {"placeholder": "Inserisci la tua password"}, "confirmPasswordLabel": "Conferma password:", "confirmPasswordInput": {"placeholder": "Reinserisci la tua password"}, "verificationCodeLabel": "Codice di verifica:", "verificationCodeInput": {"placeholder": "Inserisci il codice di verifica"}, "sendCodeButton": "Invia codice", "login": {"title": "Accedi", "submitButton": "Accedi", "toggleLink": {"text": "Non hai un account?", "link": "Registrati ora"}}, "register": {"title": "Registrati", "submitButton": "Registrati", "toggleLink": {"text": "Hai già un account?", "link": "Accedi ora"}}, "error": {"passwordMismatch": "Le password non coincidono.", "loginError": "Errore durante l'accesso.", "registrationError": "Errore durante la registrazione.", "verificationCodeError": "Il codice di verifica è errato o scaduto.", "verificationCodeSent": "Codice di verifica inviato correttamente.", "verificationCodeSendError": "Impossibile inviare il codice di verifica.", "verificationCodeRateLimit": "Troppe richieste di invio del codice di verifica. Riprova più tardi.", "lowPasswordStrength": "La password è troppo debole.", "accountAlreadyExists": "L'account esiste già."}, "passwordStrength": {"veryWeak": "<PERSON><PERSON>o de<PERSON>e", "weak": "De<PERSON>e", "normal": "Normale", "strong": "Forte", "veryStrong": "Molto forte"}}, "tutorial": {"title": "Ti va un tutorial?", "modal": {"title": "Benvenuto in Fount!", "instruction": "Vuoi seguire il tutorial per principianti?", "buttons": {"start": "Inizia il tutorial", "skip": "Salta"}}, "endScreen": {"title": "Fantastico! Tutorial completato!", "subtitle": "Ora hai imparato come si usa!", "endButton": "Avanti!"}, "progressMessages": {"mouseMove": "Prova a muovere il mouse ${mouseIcon} con la mano.", "keyboardPress": "Premi un tasto sulla tua tastiera ${keyboardIcon}.", "mobileTouchMove": "Tocca lo schermo del tuo telefono ${phoneIcon} con un dito e poi trascinalo.", "mobileClick": "Tocca lo schermo del tuo telefono ${phoneIcon} con un dito."}}, "home": {"title": "Home", "escapeConfirm": "Sei sicuro di voler uscire da Fount?", "filterInput": {"placeholder": "Cerca..."}, "sidebarTitle": "<PERSON><PERSON><PERSON>", "itemDescription": "Seleziona un elemento qui per visualizzarne i dettagli.", "noDescription": "Nessuna descrizione disponibile.", "alerts": {"fetchHomeRegistryFailed": "Impossibile recuperare le informazioni del registro home."}, "functionMenu": {"icon": {"alt": "Menu funzioni"}}, "chars": {"tab": "<PERSON><PERSON><PERSON>", "title": "Selezione personaggio", "subtitle": "Seleziona un personaggio e inizia a chattare!", "none": "<PERSON><PERSON> da <PERSON>e", "card": {"refreshButton": {"alt": "Aggiorna", "title": "Aggiorna"}, "noTags": "<PERSON><PERSON><PERSON> tag", "version": "Versione", "author": "Autore", "homepage": "Homepage", "issuepage": "Se<PERSON>la un problema", "defaultCheckbox": {"title": "Imposta come personaggio predefinito"}}}, "worlds": {"tab": "<PERSON><PERSON>", "title": "Selezione mondo", "subtitle": "<PERSON><PERSON><PERSON> un mondo e immergiti!", "none": "<PERSON><PERSON> da <PERSON>e", "card": {"refreshButton": {"alt": "Aggiorna", "title": "Aggiorna"}, "noTags": "<PERSON><PERSON><PERSON> tag", "version": "Versione", "author": "Autore", "homepage": "Homepage", "issuepage": "Se<PERSON>la un problema", "defaultCheckbox": {"title": "Imposta come mondo predefinito"}}}, "personas": {"tab": "<PERSON>e", "title": "Se<PERSON><PERSON> persona", "subtitle": "Seleziona una persona e vivi una nuova esperienza.", "none": "<PERSON><PERSON> da <PERSON>e", "card": {"refreshButton": {"alt": "Aggiorna", "title": "Aggiorna"}, "noTags": "<PERSON><PERSON><PERSON> tag", "version": "Versione", "author": "Autore", "homepage": "Homepage", "issuepage": "Se<PERSON>la un problema", "defaultCheckbox": {"title": "<PERSON>mpo<PERSON> come persona predefinita"}}}}, "themeManage": {"title": "Gestione temi", "instruction": "<PERSON><PERSON><PERSON> un tema!", "themes": {"auto": "Automatico", "light": "Chiaro", "dark": "<PERSON><PERSON>", "cupcake": "Cupcake", "bumblebee": "Bumblebee", "emerald": "<PERSON><PERSON><PERSON>", "corporate": "Corporate", "synthwave": "Synthwave", "retro": "<PERSON><PERSON><PERSON>", "cyberpunk": "Cyberpunk", "valentine": "San Valentino", "halloween": "Halloween", "garden": "Giardino", "forest": "Foresta", "aqua": "Aqua", "lofi": "lo-fi", "pastel": "<PERSON><PERSON>", "fantasy": "Fantasia", "wireframe": "Wireframe", "black": "<PERSON>", "luxury": "Lusso", "dracula": "Dracula", "cmyk": "CMYK", "autumn": "<PERSON><PERSON><PERSON>", "business": "Attività commerciale", "acid": "Acid", "lemonade": "Limonata", "night": "Notte", "coffee": "Caffè", "winter": "Inverno", "dim": "<PERSON><PERSON>", "nord": "Nordico", "sunset": "Sunset", "caramellatte": "Caramel Latte", "abyss": "<PERSON><PERSON><PERSON>", "silk": "<PERSON><PERSON>"}}, "import": {"title": "Importa", "tabs": {"fileImport": "Importa file", "textImport": "<PERSON><PERSON><PERSON> testo"}, "dropArea": {"icon": {"alt": "Icona di caricamento"}, "text": "Trascina i file qui oppure fai clic per selezionare i file."}, "textArea": {"placeholder": "Inserisci il testo da importare..."}, "buttons": {"import": "Importa"}, "alerts": {"importSuccess": "Importazione riuscita.", "importFailed": "Importazione non riuscita: ${error}", "unknownError": "<PERSON><PERSON><PERSON> sconos<PERSON>."}, "errors": {"noFileSelected": "Seleziona un file.", "fileImportFailed": "Importazione file non riuscita: ${message}", "noTextContent": "Inserisci il contenuto del testo.", "textImportFailed": "Importazione testo non riuscita: ${message}", "unknownError": "<PERSON><PERSON><PERSON> sconos<PERSON>.", "handler": "<PERSON><PERSON><PERSON>", "error": "Errore"}, "fileItem": {"removeButton": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "removeButtonIcon": {"alt": "<PERSON><PERSON><PERSON><PERSON>"}}}, "aisource_editor": {"title": "Editor sorgente IA", "fileList": {"title": "Elenco sorgenti IA", "addButton": {"title": "+"}}, "configTitle": "Configurazione sorgente IA", "generatorSelect": {"label": "Seleziona generatore", "placeholder": "Seleziona"}, "buttons": {"save": "<PERSON><PERSON>", "delete": "Elimina"}, "alerts": {"fetchFileListFailed": "Impossibile ottenere l'elenco dei file: ${error}", "fetchGeneratorListFailed": "Impossibile ottenere l'elenco dei generatori: ${error}", "fetchFileDataFailed": "Impossibile ottenere i dati del file: ${error}", "noFileSelectedSave": "Nessun file selezionato da salvare.", "saveFileFailed": "Impossibile salvare il file: ${error}", "noFileSelectedDelete": "Nessun file selezionato da eliminare.", "deleteFileFailed": "Impossibile eliminare il file: ${error}", "invalidFileName": "Il nome del file non può contenere i seguenti caratteri: / \\ : * ? \" < > |", "addFileFailed": "Impossibile aggiungere il file: ${error}", "fetchConfigTemplateFailed": "Impossibile ottenere il template di configurazione.", "noGeneratorSelectedSave": "Seleziona un generatore prima di salvare."}, "confirm": {"unsavedChanges": "Hai modifiche non salvate. Vuoi scartarle?", "deleteFile": "Sei sicuro di voler eliminare il file?", "unsavedChangesBeforeUnload": "Hai modifiche non salvate. Sei sicuro di voler lasciare questa pagina?"}, "prompts": {"newFileName": "Inserisci il nuovo nome del file sorgente IA (senza estensione):"}, "editor": {"disabledIndicator": "Seleziona prima un generatore."}}, "part_config": {"title": "Configurazione componente", "pageTitle": "Configurazione componente", "labels": {"partType": "Seleziona tipo di componente", "part": "Seleziona componente"}, "placeholders": {"partTypeSelect": "Seleziona", "partSelect": "Seleziona"}, "editor": {"title": "Configurazione componente", "disabledIndicator": "Questo componente non supporta la configurazione.", "buttons": {"save": "<PERSON><PERSON>"}}, "errorMessage": {"icon": {"alt": "Messaggio di errore"}}, "alerts": {"fetchPartTypesFailed": "Impossibile ottenere i tipi di componente.", "fetchPartsFailed": "Impossibile ottenere l'elenco dei componenti.", "loadEditorFailed": "Impossibile caricare l'editor.", "saveConfigFailed": "Impossibile salvare la configurazione del componente.", "unsavedChanges": "Hai modifiche non salvate. Vuoi scartarle?", "beforeUnload": "Hai modifiche non salvate. Sei sicuro di voler uscire?"}}, "uninstall": {"title": "Disin<PERSON>la", "titleWithName": "Disinstalla ${type}/${name}", "confirmMessage": "Sei sicuro di voler disinstallare ${type}: ${name}?", "invalidParamsTitle": "Parametri non validi", "infoMessage": {"icon": {"alt": "Icona informazioni"}}, "errorMessage": {"icon": {"alt": "Icona errore"}}, "buttons": {"confirm": "Conferma disinstallazione", "cancel": "<PERSON><PERSON><PERSON>", "back": "Indietro"}, "alerts": {"success": "${type}: ${name} disinstallato correttamente.", "failed": "Disinstallazione non riuscita: ${error}", "invalidParams": "Parametri della richiesta non validi.", "httpError": "Errore HTTP! Codice stato: ${status}"}}, "chat": {"new": {"title": "Nuova chat"}, "title": "Cha<PERSON>", "sidebar": {"world": {"icon": {"alt": "Icona Mondo"}, "title": "<PERSON><PERSON>"}, "persona": {"icon": {"alt": "Icona Persona utente"}, "title": "Persona utente"}, "charList": {"icon": {"alt": "Icona Elenco personaggi"}, "title": "<PERSON>en<PERSON>i", "buttons": {"addChar": {"title": "Aggiu<PERSON>i personaggio"}, "addCharIcon": {"alt": "Icona Aggiungi personaggio"}}}, "noSelection": "Non selezionato", "noDescription": "Nessuna descrizione disponibile."}, "chatArea": {"title": "Cha<PERSON>", "menuButton": {"title": "<PERSON><PERSON>"}, "menuButtonIcon": {"alt": "Icona Menu"}, "input": {"placeholder": "Scrivi un messaggio...\\nCtrl+Invio per inviare"}, "sendButton": {"title": "Invia"}, "sendButtonIcon": {"alt": "Icona Invia"}, "uploadButton": {"title": "Carica"}, "uploadButtonIcon": {"alt": "Icona Carica"}, "voiceButton": {"title": "Registra vocale"}, "voiceButtonIcon": {"alt": "Icona Registra vocale"}, "photoButton": {"title": "Foto"}, "photoButtonIcon": {"alt": "Icona Foto"}}, "rightSidebar": {"title": "<PERSON><PERSON><PERSON>"}, "messageList": {"confirmDeleteMessage": "Confermi di voler eliminare questo messaggio?"}, "voiceRecording": {"errorAccessingMicrophone": "Impossibile accedere al microfono."}, "messageView": {"buttons": {"edit": {"title": "Modifica"}, "editIcon": {"alt": "Icona Modifica"}, "delete": {"title": "Elimina"}, "deleteIcon": {"alt": "Icona Elimina"}}}, "messageEdit": {"input": {"placeholder": "Inserisci contenuto..."}, "buttons": {"confirm": {"title": "Conferma"}, "confirmIcon": {"alt": "Icona Conferma"}, "cancel": {"title": "<PERSON><PERSON><PERSON>"}, "cancelIcon": {"alt": "Icona Annulla"}, "upload": {"title": "Carica"}, "uploadIcon": {"alt": "Icona Carica"}}}, "attachment": {"buttons": {"download": {"title": "Scarica"}, "downloadIcon": {"alt": "Icona Scarica"}, "delete": {"title": "Elimina"}, "deleteIcon": {"alt": "Icona Elimina"}}}, "charCard": {"frequencyLabel": "Frequenza", "buttons": {"removeChar": {"title": "<PERSON><PERSON><PERSON><PERSON> dalla chat"}, "removeCharIcon": {"alt": "Icona Rimuovi personaggio"}, "forceReply": {"title": "Forza risposta"}, "forceReplyIcon": {"alt": "Icona Forza risposta"}}}}, "chat_history": {"title": "Cronologia chat", "pageTitle": "Cronologia chat", "sortOptions": {"time_desc": "Data (più recenti)", "time_asc": "Data (meno recenti)"}, "filterInput": {"placeholder": "Cerca..."}, "selectAll": "Se<PERSON><PERSON>na tutto", "buttons": {"reverseSelect": "Inverti selezione", "deleteSelected": "Elimina selezionati", "exportSelected": "Esporta selezionati"}, "confirmDeleteChat": "Sei sicuro di voler eliminare la cronologia chat con ${chars}?", "confirmDeleteMultiChats": "Sei sicuro di voler eliminare le ${count} cronologie chat selezionate?", "alerts": {"noChatSelectedForDeletion": "Seleziona le cronologie chat da eliminare.", "noChatSelectedForExport": "Seleziona le cronologie chat da esportare.", "copyError": "Copia non riuscita", "deleteError": "La cancellazione non è riuscita", "exportError": "L'esportazione non è riuscita"}, "chatItemButtons": {"continue": "Continua", "copy": "Copia", "export": "Esporta", "delete": "Elimina"}}, "discord_bots": {"title": "<PERSON><PERSON>", "cardTitle": "<PERSON><PERSON>", "buttons": {"newBot": "Nuovo", "deleteBot": "Elimina"}, "configCard": {"title": "Configurazione bot", "labels": {"character": "Personaggio", "apiKey": "Chiave API Discord", "config": "Configurazione"}, "charSelectPlaceholder": "Seleziona personaggio", "apiKeyInput": {"placeholder": "Inserisci chiave API"}, "toggleApiKeyIcon": {"alt": "Mostra/Nascondi chiave API"}, "buttons": {"saveConfig": "Salva configurazione", "startBot": "Avvia", "stopBot": "<PERSON><PERSON><PERSON>"}}, "prompts": {"newBotName": "Inserisci un nuovo nome per il bot:"}, "alerts": {"botExists": "Un bot con nome \"${botname}\" esiste già, usa un nome diverso.", "unsavedChanges": "Hai modifiche non salvate. Vuoi scartarle?", "configSaved": "Configurazione salvata correttamente.", "httpError": "Errore HTTP.", "beforeUnload": "Hai modifiche non salvate. Sei sicuro di voler uscire?"}}, "telegram_bots": {"title": "Bot Telegram", "cardTitle": "Gestione Bot Telegram", "buttons": {"newBot": "Nuovo", "deleteBot": "Elimina"}, "configCard": {"title": "Configurazione bot", "labels": {"character": "<PERSON><PERSON><PERSON> associato", "botToken": "Token Bot Telegram", "config": "Configurazione"}, "charSelectPlaceholder": "Seleziona personaggio", "botTokenInput": {"placeholder": "Inserisci token Bot Telegram"}, "toggleBotTokenIcon": {"alt": "Mostra/Nascondi token Bot"}, "buttons": {"saveConfig": "Salva configurazione", "startBot": "Avvia", "stopBot": "<PERSON><PERSON><PERSON>"}}, "prompts": {"newBotName": "Inserisci un nuovo nome per il bot:"}, "alerts": {"botExists": "Un bot con nome \"${botname}\" esiste già, usa un nome diverso.", "unsavedChanges": "Hai modifiche non salvate. Sei sicuro di volerle scartare?", "configSaved": "Configurazione salvata con successo!", "httpError": "Errore HTTP.", "beforeUnload": "Hai modifiche non salvate. Sei sicuro di voler lasciare la pagina corrente?"}}, "terminal_assistant": {"title": "Assistente terminale", "initialMessage": "Fount supporta l'integrazione dei tuoi personaggi preferiti nel terminale per assisterti nella programmazione!", "initialMessageLink": "<PERSON><PERSON><PERSON> qui per saperne di più."}, "access": {"title": "Accedi a Fount su altri dispositivi", "heading": "Vuoi accedere a Fount da altri dispositivi?", "instruction": {"sameLAN": "Assicurati che il dispositivo e l'host Fount siano sulla stessa rete locale.", "accessthis": "Visita questo URL:"}, "copyButton": "Copia URL", "copied": "URL copiato negli appunti!"}, "proxy": {"title": "Proxy API", "heading": "Indirizzo proxy API OpenAI", "instruction": "Inserisci il seguente indirizzo in qualsiasi applicazione che richieda il formato API OpenAI per utilizzare le sorgenti AI in Fount!", "copyButton": "Copia indirizzo", "copied": "Indirizzo copiato negli appunti!"}, "404": {"title": "Pagina non trovata", "pageNotFoundText": "Ops! Sembra che tu sia finito in una pagina inesistente.", "homepageButton": "Torna alla homepage", "MineSweeper": {"difficultyLabel": "Difficoltà:", "difficultyEasy": "Facile", "difficultyMedium": "Medio", "difficultyHard": "Difficile", "difficultyCustom": "Personalizzata", "minesLeftLabel": "Mine rimaste:", "timeLabel": "Tempo:", "restartButton": "Ricomincia", "rowsLabel": "Righe:", "colsLabel": "Colonne:", "minesCountLabel": "Numero mine:", "winMessage": "Congratula<PERSON>ni, hai vinto!", "loseMessage": "Partita finita, hai colpito una mina!", "soundOn": "Audio attivo", "soundOff": "Audio disattivato"}}, "userSettings": {"title": "Impostazioni utente", "PageTitle": "Impostazioni utente", "apiError": "Richiesta API non riuscita: ${message}", "generalError": "Si è verificato un errore: ${message}", "userInfo": {"title": "Informazioni utente", "usernameLabel": "Nome utente:", "creationDateLabel": "Data creazione account:", "folderSizeLabel": "Dimensioni dati utente:", "folderPathLabel": "<PERSON><PERSON><PERSON> dati utente:", "copyPathBtnTitle": "Co<PERSON> percorso", "copiedAlert": "Percorso copiato negli appunti!"}, "changePassword": {"title": "Modifica password", "currentPasswordLabel": "Password attuale:", "newPasswordLabel": "Nuova password:", "confirmNewPasswordLabel": "Conferma nuova password:", "submitButton": "Modifica password", "errorMismatch": "Le nuove password non coincidono.", "success": "Password modificata con successo."}, "renameUser": {"title": "Rinomina utente", "newUsernameLabel": "Nuovo nome utente:", "submitButton": "Rinomina utente", "confirmMessage": "Sei sicuro di voler cambiare il tuo nome utente? Dovrai effettuare nuovamente l'accesso.", "success": "Nome utente rinominato correttamente in \"${newUsername}\". Verrai disconnesso."}, "userDevices": {"title": "Dispositivi/Sessioni utente", "refreshButtonTitle": "Aggiorna elenco", "noDevicesFound": "Nessun dispositivo o sessione trovata.", "deviceInfo": "ID dispositivo: ${deviceId}", "thisDevice": "Questo dispositivo", "deviceDetails": "Ultimo accesso: ${lastSeen} | IP: ${ipAddress} | UA: ${userAgent}", "revokeButton": "Revoca", "revokeConfirm": "Sei sicuro di voler revocare l'accesso per questo dispositivo/sessione?", "revokeSuccess": "Dispositivo/Sessione revocato con successo."}, "logout": {"title": "<PERSON><PERSON><PERSON>", "description": "Verrai disconnesso dal tuo account su questo dispositivo.", "buttonText": "<PERSON><PERSON><PERSON>", "confirmMessage": "Sei sicuro di voler uscire?", "successMessage": "Disconnessione riuscita. Reindirizzamento alla pagina di accesso in corso..."}, "deleteAccount": {"title": "Elimina account", "warning": "ATTENZIONE: questa azione eliminerà permanentemente il tuo account e tutti i dati correlati e non potrà essere annullata.", "submitButton": "Elimina il mio account", "confirmMessage1": "Attenzione! Sei sicuro di voler eliminare permanentemente il tuo account? Questa operazione non può essere annullata.", "confirmMessage2": "Per confermare l'eliminazione, inserisci il tuo nome utente \"${username}\":", "usernameMismatch": "Il nome utente inserito non corrisponde all'utente corrente. Eliminazione annullata.", "success": "Account eliminato con successo. Verrai disconnesso."}, "passwordConfirm": {"title": "Conferma operazione", "message": "Per continuare, inserisci la tua password attuale:", "passwordLabel": "Password:", "confirmButton": "Conferma", "cancelButton": "<PERSON><PERSON><PERSON>"}}}