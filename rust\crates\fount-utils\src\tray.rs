//! 系统托盘工具
//! 对应原文件: src/scripts/tray.mjs

use tray_icon::{TrayIcon, TrayIconBuilder};
use anyhow::Result;

/// 系统托盘管理器
pub struct TrayManager {
    tray: Option<TrayIcon>,
}

impl TrayManager {
    pub fn new() -> Self {
        Self { tray: None }
    }
    
    pub fn create_tray(&mut self, icon_path: &str, tooltip: &str) -> Result<()> {
        let tray = TrayIconBuilder::new()
            .with_tooltip(tooltip)
            .build()?;
        
        self.tray = Some(tray);
        Ok(())
    }
    
    pub fn update_tooltip(&mut self, tooltip: &str) -> Result<()> {
        if let Some(tray) = &self.tray {
            tray.set_tooltip(Some(tooltip))?;
        }
        Ok(())
    }
}
