//! Fount服务器主入口
//! 对应原文件: src/server/index.mjs

use fount_server::*;
use tracing::{info, error};
use anyhow::Result;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    info!("Starting Fount server...");
    
    // 加载配置
    let config = load_config().await?;
    
    // 初始化服务器
    let server = FountServer::new(config).await?;
    
    // 启动服务器
    match server.start().await {
        Ok(_) => {
            info!("Fount server started successfully");
            Ok(())
        }
        Err(e) => {
            error!("Failed to start server: {}", e);
            Err(e)
        }
    }
}
