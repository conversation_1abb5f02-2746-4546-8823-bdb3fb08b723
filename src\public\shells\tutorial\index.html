<!DOCTYPE html>
<html data-theme="dark">

<head>
	<meta charset="UTF-8">
	<meta name="darkreader-lock">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title data-i18n="tutorial.title"></title>
	<link href="https://cdn.jsdelivr.net/npm/daisyui/daisyui.css" rel="stylesheet" type="text/css" />
	<link href="/base.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser"></script>
	<script type="module" src="https://cdn.jsdelivr.net/gh/steve02081504/fount/.statistic/scripts/tutorial.mjs"></script>
	<script type="module" src="/base.mjs"></script>
	<link rel="stylesheet" href="./index.css" type="text/css" />
	<link rel="prerender" href="/shells/home" />
	<script type="speculationrules">
	{
		"prerender": [
			{
				"urls": ["/shells/home"]
			}
		]
	}
	</script>
</head>

<body>
	<div id="tutorialModal" class="modal modal-open">
		<div class="modal-box">
			<h3 class="font-bold text-lg" data-i18n="tutorial.modal.title"></h3>
			<p class="py-4" data-i18n="tutorial.modal.instruction"></p>
			<div class="modal-action">
				<button id="startTutorial" class="btn btn-primary" data-i18n="tutorial.modal.buttons.start"></button>
				<button id="skipButton" class="btn" data-i18n="tutorial.modal.buttons.skip"></button>
			</div>
		</div>
	</div>

	<div id="progressBar" class="hidden">
		<progress class="progress w-56" value="0" max="100"></progress>
		<p id="progressText"></p>
	</div>

	<div id="tutorialEnd" class="hidden text-center">
		<h2 class="text-2xl" data-i18n="tutorial.endScreen.title"></h2>
		<h2 class="text-2xl" data-i18n="tutorial.endScreen.subtitle"></h2>
		<button id="endButton" class="btn btn-primary" data-i18n="tutorial.endScreen.endButton"></button>
	</div>

	<script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.2/dist/confetti.browser.min.js"></script>
	<script type="module" src="./index.mjs"></script>
</body>

</html>
