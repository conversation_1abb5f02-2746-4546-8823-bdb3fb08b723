<div class="chat-list-item rounded-md bg-base-100 shadow-md mb-2">
	<details class="collapse w-full">
		<summary class="collapse-title flex items-center justify-between text-sm text-base-content opacity-70 hover:opacity-100 cursor-pointer">
			<div class="flex items-center">
				<div class="px-2">
					<input type="checkbox" class="checkbox select-checkbox" />
				</div>

				<div class="px-2 flex items-center">
					<div class="avatar-group -space-x-6 rtl:space-x-reverse">
						${avatars.map(avatar => `<div class="avatar">
							<div class="w-8"><img src="${avatar.url || 'https://gravatar.com/avatar/0?d=mp&f=y'}" alt="${avatar.name}" /></div>
						</div>`).join('')}
					</div>
					<p class="text-sm font-semibold text-base-content ml-2">${chars.join(', ')}</p>
				</div>

				<div class="flex-1 px-2 multiline-truncate">
					<span class="font-medium">${lastMessageSender}:</span>
					${lastMessageRowContent.split('\n').slice(0, 3).join('<br/>')}
				</div>
			</div>

			<div class="px-2 flex flex-col items-end">
				<p class="text-xs text-base-content opacity-50 mb-1 text-right">${lastMessageTime}</p>
				<div class="join join-horizontal">
					<button class="btn btn-xs join-item continue-button" data-i18n="chat_history.chatItemButtons.continue"></button>
					<button class="btn btn-xs join-item copy-button" data-i18n="chat_history.chatItemButtons.copy"></button>
					<button class="btn btn-xs join-item export-button" data-i18n="chat_history.chatItemButtons.export"></button>
					<button class="btn btn-xs join-item btn-error delete-button" data-i18n="chat_history.chatItemButtons.delete"></button>
				</div>
			</div>
		</summary>
		<div class="collapse-content">
			<div class="divider"></div>
			<div class="chat-message chat chat-start mb-4" id="message-${safeTimeStamp}" data-template-type="message">
				<div class="chat-image avatar">
					<div class="w-10 mask mask-squircle rounded-full">
						<img src="${lastMessageSenderAvatar || 'https://gravatar.com/avatar/0?d=mp&f=y'}" alt="${name}" />
					</div>
				</div>
				<div class="chat-bubble relative chat-bubble">
					<div class="text-sm font-bold text-primary-content flex items-center gap-2">
						${lastMessageSender}
					</div>
					<div class="message-content markdown-body mt-1">
						${lastMessageContent}
					</div>
				</div>
				<div class="chat-footer opacity-50">
					${lastMessageTime}
				</div>
			</div>
		</div>
	</details>
</div>
