{"lang": "es-ES", "fountConsole": {"server": {"start": "Iniciando servidor", "starting": "Servidor in<PERSON>...", "ready": "Ser<PERSON>or iniciado", "usesdTime": "Tiempo de inicio: ${time}s", "showUrl": {"https": "Servidor HTTPS en ejecución en ${url}", "http": "Servidor HTTP en ejecución en ${url}"}, "standingBy": "En espera..."}, "jobs": {"restartingJob": "Reiniciando tarea ${partname} (${parttype}) del usuario ${username}: ${uid}"}, "ipc": {"sendCommandFailed": "Error al enviar comando: ${error}", "invalidCommand": "Comando inválido. Utiliza \"fount runshell <nombre_usuario> <nombre_shell> <parámetros...>\"", "runShellLog": "Ejecutando shell ${shellname} como ${username}, parámetros: ${args}", "invokeShellLog": "Invocando shell ${shellname} como ${username}, parámetros: ${invokedata}", "unsupportedCommand": "Tipo de comando no soportado", "processMessageError": "Error al procesar mensaje IPC: ${error}", "invalidCommandFormat": "Formato de comando inválido", "socketError": "Error de socket: ${error}", "instanceRunning": "Otra instancia ya está en ejecución.", "serverStartPrefix": "Inicio del servidor", "serverStarted": "Servidor IPC iniciado.", "parseResponseFailed": "Error al analizar la respuesta del servidor: ${error}", "cannotParseResponse": "No se puede analizar la respuesta del servidor.", "unknownError": "Error des<PERSON>."}, "partManager": {"git": {"noUpstream": "No se ha configurado rama de seguimiento para la rama '${currentBranch}'. Omitiendo comprobación de actualizaciones.", "dirtyWorkingDirectory": "El directorio de trabajo no está limpio. Por favor, haz 'stash' o 'commit' de tus cambios antes de actualizar.", "updating": "Actualizando desde el repositorio remoto...", "localAhead": "La rama local está por delante de la remota. No es necesaria la actualización.", "diverged": "Las ramas local y remota han divergido. Forzando actualización...", "upToDate": "Ya está actualizado.", "updateFailed": "Error al actualizar componentes desde el repositorio remoto: ${error}"}, "partInitTime": "${parttype} componente ${partname} tiempo de inicialización: ${time}s", "partLoadTime": "${parttype} componente ${partname} tiempo de carga: ${time}s"}, "web": {"requestReceived": "Petición recibida: ${method} ${url}"}, "route": {"setLanguagePreference": "El usuario ${username} ha establecido el idioma preferido: ${preferredLanguages}"}, "auth": {"tokenVerifyError": "Error de verificación del token: ${error}", "refreshTokenError": "Error del token de actualización: ${error}", "logoutRefreshTokenProcessError": "Error al procesar el token de actualización al cerrar sesión: ${error}", "revokeTokenNoJTI": "No se puede revocar el token sin JTI.", "accountLockedLog": "Cuenta del usuario ${username} bloqueada debido a múltiples intentos fallidos de inicio de sesión."}, "verification": {"codeGeneratedLog": "Código de verificación: ${code} (expira en 60 segundos).", "codeNotifyTitle": "Código de verificación", "codeNotifyBody": "Código de verificación: ${code} (expira en 60 segundos)."}, "tray": {"readIconFailed": "Error al leer el archivo del icono: ${error}", "createTrayFailed": "Error al crear el icono de la bandeja: ${error}"}, "discordbot": {"botStarted": "El personaje ${charname} ha iniciado sesión en el bot de Discord ${botusername}."}, "telegrambot": {"botStarted": "El personaje ${charname} ha iniciado sesión en el bot de Telegram ${botusername}."}}, "protocolhandler": {"title": "Procesando protocolo Fount", "processing": "Procesando protocolo...", "invalidProtocol": "Protocolo inválido.", "insufficientParams": "Parámetros insuficientes.", "unknownCommand": "Comando desconocido.", "shellCommandSent": "Comando de shell enviado.", "shellCommandFailed": "Error al enviar el comando de shell.", "shellCommandError": "Error enviando el comando de shell."}, "auth": {"title": "Autenticación", "subtitle": "Los datos del usuario se almacenan localmente", "usernameLabel": "Nombre de usuario:", "usernameInput": {"placeholder": "Introduce tu nombre de usuario"}, "passwordLabel": "Contraseña:", "passwordInput": {"placeholder": "Introduce tu contraseña"}, "confirmPasswordLabel": "Confirmar contras<PERSON>ña:", "confirmPasswordInput": {"placeholder": "Vuelve a introducir tu contraseña"}, "verificationCodeLabel": "Código de verificación:", "verificationCodeInput": {"placeholder": "Introduce el código de verificación"}, "sendCodeButton": "Enviar código", "login": {"title": "In<PERSON><PERSON>", "submitButton": "In<PERSON><PERSON>", "toggleLink": {"text": "¿No tienes cuenta?", "link": "Regístrate ahora"}}, "register": {"title": "Registrarse", "submitButton": "Registrarse", "toggleLink": {"text": "¿Ya tienes una cuenta?", "link": "Inicia sesión ahora"}}, "error": {"passwordMismatch": "Las contraseñas no coinciden.", "loginError": "Error al iniciar sesión.", "registrationError": "Error en el registro.", "verificationCodeError": "El código de verificación es incorrecto o ha caducado.", "verificationCodeSent": "Código de verificación enviado correctamente.", "verificationCodeSendError": "Error al enviar el código de verificación.", "verificationCodeRateLimit": "Demasiadas solicitudes de código de verificación. Inténtalo de nuevo más tarde.", "lowPasswordStrength": "La contraseña es demasiado débil.", "accountAlreadyExists": "La cuenta ya existe."}, "passwordStrength": {"veryWeak": "<PERSON><PERSON>", "weak": "<PERSON><PERSON><PERSON>", "normal": "Normal", "strong": "<PERSON><PERSON>e", "veryStrong": "<PERSON><PERSON>uer<PERSON>"}}, "tutorial": {"title": "¿<PERSON><PERSON><PERSON><PERSON> con un tutorial?", "modal": {"title": "¡Bienvenido a Fount!", "instruction": "¿Quieres seguir el tutorial de introducción?", "buttons": {"start": "Iniciar tutorial", "skip": "<PERSON><PERSON><PERSON>"}}, "endScreen": {"title": "¡Genial! ¡Tutorial completado!", "subtitle": "¡Ya estás listo para empezar!", "endButton": "¡Adelante!"}, "progressMessages": {"mouseMove": "Intenta mover el ratón ${mouseIcon} con tu mano.", "keyboardPress": "Pulsa una tecla en tu teclado ${keyboardIcon}.", "mobileTouchMove": "Toca la pantalla de tu móvil ${phoneIcon} con un dedo y luego deslízalo.", "mobileClick": "Toca la pantalla de tu móvil ${phoneIcon} con un dedo."}}, "home": {"title": "<PERSON><PERSON>o", "escapeConfirm": "¿Seguro que quieres salir de Fount?", "filterInput": {"placeholder": "Buscar..."}, "sidebarTitle": "Detalles", "itemDescription": "Selecciona un elemento aquí para ver los detalles.", "noDescription": "No hay descripción disponible.", "alerts": {"fetchHomeRegistryFailed": "Error al obtener la información del registro de inicio."}, "functionMenu": {"icon": {"alt": "Menú de funciones"}}, "chars": {"tab": "<PERSON><PERSON><PERSON>", "title": "Selección de personaje", "subtitle": "¡Selecciona un personaje y empieza a chatear!", "none": "Nada que mostrar", "card": {"refreshButton": {"alt": "Actualizar", "title": "Actualizar"}, "noTags": "Sin etiquetas", "version": "Versión", "author": "Autor", "homepage": "Página de inicio", "issuepage": "Página de incidencias", "defaultCheckbox": {"title": "Establecer como personaje predeterminado"}}}, "worlds": {"tab": "Mundos", "title": "Selección de mundo", "subtitle": "¡Elige un mundo y sumérgete!", "none": "Nada que mostrar", "card": {"refreshButton": {"alt": "Actualizar", "title": "Actualizar"}, "noTags": "Sin etiquetas", "version": "Versión", "author": "Autor", "homepage": "Página de inicio", "issuepage": "Página de incidencias", "defaultCheckbox": {"title": "Establecer como mundo predeterminado"}}}, "personas": {"tab": "Personas", "title": "Sele<PERSON><PERSON> de persona", "subtitle": "Selecciona una persona y vive la experiencia.", "none": "Nada que mostrar", "card": {"refreshButton": {"alt": "Actualizar", "title": "Actualizar"}, "noTags": "Sin etiquetas", "version": "Versión", "author": "Autor", "homepage": "Página de inicio", "issuepage": "Página de incidencias", "defaultCheckbox": {"title": "Establecer como persona predeterminada"}}}}, "themeManage": {"title": "Gestión de temas", "instruction": "¡Elige un tema!", "themes": {"auto": "Automático", "light": "<PERSON><PERSON><PERSON>", "dark": "Oscuro", "cupcake": "Cupcake", "bumblebee": "Bumblebee", "emerald": "Esm<PERSON><PERSON>", "corporate": "Corporativo", "synthwave": "Synthwave", "retro": "Retro", "cyberpunk": "Ciberpunk", "valentine": "Día de San Valentín", "halloween": "Halloween", "garden": "Jardín", "forest": "Bosque", "aqua": "Aqua", "lofi": "lo-fi", "pastel": "Pastel", "fantasy": "Fantasía", "wireframe": "Wireframe", "black": "Negro", "luxury": "<PERSON><PERSON>", "dracula": "<PERSON><PERSON><PERSON>", "cmyk": "CMYK", "autumn": "<PERSON><PERSON><PERSON>", "business": "Nego<PERSON><PERSON>", "acid": "Acid", "lemonade": "Limonada", "night": "Noche", "coffee": "Café", "winter": "Invierno", "dim": "<PERSON><PERSON>", "nord": "Nórdico", "sunset": "<PERSON>ardec<PERSON>", "caramellatte": "Caramel Latte", "abyss": "Abismo", "silk": "Seda"}}, "import": {"title": "Importar", "tabs": {"fileImport": "Importar archivo", "textImport": "Importar texto"}, "dropArea": {"icon": {"alt": "Icono de carga"}, "text": "Arrastra y suelta archivos aquí o haz clic para seleccionar archivos"}, "textArea": {"placeholder": "Introduce el texto a importar..."}, "buttons": {"import": "Importar"}, "alerts": {"importSuccess": "<PERSON><PERSON>rtado correctamente.", "importFailed": "Error al importar: ${error}", "unknownError": "Error des<PERSON>."}, "errors": {"noFileSelected": "Por favor, selecciona un archivo.", "fileImportFailed": "Error al importar el archivo: ${message}", "noTextContent": "Por favor, introduce contenido de texto.", "textImportFailed": "Error al importar el texto: ${message}", "unknownError": "Error des<PERSON>.", "handler": "Manejador", "error": "Error"}, "fileItem": {"removeButton": {"title": "Eliminar"}, "removeButtonIcon": {"alt": "Eliminar"}}}, "aisource_editor": {"title": "Editor de Fuentes de IA", "fileList": {"title": "Lista de Fuentes de IA", "addButton": {"title": "+"}}, "configTitle": "Configuración de Fuente de IA", "generatorSelect": {"label": "Seleccionar generador", "placeholder": "Por favor, selecciona"}, "buttons": {"save": "Guardar", "delete": "Eliminar"}, "alerts": {"fetchFileListFailed": "Error al obtener la lista de archivos: ${error}", "fetchGeneratorListFailed": "Error al obtener la lista de generadores: ${error}", "fetchFileDataFailed": "Error al obtener los datos del archivo: ${error}", "noFileSelectedSave": "No se ha seleccionado ningún archivo para guardar.", "saveFileFailed": "Error al guardar el archivo: ${error}", "noFileSelectedDelete": "No se ha seleccionado ningún archivo para eliminar.", "deleteFileFailed": "Error al eliminar el archivo: ${error}", "invalidFileName": "El nombre del archivo no puede contener los siguientes caracteres: / \\ : * ? \" < > |", "addFileFailed": "Error al añadir el archivo: ${error}", "fetchConfigTemplateFailed": "Error al obtener la plantilla de configuración.", "noGeneratorSelectedSave": "Por favor, selecciona un generador antes de guardar."}, "confirm": {"unsavedChanges": "Tienes cambios sin guardar. ¿Quieres descartarlos?", "deleteFile": "¿Seguro que quieres eliminar el archivo?", "unsavedChangesBeforeUnload": "Tienes cambios sin guardar. ¿Se<PERSON>ro que quieres salir de esta página?"}, "prompts": {"newFileName": "Introduce un nuevo nombre para el archivo de fuente de IA (sin extensión):"}, "editor": {"disabledIndicator": "Por favor, selecciona primero un generador."}}, "part_config": {"title": "Configuración de Componente", "pageTitle": "Configuración de Componente", "labels": {"partType": "Seleccionar tipo de componente", "part": "Seleccionar componente"}, "placeholders": {"partTypeSelect": "Por favor, selecciona", "partSelect": "Por favor, selecciona"}, "editor": {"title": "Configuración de Componente", "disabledIndicator": "Este componente no admite configuración.", "buttons": {"save": "Guardar"}}, "errorMessage": {"icon": {"alt": "Aviso de error"}}, "alerts": {"fetchPartTypesFailed": "Error al obtener los tipos de componente.", "fetchPartsFailed": "Error al obtener la lista de componentes.", "loadEditorFailed": "Error al cargar el editor.", "saveConfigFailed": "Error al guardar la configuración del componente.", "unsavedChanges": "Tienes cambios sin guardar. ¿Quieres descartarlos?", "beforeUnload": "Tienes cambios sin guardar. ¿Se<PERSON>ro que quieres salir?"}}, "uninstall": {"title": "<PERSON><PERSON><PERSON><PERSON>", "titleWithName": "Desinstalar ${type}/${name}", "confirmMessage": "¿Estás seguro de que quieres desinstalar ${type}: ${name}?", "invalidParamsTitle": "Parámetros <PERSON>", "infoMessage": {"icon": {"alt": "Icono de información"}}, "errorMessage": {"icon": {"alt": "Icono de error"}}, "buttons": {"confirm": "Confirmar desinstalación", "cancel": "<PERSON><PERSON><PERSON>", "back": "Volver"}, "alerts": {"success": "${type}: ${name} desinstalado correctamente.", "failed": "Error al desinstalar: ${error}", "invalidParams": "Parámetros de solicitud no válidos.", "httpError": "¡Error HTTP! Código de estado: ${status}"}}, "chat": {"new": {"title": "Nuevo chat"}, "title": "Cha<PERSON>", "sidebar": {"world": {"icon": {"alt": "Icono de mundo"}, "title": "Mundo"}, "persona": {"icon": {"alt": "Icono de persona de usuario"}, "title": "Persona del usuario"}, "charList": {"icon": {"alt": "Icono de lista de personajes"}, "title": "Lista de personajes", "buttons": {"addChar": {"title": "<PERSON><PERSON><PERSON>"}, "addCharIcon": {"alt": "Icono de añadir personaje"}}}, "noSelection": "<PERSON>da se<PERSON>", "noDescription": "No hay descripción disponible."}, "chatArea": {"title": "Cha<PERSON>", "menuButton": {"title": "Menú"}, "menuButtonIcon": {"alt": "Icono de menú"}, "input": {"placeholder": "Escribe un mensaje...\\nCtrl+Enter para enviar"}, "sendButton": {"title": "Enviar"}, "sendButtonIcon": {"alt": "Icono de enviar"}, "uploadButton": {"title": "Subir"}, "uploadButtonIcon": {"alt": "Icono de subir"}, "voiceButton": {"title": "Voz"}, "voiceButtonIcon": {"alt": "Icono de voz"}, "photoButton": {"title": "Foto"}, "photoButtonIcon": {"alt": "Icono de foto"}}, "rightSidebar": {"title": "Detalles"}, "messageList": {"confirmDeleteMessage": "¿Eliminar este mensaje?"}, "voiceRecording": {"errorAccessingMicrophone": "Error al acceder al micrófono."}, "messageView": {"buttons": {"edit": {"title": "<PERSON><PERSON>"}, "editIcon": {"alt": "Icono de editar"}, "delete": {"title": "Eliminar"}, "deleteIcon": {"alt": "Icono de eliminar"}}}, "messageEdit": {"input": {"placeholder": "Introduce contenido..."}, "buttons": {"confirm": {"title": "Confirmar"}, "confirmIcon": {"alt": "Icono de <PERSON>ar"}, "cancel": {"title": "<PERSON><PERSON><PERSON>"}, "cancelIcon": {"alt": "Icono de cancelar"}, "upload": {"title": "Subir"}, "uploadIcon": {"alt": "Icono de subir"}}}, "attachment": {"buttons": {"download": {"title": "<PERSON><PERSON><PERSON>"}, "downloadIcon": {"alt": "Icono de descarga"}, "delete": {"title": "Eliminar"}, "deleteIcon": {"alt": "Icono de eliminar"}}}, "charCard": {"frequencyLabel": "Frecuencia", "buttons": {"removeChar": {"title": "Eliminar del chat"}, "removeCharIcon": {"alt": "Icono de eliminar personaje"}, "forceReply": {"title": "<PERSON><PERSON> respuesta"}, "forceReplyIcon": {"alt": "Icono de forzar respuesta"}}}}, "chat_history": {"title": "Historial de chats", "pageTitle": "Historial de chats", "sortOptions": {"time_desc": "<PERSON><PERSON> descendente", "time_asc": "<PERSON><PERSON> ascendente"}, "filterInput": {"placeholder": "Buscar..."}, "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "buttons": {"reverseSelect": "Invertir selección", "deleteSelected": "Eliminar seleccionados", "exportSelected": "Exportar seleccionados"}, "confirmDeleteChat": "¿Seguro que quieres eliminar el historial de chat con ${chars}?", "confirmDeleteMultiChats": "¿Seguro que quieres eliminar los ${count} historiales de chat seleccionados?", "alerts": {"noChatSelectedForDeletion": "Por favor, selecciona los historiales de chat que quieres eliminar.", "noChatSelectedForExport": "Por favor, selecciona los historiales de chat que quieres exportar.", "copyError": "Copia fallida", "deleteError": "Falló la eliminación", "exportError": "Exportación fallida"}, "chatItemButtons": {"continue": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copiar", "export": "Exportar", "delete": "Eliminar"}}, "discord_bots": {"title": "<PERSON><PERSON> Discord", "cardTitle": "<PERSON><PERSON> Discord", "buttons": {"newBot": "Nuevo", "deleteBot": "Eliminar"}, "configCard": {"title": "Configuración del Bot", "labels": {"character": "<PERSON><PERSON><PERSON>", "apiKey": "Clave de API de Discord", "config": "Configuración"}, "charSelectPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>je", "apiKeyInput": {"placeholder": "Introduce la clave de API de Discord"}, "toggleApiKeyIcon": {"alt": "Alternar visibilidad de la clave de API"}, "buttons": {"saveConfig": "Guardar Configuración", "startBot": "Iniciar", "stopBot": "Detener"}}, "prompts": {"newBotName": "Introduce un nuevo nombre para el bot:"}, "alerts": {"botExists": "Ya existe un bot con el nombre \"${botname}\". Por favor, usa otro nombre.", "unsavedChanges": "Tienes cambios sin guardar. ¿Quieres descartarlos?", "configSaved": "Configuración guardada correctamente.", "httpError": "Error HTTP.", "beforeUnload": "Tienes cambios sin guardar. ¿Se<PERSON>ro que quieres salir?"}}, "telegram_bots": {"title": "Bots de Telegram", "cardTitle": "Gestión de Bots de Telegram", "buttons": {"newBot": "Nuevo", "deleteBot": "Eliminar"}, "configCard": {"title": "Configuración de Bot", "labels": {"character": "<PERSON><PERSON><PERSON> vincula<PERSON>", "botToken": "Token de Bot de Telegram", "config": "Configuración"}, "charSelectPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>je", "botTokenInput": {"placeholder": "Introduce el Token de Bot de Telegram"}, "toggleBotTokenIcon": {"alt": "Alternar visibilidad del token del bot"}, "buttons": {"saveConfig": "Guardar Configuración", "startBot": "Iniciar", "stopBot": "Detener"}}, "prompts": {"newBotName": "Introduce un nuevo nombre para el bot:"}, "alerts": {"botExists": "Ya existe un bot con el nombre \"${botname}\". Por favor, usa un nombre diferente.", "unsavedChanges": "Tienes cambios sin guardar. ¿Se<PERSON>ro que quieres descartarlos?", "configSaved": "¡La configuración se ha guardado correctamente!", "httpError": "Error HTTP.", "beforeUnload": "Tienes cambios sin guardar. ¿Se<PERSON>ro que quieres salir de la página actual?"}}, "terminal_assistant": {"title": "Asistente de Terminal", "initialMessage": "¡Fount te permite usar tus personajes favoritos en la terminal para ayudarte a programar!", "initialMessageLink": "Haz clic aquí para más información."}, "access": {"title": "Acceder a Fount en otros dispositivos", "heading": "¿Quieres acceder a Fount desde otros dispositivos?", "instruction": {"sameLAN": "Asegúrate de que el dispositivo y el host de Fount están en la misma red local.", "accessthis": "Visita esta URL:"}, "copyButton": "Copiar URL", "copied": "¡URL copiada al portapapeles!"}, "proxy": {"title": "Proxy de API", "heading": "Dirección del Proxy de API de OpenAI", "instruction": "¡Introduce la siguiente dirección en cualquier aplicación que requiera el formato de API de OpenAI para usar las fuentes de IA en Fount!", "copyButton": "<PERSON><PERSON><PERSON>", "copied": "¡Dirección copiada al portapapeles!"}, "404": {"title": "Página no encontrada", "pageNotFoundText": "¡Vaya! Parece que has llegado a una página que no existe.", "homepageButton": "Volver a la página de inicio", "MineSweeper": {"difficultyLabel": "Dificultad:", "difficultyEasy": "F<PERSON><PERSON>l", "difficultyMedium": "Media", "difficultyHard": "Dif<PERSON><PERSON>l", "difficultyCustom": "Personalizada", "minesLeftLabel": "Minas restantes:", "timeLabel": "Tiempo:", "restartButton": "Reiniciar", "rowsLabel": "Filas:", "colsLabel": "Columnas:", "minesCountLabel": "Nº de minas:", "winMessage": "¡Enhorabuena, has ganado!", "loseMessage": "¡Fin del juego, has pisado una mina!", "soundOn": "Sonido activado", "soundOff": "Sonido desactivado"}}, "userSettings": {"title": "Ajustes de usuario", "PageTitle": "Ajustes de usuario", "apiError": "Error en la solicitud de API: ${message}", "generalError": "Ha ocurrido un error: ${message}", "userInfo": {"title": "Información del usuario", "usernameLabel": "Nombre de usuario:", "creationDateLabel": "Fecha de creación de la cuenta:", "folderSizeLabel": "Tamaño de datos de usuario:", "folderPathLabel": "Ruta de datos de usuario:", "copyPathBtnTitle": "Copiar ruta", "copiedAlert": "¡Ruta copiada al portapapeles!"}, "changePassword": {"title": "Cambiar contraseña", "currentPasswordLabel": "Contraseña actual:", "newPasswordLabel": "Nueva contraseña:", "confirmNewPasswordLabel": "Confirmar nueva contraseña:", "submitButton": "Cambiar contraseña", "errorMismatch": "Las nuevas contraseñas no coinciden.", "success": "Contraseña cambiada correctamente."}, "renameUser": {"title": "Cambiar nombre de usuario", "newUsernameLabel": "Nuevo nombre de usuario:", "submitButton": "Cambiar nombre de usuario", "confirmMessage": "¿Seguro que quieres cambiar tu nombre de usuario? Esto requerirá que vuelvas a iniciar sesión.", "success": "Nombre de usuario cambiado a \"${newUsername}\" correctamente. Ahora se cerrará tu sesión."}, "userDevices": {"title": "Dispositivos/sesiones de usuario", "refreshButtonTitle": "Actualizar lista", "noDevicesFound": "No se han encontrado dispositivos ni sesiones.", "deviceInfo": "ID del dispositivo: ${deviceId}", "thisDevice": "Este dispositivo", "deviceDetails": "Última conexión: ${lastSeen} | IP: ${ipAddress} | UA: ${userAgent}", "revokeButton": "Revocar", "revokeConfirm": "¿Seguro que quieres revocar el acceso para este dispositivo/sesión?", "revokeSuccess": "Dispositivo/sesión revocado correctamente."}, "logout": {"title": "<PERSON><PERSON><PERSON>", "description": "Esto cerrará la sesión de tu cuenta en el dispositivo actual.", "buttonText": "<PERSON><PERSON><PERSON>", "confirmMessage": "¿Seguro que quieres cerrar la sesión?", "successMessage": "Sesión cerrada correctamente. Redirigiendo a la página de inicio de sesión..."}, "deleteAccount": {"title": "Eliminar cuenta", "warning": "Atención: Esta acción eliminará tu cuenta y todos tus datos de forma permanente y es irreversible.", "submitButton": "Eliminar mi cuenta", "confirmMessage1": "¡Atención! ¿Seguro que quieres eliminar tu cuenta permanentemente? Esta acción no se puede deshacer.", "confirmMessage2": "Para confirmar la eliminación, introduce tu nombre de usuario \"${username}\":", "usernameMismatch": "El nombre de usuario introducido no coincide con el actual. Se ha cancelado la eliminación.", "success": "Cuenta eliminada correctamente. Ahora se cerrará tu sesión."}, "passwordConfirm": {"title": "Confirmar operación", "message": "Para continuar, introduce tu contraseña actual:", "passwordLabel": "Contraseña:", "confirmButton": "Confirmar", "cancelButton": "<PERSON><PERSON><PERSON>"}}}