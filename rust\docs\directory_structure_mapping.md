# Fount项目目录结构映射文档

## 原始JavaScript/TypeScript项目结构

```
fount/
├── LICENSE                          # 许可证文件
├── readme.md                        # 项目说明文档
├── deno.json                        # Deno配置文件
├── docker-compose.yml               # Docker Compose配置
├── dockerfile                       # Docker构建文件
├── eslint.config.mjs                # ESLint配置
├── run.bat                          # Windows启动脚本
├── run.sh                           # Linux/macOS启动脚本
├── default/                         # 默认配置目录
│   ├── config.json                  # 默认配置文件
│   └── templates/                   # 默认模板目录
├── docs/                            # 多语言文档目录
│   ├── Readme.zh-CN.md             # 中文文档
│   ├── Readme.en-UK.md             # 英文文档
│   └── [其他语言文档...]
├── imgs/                            # 图标和图片资源
│   ├── icon.svg                     # 主图标
│   ├── icon.png                     # PNG图标
│   └── [其他图标文件...]
├── path/                            # 路径和启动脚本
│   ├── fount                        # 主启动脚本
│   ├── fount.bat                    # Windows批处理脚本
│   ├── fount.ps1                    # PowerShell脚本
│   └── fount.sh                     # Shell脚本
└── src/                             # 源代码目录
    ├── decl/                        # TypeScript类型定义
    │   ├── AIsource.ts              # AI源类型定义
    │   ├── AIsourceGeneretor.ts     # AI源生成器类型定义
    │   ├── UserAPI.ts               # 用户API类型定义
    │   ├── WorldAPI.ts              # 世界API类型定义
    │   ├── basedefs.ts              # 基础类型定义
    │   ├── charAPI.ts               # 角色API类型定义
    │   ├── importHandlerAPI.ts      # 导入处理器API类型定义
    │   ├── pluginAPI.ts             # 插件API类型定义
    │   ├── prompt_struct.ts         # 提示结构类型定义
    │   └── shellAPI.ts              # Shell API类型定义
    ├── locales/                     # 国际化语言文件
    │   ├── zh-CN.json               # 中文语言包
    │   ├── en-UK.json               # 英文语言包
    │   └── [其他语言包...]
    ├── public/                      # 公共前端资源
    │   ├── 404.html                 # 404错误页面
    │   ├── base.css                 # 基础样式文件
    │   ├── base.mjs                 # 基础JavaScript模块
    │   ├── favicon.ico              # 网站图标
    │   ├── favicon.png              # PNG网站图标
    │   ├── index.html               # 主页面
    │   ├── service_worker.mjs       # Service Worker
    │   ├── AIsourceGenerators/      # AI源生成器插件
    │   │   ├── blackbox/            # Blackbox AI生成器
    │   │   ├── claude/              # Claude AI生成器
    │   │   ├── cohere/              # Cohere AI生成器
    │   │   ├── gemini/              # Gemini AI生成器
    │   │   └── [其他AI生成器...]
    │   ├── ImportHandlers/          # 导入处理器
    │   │   ├── Risu/                # Risu导入处理器
    │   │   ├── SillyTavern/         # SillyTavern导入处理器
    │   │   └── fount/               # Fount原生导入处理器
    │   ├── login/                   # 登录页面
    │   │   ├── index.html           # 登录页面HTML
    │   │   ├── index.css            # 登录页面样式
    │   │   └── index.mjs            # 登录页面脚本
    │   ├── protocolhandler/         # 协议处理器
    │   │   ├── index.html           # 协议处理页面
    │   │   ├── index.css            # 协议处理样式
    │   │   └── index.mjs            # 协议处理脚本
    │   ├── scripts/                 # 前端脚本库
    │   │   ├── cssValues.mjs        # CSS值处理
    │   │   ├── do_notification.mjs  # 通知处理
    │   │   ├── endpoints.mjs        # API端点
    │   │   ├── i18n.mjs             # 国际化
    │   │   ├── jsoneditor.mjs       # JSON编辑器
    │   │   ├── markdown.mjs         # Markdown处理
    │   │   ├── parts.mjs            # 组件管理
    │   │   ├── regex.mjs            # 正则表达式工具
    │   │   ├── template.mjs         # 模板处理
    │   │   ├── theme.mjs            # 主题管理
    │   │   └── [其他工具脚本...]
    │   ├── shells/                  # Shell插件系统
    │   │   ├── AIsourceManage/      # AI源管理Shell
    │   │   ├── access/              # 访问控制Shell
    │   │   ├── chat/                # 聊天Shell
    │   │   ├── config/              # 配置Shell
    │   │   ├── discordbot/          # Discord机器人Shell
    │   │   ├── home/                # 主页Shell
    │   │   ├── install/             # 安装Shell
    │   │   ├── proxy/               # 代理Shell
    │   │   ├── telegrambot/         # Telegram机器人Shell
    │   │   ├── themeManage/         # 主题管理Shell
    │   │   ├── tutorial/            # 教程Shell
    │   │   └── user_settings/       # 用户设置Shell
    │   └── template/                # 模板文件
    │       ├── chat/                # 聊天模板
    │       ├── home/                # 主页模板
    │       └── [其他模板...]
    ├── runner/                      # 运行器脚本
    │   ├── background.ps1           # 后台运行PowerShell脚本
    │   ├── main.ps1                 # 主PowerShell脚本
    │   └── main.sh                  # 主Shell脚本
    ├── scripts/                     # 后端脚本工具
    │   ├── await_timeout.mjs        # 超时等待工具
    │   ├── console.mjs              # 控制台工具
    │   ├── discordrpc.mjs           # Discord RPC
    │   ├── env.mjs                  # 环境变量处理
    │   ├── escape.mjs               # 转义工具
    │   ├── exec.mjs                 # 执行工具
    │   ├── i18n.mjs                 # 国际化工具
    │   ├── json_loader.mjs          # JSON加载器
    │   ├── locale.mjs               # 本地化工具
    │   ├── ms.mjs                   # 时间处理工具
    │   ├── notify.mjs               # 通知工具
    │   ├── proxy.mjs                # 代理工具
    │   ├── ratelimit.mjs            # 速率限制
    │   ├── sentrytunnel.mjs         # Sentry隧道
    │   ├── tray.mjs                 # 系统托盘
    │   └── verifycode.mjs           # 验证码工具
    └── server/                      # 服务器核心模块
        ├── auth.mjs                 # 认证模块
        ├── base.mjs                 # 基础模块
        ├── endpoints.mjs            # API端点
        ├── events.mjs               # 事件系统
        ├── index.mjs                # 服务器入口
        ├── ipc_server.mjs           # IPC服务器
        ├── jobs.mjs                 # 任务管理
        ├── on_shutdown.mjs          # 关闭处理
        ├── parts_loader.mjs         # 插件加载器
        ├── server.mjs               # 主服务器
        ├── setting_loader.mjs       # 设置加载器
        ├── timers.mjs               # 定时器
        └── managers/                # 管理器模块
            ├── index.mjs            # 管理器入口
            ├── AIsources_manager.mjs # AI源管理器
            ├── char_manager.mjs     # 角色管理器
            ├── personas_manager.mjs # 人格管理器
            ├── shell_manager.mjs    # Shell管理器
            └── world_manager.mjs    # 世界管理器
```

## 对应的Rust项目结构映射

```
rust/
├── Cargo.toml                      # Workspace配置文件
├── README.md                       # 项目说明文档
├── LICENSE                         # 许可证文件（复制）
├── docker-compose.yml              # Docker Compose配置（适配）
├── Dockerfile                      # Docker构建文件（适配）
├── build.rs                        # 构建脚本（如需要）
├── run.bat                         # Windows启动脚本（适配）
├── run.sh                          # Linux/macOS启动脚本（适配）
├── default/                        # 默认配置目录（复制）
│   ├── config.json                 # 默认配置文件
│   └── templates/                  # 默认模板目录
├── docs/                           # 文档目录
│   ├── directory_structure_mapping.md # 目录结构映射文档
│   ├── migration_plan.md           # 迁移计划文档
│   └── rust_equivalents.md        # Rust等价物文档
├── assets/                         # 静态资源目录
│   ├── icons/                      # 图标文件（从imgs/复制）
│   └── locales/                    # 国际化文件（从src/locales/复制）
├── crates/                         # Crate目录
│   ├── fount-types/                # 类型定义crate（对应src/decl/）
│   │   ├── Cargo.toml
│   │   └── src/
│   │       ├── lib.rs              # 库入口
│   │       ├── ai_source.rs        # AI源类型（对应AIsource.ts）
│   │       ├── ai_source_generator.rs # AI源生成器类型
│   │       ├── user_api.rs         # 用户API类型
│   │       ├── world_api.rs        # 世界API类型
│   │       ├── base_defs.rs        # 基础类型定义
│   │       ├── char_api.rs         # 角色API类型
│   │       ├── import_handler_api.rs # 导入处理器API类型
│   │       ├── plugin_api.rs       # 插件API类型
│   │       ├── prompt_struct.rs    # 提示结构类型
│   │       └── shell_api.rs        # Shell API类型
│   ├── fount-utils/                # 工具库crate（对应src/scripts/）
│   │   ├── Cargo.toml
│   │   └── src/
│   │       ├── lib.rs              # 库入口
│   │       ├── console.rs          # 控制台工具
│   │       ├── json_loader.rs      # JSON加载器
│   │       ├── i18n.rs             # 国际化工具
│   │       ├── exec.rs             # 执行工具
│   │       ├── proxy.rs            # 代理工具
│   │       ├── ratelimit.rs        # 速率限制
│   │       ├── ms.rs               # 时间处理工具
│   │       ├── escape.rs           # 转义工具
│   │       ├── await_timeout.rs    # 超时等待工具
│   │       ├── notify.rs           # 通知工具
│   │       ├── tray.rs             # 系统托盘
│   │       ├── discord_rpc.rs      # Discord RPC
│   │       ├── env.rs              # 环境变量处理
│   │       ├── locale.rs           # 本地化工具
│   │       ├── verify_code.rs      # 验证码工具
│   │       └── sentry_tunnel.rs    # Sentry隧道
│   ├── fount-server/               # 服务器核心crate（对应src/server/）
│   │   ├── Cargo.toml
│   │   └── src/
│   │       ├── lib.rs              # 库入口
│   │       ├── main.rs             # 服务器入口（对应index.mjs）
│   │       ├── auth.rs             # 认证模块
│   │       ├── base.rs             # 基础模块
│   │       ├── server.rs           # 主服务器
│   │       ├── endpoints.rs        # API端点
│   │       ├── events.rs           # 事件系统
│   │       ├── ipc_server.rs       # IPC服务器
│   │       ├── jobs.rs             # 任务管理
│   │       ├── on_shutdown.rs      # 关闭处理
│   │       ├── parts_loader.rs     # 插件加载器
│   │       ├── setting_loader.rs   # 设置加载器
│   │       ├── timers.rs           # 定时器
│   │       └── managers/           # 管理器模块
│   │           ├── mod.rs          # 管理器模块入口
│   │           ├── ai_sources_manager.rs # AI源管理器
│   │           ├── char_manager.rs # 角色管理器
│   │           ├── personas_manager.rs # 人格管理器
│   │           ├── shell_manager.rs # Shell管理器
│   │           └── world_manager.rs # 世界管理器
│   └── fount-desktop/              # 桌面应用crate（Tauri应用）
│       ├── Cargo.toml
│       ├── tauri.conf.json         # Tauri配置
│       └── src/
│           ├── main.rs             # 桌面应用入口
│           └── lib.rs              # 桌面应用库
├── static/                         # 静态文件目录（对应src/public/）
│   ├── 404.html                    # 404错误页面
│   ├── base.css                    # 基础样式文件
│   ├── base.mjs                    # 基础JavaScript模块
│   ├── favicon.ico                 # 网站图标
│   ├── favicon.png                 # PNG网站图标
│   ├── index.html                  # 主页面
│   ├── service_worker.mjs          # Service Worker
│   ├── login/                      # 登录页面
│   ├── protocolhandler/            # 协议处理器
│   ├── scripts/                    # 前端脚本库
│   ├── shells/                     # Shell插件系统
│   └── template/                   # 模板文件
├── plugins/                        # 插件目录
│   ├── ai_source_generators/       # AI源生成器插件
│   ├── import_handlers/            # 导入处理器
│   └── shells/                     # Shell插件
└── scripts/                        # 构建和部署脚本
    ├── build.rs                    # 构建脚本
    ├── install.sh                  # 安装脚本
    ├── uninstall.sh                # 卸载脚本
    └── deploy.sh                   # 部署脚本
```

## 文件映射关系

### 核心类型定义映射
- `src/decl/*.ts` → `crates/fount-types/src/*.rs`
- TypeScript接口 → Rust trait + struct
- TypeScript类型别名 → Rust type alias
- TypeScript枚举 → Rust enum

### 服务器模块映射
- `src/server/*.mjs` → `crates/fount-server/src/*.rs`
- Express.js路由 → Axum路由
- JavaScript异步函数 → Rust async fn
- Node.js模块导入 → Rust use语句

### 工具库映射
- `src/scripts/*.mjs` → `crates/fount-utils/src/*.rs`
- JavaScript工具函数 → Rust函数
- Node.js API调用 → 对应的Rust crate调用

### 前端资源映射
- `src/public/*` → `static/*`
- 保持相同的目录结构和文件名
- JavaScript模块保持不变（前端继续使用JavaScript）

### 配置文件映射
- `deno.json` → `Cargo.toml`
- `package.json`依赖 → `Cargo.toml`依赖
- 环境配置保持JSON格式不变

## TypeScript类型定义文件分析

### 核心类型文件列表
1. **basedefs.ts** - 基础类型定义
   - `timeStamp_t`: 时间戳类型 (number)
   - `locale_t`: 本地化字符串类型 (string)
   - `role_t`: 角色类型枚举 ('user' | 'char' | 'system' | 'world' | 'plugin')
   - `info_t`: 信息结构体，包含多语言支持

2. **charAPI.ts** - 角色API类型定义
   - `charState_t`: 角色状态类
   - `charInit_t`: 角色初始化参数类
   - `charAPI_t`: 角色API主接口，包含生命周期方法和多种接口

3. **AIsource.ts** - AI源类型定义
   - `tokenizer_t`: 分词器泛型类
   - `AIsource_t`: AI源泛型类
   - `textAISource_t`: 文本AI源类（继承自AIsource_t）

4. **prompt_struct.ts** - 提示结构类型定义
   - `single_part_prompt_t`: 单部分提示类
   - `other_chars_prompt_t`: 其他角色提示类
   - `chatLogEntry_t`: 聊天日志条目类型
   - `prompt_struct_t`: 完整提示结构类型

5. **WorldAPI.ts** - 世界API类型定义
   - `WorldAPI_t`: 世界API主接口，类似charAPI_t结构

6. **UserAPI.ts** - 用户API类型定义
7. **AIsourceGeneretor.ts** - AI源生成器类型定义
8. **importHandlerAPI.ts** - 导入处理器API类型定义
9. **pluginAPI.ts** - 插件API类型定义
10. **shellAPI.ts** - Shell API类型定义

### Rust类型映射策略
- TypeScript `class` → Rust `struct`
- TypeScript `interface` → Rust `trait`
- TypeScript 联合类型 → Rust `enum`
- TypeScript 泛型 → Rust 泛型
- TypeScript `Promise<T>` → Rust `Future<Output = T>`
- TypeScript 可选字段 → Rust `Option<T>`
- TypeScript `Record<K, V>` → Rust `HashMap<K, V>`
