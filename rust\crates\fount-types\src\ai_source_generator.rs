//! AI源生成器类型定义
//! 对应原文件: src/decl/AIsourceGeneretor.ts

use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// AI源生成器
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AiSourceGenerator {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub version: String,
    pub author: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 生成器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeneratorConfig {
    pub template: String,
    pub parameters: serde_json::Value,
}

/// 生成请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerateRequest {
    pub generator_id: Uuid,
    pub config: GeneratorConfig,
}
