# Rust系统托盘库选型分析

## 系统托盘需求概览

Fount项目需要系统托盘功能来提供后台运行、快速访问和状态显示。我们需要选择一个跨平台、功能完整、易于集成的系统托盘库。

## 候选库分析

### 1. tray-icon
**开发者**: Tauri团队
**版本**: 0.14.x (活跃开发)
**设计理念**: 现代、跨平台、功能丰富

#### 核心特性
- **跨平台**: Windows, macOS, Linux全支持
- **现代API**: 基于事件驱动的现代API
- **菜单支持**: 丰富的上下文菜单
- **图标管理**: 支持多种图标格式
- **Tauri集成**: 与Tauri深度集成

#### API示例
```rust
use tray_icon::{TrayIcon, TrayIconBuilder, menu::{Menu, MenuItem}};

// 创建托盘菜单
let open_item = MenuItem::new("Open Fount", true, None);
let settings_item = MenuItem::new("Settings", true, None);
let quit_item = MenuItem::new("Quit", true, None);

let menu = Menu::new();
menu.append(&open_item)?;
menu.append(&settings_item)?;
menu.append_separator()?;
menu.append(&quit_item)?;

// 创建托盘图标
let tray = TrayIconBuilder::new()
    .with_menu(Box::new(menu))
    .with_tooltip("Fount - AI Character Platform")
    .with_icon(icon)
    .build()?;
```

#### 优势
- **最新技术**: 使用最新的系统API
- **活跃维护**: Tauri团队积极维护
- **功能完整**: 支持所有现代托盘功能
- **文档良好**: 清晰的文档和示例

### 2. systray (已废弃)
**状态**: 不再维护
**问题**: 依赖过时、API陈旧、跨平台问题

### 3. ksni
**开发者**: PolyMeilex
**版本**: 0.2.x (Linux专用)
**设计理念**: Linux StatusNotifierItem协议

#### 核心特性
- **Linux专用**: 专门为Linux设计
- **现代协议**: 使用StatusNotifierItem协议
- **DBus集成**: 与DBus深度集成

#### API示例
```rust
use ksni::{TrayService, MenuItem};

struct FountTray;

impl TrayService for FountTray {
    fn activate(&mut self, _x: i32, _y: i32) {
        // 点击托盘图标
    }
    
    fn context_menu(&self) -> Vec<MenuItem<Self>> {
        vec![
            MenuItem::Standard {
                label: "Open".into(),
                activate: Box::new(|_| {
                    // 打开应用
                }),
            },
            MenuItem::Separator,
            MenuItem::Standard {
                label: "Quit".into(),
                activate: Box::new(|_| {
                    // 退出应用
                }),
            },
        ]
    }
}
```

#### 劣势
- **平台限制**: 仅支持Linux
- **复杂性**: DBus集成复杂

### 4. 原生平台API
**方式**: 直接调用系统API
**复杂性**: 需要为每个平台单独实现

#### Windows实现
```rust
// Windows Shell_NotifyIcon API
use winapi::um::shellapi::{Shell_NotifyIconW, NOTIFYICONDATAW};

struct WindowsTray {
    nid: NOTIFYICONDATAW,
}

impl WindowsTray {
    pub fn new() -> Result<Self, TrayError> {
        // 复杂的Windows API调用
    }
}
```

#### macOS实现
```rust
// macOS NSStatusBar API
use objc::runtime::{Object, Class};

struct MacOSTray {
    status_item: *mut Object,
}
```

#### Linux实现
```rust
// Linux libappindicator或StatusNotifierItem
use gtk::prelude::*;
use libappindicator::{AppIndicator, AppIndicatorStatus};
```

## 库对比矩阵

| 特性 | tray-icon | ksni | 原生API | systray |
|------|-----------|------|---------|---------|
| **跨平台** | ⭐⭐⭐⭐⭐ | ⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **易用性** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐ | ⭐⭐⭐ |
| **功能丰富度** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **维护状态** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ❌ |
| **文档质量** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐ |
| **性能** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **集成度** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐ | ⭐⭐⭐ |

## Fount项目需求分析

### 1. 现有Node.js托盘功能
```javascript
// 使用systray库
const SysTray = require('systray').default;

const systray = new SysTray({
    menu: {
        icon: iconPath,
        title: "Fount",
        tooltip: "Fount - AI Character Platform",
        items: [
            {
                title: "Open Fount",
                tooltip: "Open the main window",
                checked: false,
                enabled: true
            },
            {
                title: "Settings",
                tooltip: "Open settings",
                checked: false,
                enabled: true
            },
            SysTray.separator,
            {
                title: "Exit",
                tooltip: "Exit the application",
                checked: false,
                enabled: true
            }
        ]
    }
});

systray.onClick(action => {
    if (action.seq_id === 0) {
        // Open Fount
        openMainWindow();
    } else if (action.seq_id === 1) {
        // Settings
        openSettings();
    } else if (action.seq_id === 3) {
        // Exit
        process.exit(0);
    }
});
```

### 2. 核心功能需求

#### 基础功能
- **托盘图标**: 显示应用图标
- **工具提示**: 鼠标悬停显示信息
- **左键点击**: 显示/隐藏主窗口
- **右键菜单**: 上下文菜单

#### 菜单功能
- **打开应用**: 显示主窗口
- **设置**: 打开设置页面
- **状态显示**: 显示运行状态
- **退出**: 优雅关闭应用

#### 高级功能
- **通知**: 系统通知集成
- **状态更新**: 动态更新图标和菜单
- **多语言**: 支持国际化菜单

## 最终选择：tray-icon

### 选择理由

#### 1. 最佳的跨平台支持
```rust
use tray_icon::{TrayIcon, TrayIconBuilder, TrayIconEvent};
use tray_icon::menu::{Menu, MenuItem, PredefinedMenuItem};

pub struct FountTray {
    tray: TrayIcon,
    menu_items: TrayMenuItems,
}

struct TrayMenuItems {
    open: MenuItem,
    settings: MenuItem,
    status: MenuItem,
    quit: MenuItem,
}

impl FountTray {
    pub fn new() -> Result<Self, TrayError> {
        // 创建菜单项
        let open = MenuItem::new("Open Fount", true, None);
        let settings = MenuItem::new("Settings", true, None);
        let status = MenuItem::new("Status: Running", false, None);
        let quit = MenuItem::new("Quit", true, None);
        
        // 创建菜单
        let menu = Menu::new();
        menu.append(&open)?;
        menu.append(&settings)?;
        menu.append_separator()?;
        menu.append(&status)?;
        menu.append_separator()?;
        menu.append(&quit)?;
        
        // 加载图标
        let icon = load_icon()?;
        
        // 创建托盘
        let tray = TrayIconBuilder::new()
            .with_menu(Box::new(menu))
            .with_tooltip("Fount - AI Character Platform")
            .with_icon(icon)
            .build()?;
            
        Ok(Self {
            tray,
            menu_items: TrayMenuItems {
                open, settings, status, quit
            },
        })
    }
    
    pub fn update_status(&self, status: &str) -> Result<(), TrayError> {
        self.menu_items.status.set_text(&format!("Status: {}", status))?;
        Ok(())
    }
    
    pub fn set_icon(&self, icon_data: &[u8]) -> Result<(), TrayError> {
        let icon = Icon::from_rgba(icon_data, 32, 32)?;
        self.tray.set_icon(Some(icon))?;
        Ok(())
    }
}
```

#### 2. 事件处理系统
```rust
use tray_icon::TrayIconEvent;
use tokio::sync::mpsc;

pub struct TrayEventHandler {
    event_receiver: mpsc::Receiver<TrayEvent>,
    app_handle: AppHandle,
}

#[derive(Debug)]
pub enum TrayEvent {
    Open,
    Settings,
    Quit,
    LeftClick,
    RightClick,
}

impl TrayEventHandler {
    pub fn new(app_handle: AppHandle) -> Self {
        let (tx, rx) = mpsc::channel(100);
        
        // 监听托盘事件
        let tx_clone = tx.clone();
        TrayIconEvent::set_event_handler(Some(move |event| {
            match event {
                TrayIconEvent::Click { button, .. } => {
                    match button {
                        tray_icon::ClickType::Left => {
                            let _ = tx_clone.try_send(TrayEvent::LeftClick);
                        }
                        tray_icon::ClickType::Right => {
                            let _ = tx_clone.try_send(TrayEvent::RightClick);
                        }
                        _ => {}
                    }
                }
                TrayIconEvent::MenuEvent { id } => {
                    let event = match id.as_ref() {
                        "open" => TrayEvent::Open,
                        "settings" => TrayEvent::Settings,
                        "quit" => TrayEvent::Quit,
                        _ => return,
                    };
                    let _ = tx_clone.try_send(event);
                }
                _ => {}
            }
        }));
        
        Self {
            event_receiver: rx,
            app_handle,
        }
    }
    
    pub async fn handle_events(&mut self) {
        while let Some(event) = self.event_receiver.recv().await {
            match event {
                TrayEvent::Open => {
                    self.app_handle.show_main_window().await;
                }
                TrayEvent::Settings => {
                    self.app_handle.show_settings().await;
                }
                TrayEvent::Quit => {
                    self.app_handle.quit().await;
                }
                TrayEvent::LeftClick => {
                    self.app_handle.toggle_main_window().await;
                }
                TrayEvent::RightClick => {
                    // 右键点击通常显示菜单，由系统自动处理
                }
            }
        }
    }
}
```

#### 3. 图标管理
```rust
use image::{ImageBuffer, Rgba};

pub struct TrayIconManager {
    default_icon: Icon,
    status_icons: HashMap<String, Icon>,
}

impl TrayIconManager {
    pub fn new() -> Result<Self, TrayError> {
        let default_icon = Self::load_icon_from_bytes(include_bytes!("../assets/icon.png"))?;
        
        let mut status_icons = HashMap::new();
        status_icons.insert("running".to_string(), 
            Self::load_icon_from_bytes(include_bytes!("../assets/icon_running.png"))?);
        status_icons.insert("error".to_string(), 
            Self::load_icon_from_bytes(include_bytes!("../assets/icon_error.png"))?);
        status_icons.insert("updating".to_string(), 
            Self::load_icon_from_bytes(include_bytes!("../assets/icon_updating.png"))?);
        
        Ok(Self {
            default_icon,
            status_icons,
        })
    }
    
    fn load_icon_from_bytes(bytes: &[u8]) -> Result<Icon, TrayError> {
        let img = image::load_from_memory(bytes)?;
        let rgba = img.to_rgba8();
        let (width, height) = rgba.dimensions();
        
        Icon::from_rgba(rgba.into_raw(), width, height)
            .map_err(|e| TrayError::IconError(e.to_string()))
    }
    
    pub fn get_status_icon(&self, status: &str) -> &Icon {
        self.status_icons.get(status).unwrap_or(&self.default_icon)
    }
    
    pub fn create_notification_icon(&self, count: u32) -> Result<Icon, TrayError> {
        // 动态创建带数字的图标
        let base_img = image::load_from_memory(include_bytes!("../assets/icon.png"))?;
        let mut img = base_img.to_rgba8();
        
        if count > 0 {
            // 在图标上绘制数字
            self.draw_badge(&mut img, count)?;
        }
        
        let (width, height) = img.dimensions();
        Icon::from_rgba(img.into_raw(), width, height)
            .map_err(|e| TrayError::IconError(e.to_string()))
    }
    
    fn draw_badge(&self, img: &mut ImageBuffer<Rgba<u8>, Vec<u8>>, count: u32) -> Result<(), TrayError> {
        // 简单的数字徽章绘制
        // 实际实现可能需要更复杂的文本渲染
        Ok(())
    }
}
```

#### 4. 国际化支持
```rust
use crate::i18n::I18nManager;

pub struct LocalizedTray {
    tray: FountTray,
    i18n: Arc<I18nManager>,
    current_locale: String,
}

impl LocalizedTray {
    pub fn new(i18n: Arc<I18nManager>) -> Result<Self, TrayError> {
        let tray = FountTray::new()?;
        let current_locale = i18n.get_current_locale();
        
        let mut localized_tray = Self {
            tray,
            i18n,
            current_locale,
        };
        
        localized_tray.update_menu_text()?;
        Ok(localized_tray)
    }
    
    pub fn update_locale(&mut self, locale: &str) -> Result<(), TrayError> {
        if self.current_locale != locale {
            self.current_locale = locale.to_string();
            self.update_menu_text()?;
        }
        Ok(())
    }
    
    fn update_menu_text(&self) -> Result<(), TrayError> {
        let open_text = self.i18n.get_text(&self.current_locale, "tray.open")
            .unwrap_or("Open Fount");
        let settings_text = self.i18n.get_text(&self.current_locale, "tray.settings")
            .unwrap_or("Settings");
        let quit_text = self.i18n.get_text(&self.current_locale, "tray.quit")
            .unwrap_or("Quit");
        let tooltip_text = self.i18n.get_text(&self.current_locale, "tray.tooltip")
            .unwrap_or("Fount - AI Character Platform");
        
        self.tray.menu_items.open.set_text(open_text)?;
        self.tray.menu_items.settings.set_text(settings_text)?;
        self.tray.menu_items.quit.set_text(quit_text)?;
        self.tray.tray.set_tooltip(Some(tooltip_text))?;
        
        Ok(())
    }
}
```

#### 5. 与应用集成
```rust
pub struct AppTrayManager {
    tray: LocalizedTray,
    event_handler: TrayEventHandler,
    icon_manager: TrayIconManager,
    notification_count: Arc<AtomicU32>,
}

impl AppTrayManager {
    pub async fn new(app_handle: AppHandle, i18n: Arc<I18nManager>) -> Result<Self, TrayError> {
        let tray = LocalizedTray::new(i18n)?;
        let event_handler = TrayEventHandler::new(app_handle);
        let icon_manager = TrayIconManager::new()?;
        let notification_count = Arc::new(AtomicU32::new(0));
        
        Ok(Self {
            tray,
            event_handler,
            icon_manager,
            notification_count,
        })
    }
    
    pub async fn start(&mut self) {
        // 启动事件处理循环
        tokio::spawn(async move {
            self.event_handler.handle_events().await;
        });
    }
    
    pub fn update_status(&self, status: &str) -> Result<(), TrayError> {
        self.tray.tray.update_status(status)?;
        
        // 更新图标
        let icon = self.icon_manager.get_status_icon(status);
        self.tray.tray.set_icon_from_icon(icon)?;
        
        Ok(())
    }
    
    pub fn add_notification(&self) -> Result<(), TrayError> {
        let count = self.notification_count.fetch_add(1, Ordering::Relaxed) + 1;
        let icon = self.icon_manager.create_notification_icon(count)?;
        self.tray.tray.set_icon_from_icon(&icon)?;
        Ok(())
    }
    
    pub fn clear_notifications(&self) -> Result<(), TrayError> {
        self.notification_count.store(0, Ordering::Relaxed);
        let icon = self.icon_manager.get_status_icon("running");
        self.tray.tray.set_icon_from_icon(icon)?;
        Ok(())
    }
}
```

### 错误处理

```rust
#[derive(Debug, thiserror::Error)]
pub enum TrayError {
    #[error("Tray icon creation failed: {0}")]
    CreationFailed(String),
    
    #[error("Icon loading failed: {0}")]
    IconError(String),
    
    #[error("Menu operation failed: {0}")]
    MenuError(String),
    
    #[error("Platform not supported")]
    PlatformNotSupported,
    
    #[error("Image processing error: {0}")]
    ImageError(#[from] image::ImageError),
}
```

这个选择确保了我们能够在所有主要平台上提供一致、现代的系统托盘体验，同时保持代码的简洁性和可维护性。
