[package]
name = "fount-utils"
version = "0.1.0"
edition = "2021"
description = "Utility functions for Fount"
license = "MIT"
authors = ["Fount Team"]
repository = "https://github.com/steve02081504/fount"

[dependencies]
# 内部依赖
fount-types = { workspace = true }

# 核心
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }

# 异步
async-trait = { workspace = true }
futures = { workspace = true }

# 日志
tracing = { workspace = true }

# 时间
chrono = { workspace = true }
humantime = { workspace = true }

# 文件系统
fs-extra = { workspace = true }
dirs = { workspace = true }
notify = { workspace = true }

# 网络
reqwest = { workspace = true }

# 系统集成
notify-rust = "4.10.0"
tray-icon = "0.14.3"

# 进程间通信
interprocess = "1.2.1"

# 终端和控制台
crossterm = "0.27.0"

# 国际化
fluent = "0.16.0"
fluent-bundle = "0.15.2"
unic-langid = "0.9.4"

# 正则表达式
regex = { workspace = true }

# 环境检测
sysinfo = "0.30.5"

# 速率限制
governor = "0.6.0"
nonzero_ext = "0.3.0"

# Discord RPC
discord-rich-presence = "0.2.3"

# 验证码
captcha = "0.0.9"

# Sentry
sentry = { version = "0.32.1", features = ["tracing", "debug-images"] }

# 压缩
flate2 = { workspace = true }
zip = { workspace = true }

# 图像处理
image = { workspace = true }

# 配置
config = { workspace = true }

# 加密
bcrypt = { workspace = true }
rand = { workspace = true }

# 数据结构
dashmap = { workspace = true }
