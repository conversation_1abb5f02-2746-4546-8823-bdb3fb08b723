//! Shell API类型定义
//! 对应原文件: src/decl/shellAPI.ts

use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// Shell配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShellConfig {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub script_path: String,
    pub enabled: bool,
}

/// Shell执行请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShellExecuteRequest {
    pub shell_id: Uuid,
    pub command: String,
    pub args: Vec<String>,
}
