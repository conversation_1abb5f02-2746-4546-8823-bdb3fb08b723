//! 进程执行工具
//! 对应原文件: src/scripts/exec.mjs

use tokio::process::Command;
use anyhow::Result;

/// 执行命令
pub async fn execute_command(command: &str, args: &[&str]) -> Result<String> {
    let output = Command::new(command)
        .args(args)
        .output()
        .await?;
    
    if output.status.success() {
        Ok(String::from_utf8_lossy(&output.stdout).to_string())
    } else {
        Err(anyhow::anyhow!("Command failed: {}", String::from_utf8_lossy(&output.stderr)))
    }
}
