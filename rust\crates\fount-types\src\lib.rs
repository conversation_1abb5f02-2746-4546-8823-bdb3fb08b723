//! Fount类型定义库
//! 
//! 这个crate包含了Fount项目中使用的所有核心类型定义，
//! 对应原JavaScript/TypeScript项目的src/decl/目录。

// 基础类型定义
pub mod base_defs;
pub mod char_api;
pub mod ai_source;
pub mod ai_source_generator;
pub mod user_api;
pub mod world_api;
pub mod import_handler_api;
pub mod plugin_api;
pub mod prompt_struct;
pub mod shell_api;

// 重新导出主要类型
pub use base_defs::*;
pub use char_api::*;
pub use ai_source::*;
pub use ai_source_generator::*;
pub use user_api::*;
pub use world_api::*;
pub use import_handler_api::*;
pub use plugin_api::*;
pub use prompt_struct::*;
pub use shell_api::*;

// 通用错误类型
use thiserror::Error;

#[derive(Error, Debug)]
pub enum FountError {
    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("配置错误: {message}")]
    Config { message: String },
    
    #[error("认证错误: {message}")]
    Auth { message: String },
    
    #[error("插件错误: {message}")]
    Plugin { message: String },
    
    #[error("网络错误: {0}")]
    Network(#[from] reqwest::Error),
    
    #[error("未知错误: {message}")]
    Unknown { message: String },
}

pub type Result<T> = std::result::Result<T, FountError>;
