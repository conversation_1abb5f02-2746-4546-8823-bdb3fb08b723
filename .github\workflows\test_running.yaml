name: Test Running

on:
  workflow_dispatch:
  push:
    paths:
      - '**.sh'
      - '**.fish'
      - '**.zsh'
      - '**.ps1'
      - 'path/**'
    tags-ignore:
      - '*'

jobs:
  test-fount:
    timeout-minutes: 4
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - name: Extract branch name
        shell: bash
        run: echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT
        id: extract_branch

      - name: Run install script (Unix)
        if: runner.os != 'Windows'
        run: |
          git clone https://github.com/steve02081504/fount.git --depth 1 --single-branch --branch ${{ steps.extract_branch.outputs.branch }}
          if [[ "$OSTYPE" == "darwin"* ]]; then
            xattr -dr com.apple.quarantine "./fount" || true
          fi
          find "./fount" -name "*.sh" -exec chmod +x {} \;
          find "./fount/path" -type f -exec chmod +x {} \;
          ./fount/path/fount.sh init

      - name: Run install script (Windows)
        if: runner.os == 'Windows'
        shell: powershell
        run: |
          git clone https://github.com/steve02081504/fount.git --depth 1 --single-branch --branch ${{ steps.extract_branch.outputs.branch }}
          ./fount/path/fount.ps1 init

      - name: Run remove script (Unix)
        if: runner.os != 'Windows'
        run: ./fount/path/fount.sh remove

      - name: Run remove script (Windows)
        if: runner.os == 'Windows'
        shell: powershell
        run: ./fount/path/fount.ps1 remove
