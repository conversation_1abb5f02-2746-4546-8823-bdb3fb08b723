{"lang": "zh-CN", "fountConsole": {"server": {"start": "正在启动服务器", "starting": "服务器启动中...", "ready": "服务器已启动", "usesdTime": "启动用时：${time}s", "showUrl": {"https": "HTTPS 服务器运行在 ${url}", "http": "HTTP 服务器运行在 ${url}"}, "standingBy": "standing by..."}, "jobs": {"restartingJob": "正在重启用户 ${username} 的 ${parttype} ${partname} 任务：${uid}"}, "ipc": {"sendCommandFailed": "发送命令失败：${error}", "invalidCommand": "无效的命令，请使用\"fount runshell <用户名> <shell名> <参数...>\"", "runShellLog": "运行 shell ${shellname} 作为 ${username}，参数：${args}", "invokeShellLog": "调用 shell ${shellname} 作为 ${username}，参数：${invokedata}", "unsupportedCommand": "不支持的命令类型", "processMessageError": "处理 IPC 消息时出错：${error}", "invalidCommandFormat": "无效的命令格式", "socketError": "Socket 错误: ${error}", "instanceRunning": "另一个实例正在运行", "serverStartPrefix": "server start", "serverStarted": "IPC 服务器已启动", "parseResponseFailed": "解析服务器响应失败: ${error}", "cannotParseResponse": "无法解析服务器响应", "unknownError": "未知错误"}, "partManager": {"git": {"noUpstream": "没有为分支 '${currentBranch}' 配置上游分支，跳过更新检查。", "dirtyWorkingDirectory": "工作目录不干净。请暂存或提交你的更改后再更新。", "updating": "从远程仓库更新中...", "localAhead": "本地分支领先于远程分支。无需更新。", "diverged": "本地和远程分支已分叉。强制更新中...", "upToDate": "已是最新。", "updateFailed": "从远程仓库更新部件失败：${error}"}, "partInitTime": "${parttype}部件${partname}初始化时间：${time}s", "partLoadTime": "${parttype}部件${partname}加载时间：${time}s"}, "web": {"requestReceived": "接受到请求：${method} ${url}"}, "route": {"setLanguagePreference": "用户 ${username} 设置首选语言: ${preferredLanguages}"}, "auth": {"tokenVerifyError": "令牌验证错误: ${error}", "refreshTokenError": "刷新令牌错误: ${error}", "logoutRefreshTokenProcessError": "登出刷新令牌处理错误: ${error}", "revokeTokenNoJTI": "无法撤销没有 jti 的令牌", "accountLockedLog": "用户 ${username} 账户因多次登录失败被锁定"}, "verification": {"codeGeneratedLog": "验证码: ${code} (60秒后过期)", "codeNotifyTitle": "验证码", "codeNotifyBody": "验证码: ${code} (60秒后过期)"}, "tray": {"readIconFailed": "读取图标文件失败: ${error}", "createTrayFailed": "创建托盘失败: ${error}"}, "discordbot": {"botStarted": "char ${charname} 已在discord bot ${botusername} 上登录"}, "telegrambot": {"botStarted": "char ${charname} 已在telegream bot ${botusername} 上登录"}}, "protocolhandler": {"title": "处理 Fount 协议", "processing": "正在处理协议...", "invalidProtocol": "无效的协议", "insufficientParams": "参数不足", "unknownCommand": "未知的命令", "shellCommandSent": "Shell 命令已发送", "shellCommandFailed": "发送 Shell 命令失败", "shellCommandError": "发送 Shell 命令时出错"}, "auth": {"title": "认证", "subtitle": "用户数据存储在本地", "usernameLabel": "用户名:", "usernameInput": {"placeholder": "请输入用户名"}, "passwordLabel": "密码:", "passwordInput": {"placeholder": "请输入密码"}, "confirmPasswordLabel": "确认密码:", "confirmPasswordInput": {"placeholder": "请再次输入密码"}, "verificationCodeLabel": "验证码:", "verificationCodeInput": {"placeholder": "请输入验证码"}, "sendCodeButton": "发送验证码", "login": {"title": "登录", "submitButton": "登录", "toggleLink": {"text": "没有账号？", "link": "立即注册"}}, "register": {"title": "注册", "submitButton": "注册", "toggleLink": {"text": "已有账号？", "link": "立即登录"}}, "error": {"passwordMismatch": "两次密码不一致。", "loginError": "登录出错。", "registrationError": "注册出错。", "verificationCodeError": "验证码错误或已过期。", "verificationCodeSent": "验证码发送成功。", "verificationCodeSendError": "验证码发送失败。", "verificationCodeRateLimit": "发送验证码过于频繁，请稍后再试。", "lowPasswordStrength": "密码强度太低。", "accountAlreadyExists": "账户已存在"}, "passwordStrength": {"veryWeak": "非常弱", "weak": "弱", "normal": "一般", "strong": "强", "veryStrong": "非常强"}}, "tutorial": {"title": "来点教程？", "modal": {"title": "欢迎使用fount！", "instruction": "走一遍新手教程吗？", "buttons": {"start": "开始教程", "skip": "跳过"}}, "endScreen": {"title": "太棒啦！教程完成！", "subtitle": "现在你已经学会操作啦！", "endButton": "下一步！"}, "progressMessages": {"mouseMove": "请使用您的手指尝试握住鼠标${mouseIcon}<br/>随后移动它", "keyboardPress": "请找到您的键盘${keyboardIcon}<br/>尝试使用您的手指按压键盘", "mobileTouchMove": "请找到您的手机${phoneIcon}<br/>尝试使用您的任意手指触摸屏幕，再移动它", "mobileClick": "请尝试使用您的任意手指触碰手机屏幕${phoneIcon}<br/>之后松开"}}, "home": {"title": "主页", "escapeConfirm": "确定要离开fount吗？", "filterInput": {"placeholder": "搜索..."}, "sidebarTitle": "详细信息", "itemDescription": "在此处选择一个项目以查看详细信息。", "noDescription": "无描述信息", "alerts": {"fetchHomeRegistryFailed": "获取主页注册信息失败"}, "functionMenu": {"icon": {"alt": "功能菜单"}}, "chars": {"tab": "角色", "title": "角色选择", "subtitle": "选择一个角色——然后开启聊天！", "none": "空空如也", "card": {"refreshButton": {"alt": "刷新", "title": "刷新"}, "noTags": "无标签", "version": "版本", "author": "作者", "homepage": "主页", "issuepage": "问题反馈", "defaultCheckbox": {"title": "设为默认角色"}}}, "worlds": {"tab": "世界", "title": "世界选择", "subtitle": "选择世界，沉浸其中！", "none": "空空如也", "card": {"refreshButton": {"alt": "刷新", "title": "刷新"}, "noTags": "无标签", "version": "版本", "author": "作者", "homepage": "主页", "issuepage": "问题反馈", "defaultCheckbox": {"title": "设为默认世界"}}}, "personas": {"tab": "人设", "title": "人设选择", "subtitle": "选择人设，体验人生。", "none": "空空如也", "card": {"refreshButton": {"alt": "刷新", "title": "刷新"}, "noTags": "无标签", "version": "版本", "author": "作者", "homepage": "主页", "issuepage": "问题反馈", "defaultCheckbox": {"title": "设为默认人设"}}}}, "themeManage": {"title": "主题管理", "instruction": "选择一个主题！", "themes": {"auto": "自动", "light": "亮色", "dark": "暗色", "cupcake": "杯糕", "bumblebee": "蜂蜜糖", "emerald": "绿宝石", "corporate": "公司", "synthwave": "合成波", "retro": "复古", "cyberpunk": "赛博朋克", "valentine": "情人节", "halloween": "万圣节", "garden": "花园", "forest": "森林", "aqua": "深海", "lofi": "lo-fi", "pastel": "浅色", "fantasy": "幻想", "wireframe": "线框", "black": "黑色", "luxury": "奢华", "dracula": "恶魔", "cmyk": "CMYK", "autumn": "秋天", "business": "商业", "acid": "酸性", "lemonade": "柠檬汁", "night": "夜晚", "coffee": "咖啡", "winter": "冬天", "dim": "昏暗", "nord": "北方", "sunset": "日落", "caramellatte": "巧克力拿铁", "abyss": "深渊", "silk": "丝绒"}}, "import": {"title": "导入", "tabs": {"fileImport": "文件导入", "textImport": "文本导入"}, "dropArea": {"icon": {"alt": "上传图标"}, "text": "拖放文件到此处或点击选择文件"}, "textArea": {"placeholder": "输入要导入的文本..."}, "buttons": {"import": "导入"}, "alerts": {"importSuccess": "导入成功", "importFailed": "导入失败: ${error}", "unknownError": "未知错误"}, "errors": {"noFileSelected": "请选择文件", "fileImportFailed": "文件导入失败: ${message}", "noTextContent": "请输入文本内容", "textImportFailed": "文本导入失败: ${message}", "unknownError": "未知错误", "handler": "处理器", "error": "错误"}, "fileItem": {"removeButton": {"title": "删除"}, "removeButtonIcon": {"alt": "删除"}}}, "aisource_editor": {"title": "AI 来源编辑器", "fileList": {"title": "AI 来源列表", "addButton": {"title": "+"}}, "configTitle": "AI 来源配置", "generatorSelect": {"label": "选择生成器", "placeholder": "请选择"}, "buttons": {"save": "保存", "delete": "删除"}, "alerts": {"fetchFileListFailed": "获取文件列表失败: ${error}", "fetchGeneratorListFailed": "获取生成器列表失败: ${error}", "fetchFileDataFailed": "获取文件数据失败: ${error}", "noFileSelectedSave": "没有选择要保存的文件。", "saveFileFailed": "保存文件失败: ${error}", "noFileSelectedDelete": "没有选择要删除的文件。", "deleteFileFailed": "删除文件失败: ${error}", "invalidFileName": "文件名不能包含以下字符： / \\ : * ? \" < > |", "addFileFailed": "添加文件失败: ${error}", "fetchConfigTemplateFailed": "获取配置模板失败", "noGeneratorSelectedSave": "请选择生成器后再保存"}, "confirm": {"unsavedChanges": "您有未保存的更改。是否要放弃更改？", "deleteFile": "确定要删除文件吗?", "unsavedChangesBeforeUnload": "您有未保存的更改。确定要离开此页面吗？"}, "prompts": {"newFileName": "请输入新的 AI 来源文件名 (请勿包含后缀名):"}, "editor": {"disabledIndicator": "请先选择生成器"}}, "part_config": {"title": "部件配置", "pageTitle": "部件配置", "labels": {"partType": "选择部件类型", "part": "选择部件"}, "placeholders": {"partTypeSelect": "请选择", "partSelect": "请选择"}, "editor": {"title": "部件配置", "disabledIndicator": "此部件不支持配置", "buttons": {"save": "保存"}}, "errorMessage": {"icon": {"alt": "错误提示"}}, "alerts": {"fetchPartTypesFailed": "获取部件类型失败", "fetchPartsFailed": "获取部件列表失败", "loadEditorFailed": "加载编辑器失败", "saveConfigFailed": "保存部件配置失败", "unsavedChanges": "您有未保存的更改。是否要放弃更改？", "beforeUnload": "您有未保存的更改。确定要离开吗？"}}, "uninstall": {"title": "卸载", "titleWithName": "卸载 ${type}/${name}", "confirmMessage": "您确定要卸载 ${type}: ${name} 吗？", "invalidParamsTitle": "参数无效", "infoMessage": {"icon": {"alt": "信息图标"}}, "errorMessage": {"icon": {"alt": "错误图标"}}, "buttons": {"confirm": "确认卸载", "cancel": "取消", "back": "返回"}, "alerts": {"success": "成功卸载 ${type}: ${name}", "failed": "卸载失败: ${error}", "invalidParams": "无效的请求参数。", "httpError": "HTTP 错误！状态码：${status}"}}, "chat": {"new": {"title": "新聊天"}, "title": "聊天", "sidebar": {"world": {"icon": {"alt": "世界图标"}, "title": "世界"}, "persona": {"icon": {"alt": "用户角色图标"}, "title": "用户角色"}, "charList": {"icon": {"alt": "角色列表图标"}, "title": "角色列表", "buttons": {"addChar": {"title": "添加角色"}, "addCharIcon": {"alt": "添加角色图标"}}}, "noSelection": "无", "noDescription": "无描述信息"}, "chatArea": {"title": "聊天", "menuButton": {"title": "菜单"}, "menuButtonIcon": {"alt": "菜单图标"}, "input": {"placeholder": "输入信息...\nCtrl+Enter 发送"}, "sendButton": {"title": "发送"}, "sendButtonIcon": {"alt": "发送图标"}, "uploadButton": {"title": "上传"}, "uploadButtonIcon": {"alt": "上传图标"}, "voiceButton": {"title": "录音"}, "voiceButtonIcon": {"alt": "录音图标"}, "photoButton": {"title": "照片"}, "photoButtonIcon": {"alt": "照片图标"}}, "rightSidebar": {"title": "描述信息"}, "messageList": {"confirmDeleteMessage": "确认删除此消息？"}, "voiceRecording": {"errorAccessingMicrophone": "无法访问麦克风"}, "messageView": {"buttons": {"edit": {"title": "编辑"}, "editIcon": {"alt": "编辑图标"}, "delete": {"title": "删除"}, "deleteIcon": {"alt": "删除图标"}}}, "messageEdit": {"input": {"placeholder": "输入内容..."}, "buttons": {"confirm": {"title": "确认"}, "confirmIcon": {"alt": "确认图标"}, "cancel": {"title": "取消"}, "cancelIcon": {"alt": "取消图标"}, "upload": {"title": "上传"}, "uploadIcon": {"alt": "上传图标"}}}, "attachment": {"buttons": {"download": {"title": "下载"}, "downloadIcon": {"alt": "下载图标"}, "delete": {"title": "删除"}, "deleteIcon": {"alt": "删除图标"}}}, "charCard": {"frequencyLabel": "频率", "buttons": {"removeChar": {"title": "移出对话"}, "removeCharIcon": {"alt": "移除角色图标"}, "forceReply": {"title": "强制回复"}, "forceReplyIcon": {"alt": "强制回复图标"}}}}, "chat_history": {"title": "聊天记录", "pageTitle": "聊天记录", "sortOptions": {"time_desc": "时间降序", "time_asc": "时间升序"}, "filterInput": {"placeholder": "搜索..."}, "selectAll": "全选", "buttons": {"reverseSelect": "反选", "deleteSelected": "删除选中", "exportSelected": "导出选中"}, "confirmDeleteChat": "确定要删除与 ${chars} 的聊天记录吗？", "confirmDeleteMultiChats": "确定要删除选中的 ${count} 条聊天记录吗？", "alerts": {"noChatSelectedForDeletion": "请选择要删除的聊天记录", "noChatSelectedForExport": "请选择要导出的聊天记录", "copyError": "复制失败", "deleteError": "删除失败", "exportError": "导出失败"}, "chatItemButtons": {"continue": "继续", "copy": "复制", "export": "导出", "delete": "删除"}}, "discord_bots": {"title": "<PERSON>rd <PERSON>", "cardTitle": "<PERSON>rd <PERSON>", "buttons": {"newBot": "新建", "deleteBot": "删除"}, "configCard": {"title": "<PERSON><PERSON>", "labels": {"character": "角色", "apiKey": "Discord API Key", "config": "Config"}, "charSelectPlaceholder": "选择角色", "apiKeyInput": {"placeholder": "Enter API Key"}, "toggleApiKeyIcon": {"alt": "切换显示 API 密钥"}, "buttons": {"saveConfig": "保存配置", "startBot": "启动", "stopBot": "停止"}}, "prompts": {"newBotName": "请输入新的 Bot 名称："}, "alerts": {"botExists": "名为 \"${botname}\" 的 Bot 已存在，请使用其他名称。", "unsavedChanges": "您有未保存的更改。是否要放弃更改？", "configSaved": "配置保存成功", "httpError": "HTTP 错误", "beforeUnload": "您有未保存的更改。确定要离开吗？"}}, "telegram_bots": {"title": "Telegram 机器人", "cardTitle": "Telegram 机器人管理", "buttons": {"newBot": "新建", "deleteBot": "删除"}, "configCard": {"title": "机器人配置", "labels": {"character": "绑定角色", "botToken": "Telegram Bot Token", "config": "Config"}, "charSelectPlaceholder": "选择角色", "botTokenInput": {"placeholder": "Enter Telegram Bot Token"}, "toggleBotTokenIcon": {"alt": "切换显示 API 密钥"}, "buttons": {"saveConfig": "保存配置", "startBot": "启动", "stopBot": "停止"}}, "prompts": {"newBotName": "请输入新的 Bot 名称："}, "alerts": {"botExists": "名为 “${botname}” 的机器人已存在，请使用其他名称。", "unsavedChanges": "您有未保存的更改。确定要放弃这些更改吗？", "configSaved": "配置已成功保存！", "httpError": "HTTP 错误", "beforeUnload": "您有未保存的更改。确定要离开当前页面吗？"}}, "terminal_assistant": {"title": "终端辅助", "initialMessage": "Fount 支持将你喜欢的角色部署到你的终端中辅助你编码！", "initialMessageLink": "点击这里了解更多信息"}, "access": {"title": "在其他设备上访问 Fount", "heading": "想在其他设备上访问 Fount 吗？", "instruction": {"sameLAN": "确保设备和 Fount 主机在同一个局域网下", "accessthis": "访问以下网址："}, "copyButton": "复制网址", "copied": "网址已复制到剪贴板！"}, "proxy": {"title": "API 代理", "heading": "OpenAI API 代理地址", "instruction": "将以下地址填入任意需要 OpenAI API 格式的应用中，即可使用在 fount 中的AI源！", "copyButton": "复制地址", "copied": "地址已复制到剪贴板！"}, "404": {"title": "页面未找到", "pageNotFoundText": "糟糕！您似乎来到了一个不存在的页面。", "homepageButton": "返回首页", "MineSweeper": {"difficultyLabel": "难度：", "difficultyEasy": "简单", "difficultyMedium": "中等", "difficultyHard": "困难", "difficultyCustom": "自定义", "minesLeftLabel": "剩余雷数：", "timeLabel": "时间：", "restartButton": "重新开始", "rowsLabel": "行数：", "colsLabel": "列数：", "minesCountLabel": "雷数：", "winMessage": "恭喜你，你赢了！", "loseMessage": "游戏结束，你踩到地雷了！", "soundOn": "声音开启", "soundOff": "声音关闭"}}, "userSettings": {"title": "用户设置", "PageTitle": "用户设置", "apiError": "API 请求失败: ${message}", "generalError": "发生错误: ${message}", "userInfo": {"title": "用户信息", "usernameLabel": "用户名:", "creationDateLabel": "账户创建日期:", "folderSizeLabel": "用户数据大小:", "folderPathLabel": "用户数据路径:", "copyPathBtnTitle": "复制路径", "copiedAlert": "路径已复制到剪贴板！"}, "changePassword": {"title": "修改密码", "currentPasswordLabel": "当前密码:", "newPasswordLabel": "新密码:", "confirmNewPasswordLabel": "确认新密码:", "submitButton": "修改密码", "errorMismatch": "新密码两次输入不一致。", "success": "密码修改成功。"}, "renameUser": {"title": "重命名用户", "newUsernameLabel": "新用户名:", "submitButton": "重命名用户", "confirmMessage": "您确定要更改用户名吗？这将需要您重新登录。", "success": "用户已成功重命名为 “${newUsername}”。您现在将被注销。"}, "userDevices": {"title": "用户设备/会话", "refreshButtonTitle": "刷新列表", "noDevicesFound": "未找到任何设备或会话。", "deviceInfo": "设备ID: ${deviceId}", "thisDevice": "当前设备", "deviceDetails": "最后上线: ${lastSeen} | IP: ${ipAddress} | UA: ${userAgent}", "revokeButton": "撤销", "revokeConfirm": "您确定要从此设备/会话注销吗？", "revokeSuccess": "设备/会话已成功撤销。"}, "logout": {"title": "登出", "description": "这将从当前设备注销您的账户。", "buttonText": "登出", "confirmMessage": "您确定要登出吗？", "successMessage": "已成功登出。即将跳转到登录页面..."}, "deleteAccount": {"title": "删除账户", "warning": "警告：此操作将永久删除您的账户和所有相关数据，且无法恢复。", "submitButton": "删除我的账户", "confirmMessage1": "警告！您确定要永久删除您的账户吗？此操作无法撤销。", "confirmMessage2": "为确认删除，请输入您的用户名“${username}”：", "usernameMismatch": "输入的用户名与当前用户不匹配。删除操作已取消。", "success": "账户已成功删除。您现在将被注销。"}, "passwordConfirm": {"title": "确认操作", "message": "为继续此操作，请输入您的当前密码：", "passwordLabel": "密码:", "confirmButton": "确认", "cancelButton": "取消"}}}