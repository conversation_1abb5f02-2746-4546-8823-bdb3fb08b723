# Fount Rust项目依赖版本清单

## 版本策略

- **稳定版本**: 优先选择稳定、广泛使用的版本
- **兼容性**: 确保依赖间的版本兼容性
- **安全性**: 选择没有已知安全漏洞的版本
- **维护性**: 选择活跃维护的版本

## Workspace根目录 Cargo.toml

```toml
[workspace]
members = [
    "crates/fount-types",
    "crates/fount-utils", 
    "crates/fount-server",
    "crates/fount-desktop"
]
resolver = "2"

[workspace.dependencies]
# 核心运行时
tokio = { version = "1.35.1", features = ["full"] }
serde = { version = "1.0.193", features = ["derive"] }
serde_json = "1.0.108"
anyhow = "1.0.79"
thiserror = "1.0.56"

# 异步工具
futures = "0.3.30"
async-trait = "0.1.77"

# 日志和追踪
tracing = "0.1.40"
tracing-subscriber = { version = "0.3.18", features = ["env-filter", "json"] }
tracing-appender = "0.2.3"

# 时间处理
chrono = { version = "0.4.31", features = ["serde"] }
humantime = "2.1.0"

# 配置管理
config = "0.14.0"
dirs = "5.0.1"

# 加密和安全
bcrypt = "0.15.0"
jsonwebtoken = "9.2.0"
uuid = { version = "1.6.1", features = ["v4", "serde"] }
rand = "0.8.5"

# 网络和HTTP
reqwest = { version = "0.11.23", features = ["json", "stream", "multipart", "cookies", "gzip", "brotli", "rustls-tls"] }
reqwest-middleware = "0.2.4"
reqwest-retry = "0.4.0"
reqwest-tracing = "0.4.7"

# 文件系统
fs-extra = "1.3.0"
walkdir = "2.4.0"
notify = "6.1.1"

# 压缩和归档
zip = "0.6.6"
flate2 = "1.0.28"

# 图像处理
image = { version = "0.24.7", features = ["png", "jpeg", "ico"] }

# 正则表达式
regex = "1.10.2"

# 数据结构
indexmap = "2.1.0"
dashmap = "5.5.3"

# 内部依赖
fount-types = { path = "crates/fount-types" }
fount-utils = { path = "crates/fount-utils" }
fount-server = { path = "crates/fount-server" }
```

## fount-types Crate

```toml
[package]
name = "fount-types"
version = "0.1.0"
edition = "2021"
description = "Type definitions for Fount"
license = "MIT"

[dependencies]
# 序列化
serde = { workspace = true }
serde_json = { workspace = true }

# 时间
chrono = { workspace = true }

# 工具
uuid = { workspace = true }
indexmap = { workspace = true }

# 异步
async-trait = { workspace = true }
futures = { workspace = true }

# 错误处理
thiserror = { workspace = true }
anyhow = { workspace = true }
```

## fount-utils Crate

```toml
[package]
name = "fount-utils"
version = "0.1.0"
edition = "2021"
description = "Utility functions for Fount"
license = "MIT"

[dependencies]
# 内部依赖
fount-types = { workspace = true }

# 核心
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }

# 异步
async-trait = { workspace = true }
futures = { workspace = true }

# 日志
tracing = { workspace = true }

# 时间
chrono = { workspace = true }
humantime = { workspace = true }

# 文件系统
fs-extra = { workspace = true }
dirs = { workspace = true }
notify = { workspace = true }

# 网络
reqwest = { workspace = true }

# 系统集成
notify-rust = "4.10.0"
tray-icon = "0.14.3"

# 进程间通信
interprocess = "1.2.1"

# 终端和控制台
crossterm = "0.27.0"

# 国际化
fluent = "0.16.0"
fluent-bundle = "0.15.2"
unic-langid = "0.9.4"

# 正则表达式
regex = { workspace = true }

# 环境检测
sysinfo = "0.30.5"

# 速率限制
governor = "0.6.0"
nonzero_ext = "0.3.0"

# Discord RPC
discord-rich-presence = "0.2.3"

# 验证码
captcha = "0.0.9"

# Sentry
sentry = { version = "0.32.1", features = ["tracing", "debug-images"] }

# 压缩
flate2 = { workspace = true }
zip = { workspace = true }

# 图像处理
image = { workspace = true }

# 配置
config = { workspace = true }

# 加密
bcrypt = { workspace = true }
rand = { workspace = true }

# 数据结构
dashmap = { workspace = true }
```

## fount-server Crate

```toml
[package]
name = "fount-server"
version = "0.1.0"
edition = "2021"
description = "Fount server implementation"
license = "MIT"

[[bin]]
name = "fount-server"
path = "src/main.rs"

[dependencies]
# 内部依赖
fount-types = { workspace = true }
fount-utils = { workspace = true }

# 核心
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }

# Web框架
axum = { version = "0.7.4", features = ["ws", "multipart", "macros"] }
tower = { version = "0.4.13", features = ["full"] }
tower-http = { version = "0.5.0", features = ["fs", "cors", "trace", "compression-gzip", "compression-br"] }
hyper = { version = "1.1.0", features = ["full"] }

# 模板引擎
askama = { version = "0.12.1", features = ["with-axum"] }
askama_axum = "0.4.0"

# HTTP客户端
reqwest = { workspace = true }
reqwest-middleware = { workspace = true }
reqwest-retry = { workspace = true }
reqwest-tracing = { workspace = true }

# 认证和安全
jsonwebtoken = { workspace = true }
bcrypt = { workspace = true }
uuid = { workspace = true }

# 日志和追踪
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
tracing-appender = { workspace = true }

# 时间
chrono = { workspace = true }

# 文件系统
fs-extra = { workspace = true }
walkdir = { workspace = true }

# 配置
config = { workspace = true }
dirs = { workspace = true }

# 数据结构
indexmap = { workspace = true }
dashmap = { workspace = true }

# 异步
async-trait = { workspace = true }
futures = { workspace = true }

# WebSocket
tokio-tungstenite = "0.21.0"

# 文件上传
multer = "3.0.0"

# Cookie处理
cookie = "0.18.0"

# MIME类型
mime = "0.3.17"
mime_guess = "2.0.4"

# 压缩
flate2 = { workspace = true }
zip = { workspace = true }

# 单实例检查
single-instance = "0.3.0"

# 信号处理
signal-hook = "0.3.17"
signal-hook-tokio = { version = "0.3.1", features = ["futures-v0_3"] }

# 性能监控
metrics = "0.22.0"
metrics-exporter-prometheus = "0.13.0"

# 内存映射
memmap2 = "0.9.4"

# 动态库加载
libloading = "0.8.1"

# 进程间通信
interprocess = "1.2.1"

# Sentry
sentry = { workspace = true }
sentry-tower = "0.32.1"
sentry-tracing = "0.32.1"
```

## fount-desktop Crate (Tauri)

```toml
[package]
name = "fount-desktop"
version = "0.1.0"
edition = "2021"
description = "Fount desktop application"
license = "MIT"

[build-dependencies]
tauri-build = { version = "2.0.0-beta.17", features = [] }

[dependencies]
# 内部依赖
fount-server = { workspace = true }
fount-types = { workspace = true }
fount-utils = { workspace = true }

# Tauri
tauri = { version = "2.0.0-beta.22", features = ["system-tray", "protocol-all", "window-all", "macos-private-api"] }
tauri-plugin-shell = "2.0.0-beta.7"
tauri-plugin-dialog = "2.0.0-beta.6"
tauri-plugin-fs = "2.0.0-beta.6"
tauri-plugin-notification = "2.0.0-beta.5"
tauri-plugin-updater = "2.0.0-beta.5"
tauri-plugin-window-state = "2.0.0-beta.5"

# 核心
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }

# 日志
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

# 系统集成
tray-icon = { workspace = true }
single-instance = "0.3.0"

# 异步
async-trait = { workspace = true }
futures = { workspace = true }

# 配置
dirs = { workspace = true }
config = { workspace = true }

# 时间
chrono = { workspace = true }

# 网络
reqwest = { workspace = true }

# 文件系统
fs-extra = { workspace = true }

# UUID
uuid = { workspace = true }
```

## 开发依赖

```toml
[workspace.dev-dependencies]
# 测试
tokio-test = "0.4.3"
mockall = "0.12.1"
wiremock = "0.5.22"
tempfile = "3.8.1"
assert_matches = "1.5.0"

# 基准测试
criterion = { version = "0.5.1", features = ["html_reports"] }

# 代码覆盖率
tarpaulin = "0.27.3"

# 文档测试
doc-comment = "0.3.3"

# 属性测试
proptest = "1.4.0"
quickcheck = "1.0.3"

# 序列化测试
serde_test = "1.0.176"

# HTTP测试
httpmock = "0.7.0"
```

## 构建工具依赖

```toml
# 在各个crate的[build-dependencies]中
[build-dependencies]
# 代码生成
quote = "1.0.35"
proc-macro2 = "1.0.76"
syn = { version = "2.0.48", features = ["full"] }

# 资源嵌入
rust-embed = "8.2.0"

# 环境变量
vergen = { version = "8.2.6", features = ["build", "git", "gitcl"] }

# Protobuf (如果需要)
prost-build = "0.12.3"
tonic-build = "0.10.2"
```

## 平台特定依赖

### Windows特定
```toml
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3.9", features = ["winuser", "shellapi", "processthreadsapi"] }
windows = { version = "0.52.0", features = ["Win32_Foundation", "Win32_System_Threading", "Win32_UI_Shell"] }
```

### Unix/Linux特定
```toml
[target.'cfg(unix)'.dependencies]
nix = { version = "0.27.1", features = ["signal", "process"] }
libc = "0.2.152"
```

### macOS特定
```toml
[target.'cfg(target_os = "macos")'.dependencies]
cocoa = "0.25.0"
objc = "0.2.7"
core-foundation = "0.9.4"
```

## 可选功能特性

```toml
[features]
default = ["server", "desktop"]

# 核心功能
server = ["fount-server"]
desktop = ["fount-desktop", "tauri"]

# 可选功能
metrics = ["dep:metrics", "dep:metrics-exporter-prometheus"]
sentry = ["dep:sentry", "dep:sentry-tower", "dep:sentry-tracing"]
discord-rpc = ["fount-utils/discord-rpc"]
system-tray = ["dep:tray-icon"]
notifications = ["dep:notify-rust"]

# 开发功能
dev = ["tracing-subscriber/env-filter"]
hot-reload = ["notify"]

# 性能优化
simd = ["serde_json/arbitrary_precision"]
jemalloc = ["dep:jemallocator"]

# 安全功能
security-audit = []
```

## 版本兼容性矩阵

| 组件 | 最低Rust版本 | 推荐版本 | 说明 |
|------|-------------|----------|------|
| **Rust编译器** | 1.75.0 | 1.75.0+ | 支持最新语言特性 |
| **Tokio** | 1.35.1 | 1.35.1+ | 稳定的异步运行时 |
| **Axum** | 0.7.4 | 0.7.4+ | 最新Web框架 |
| **Serde** | 1.0.193 | 1.0.193+ | 序列化支持 |
| **Tauri** | 2.0.0-beta.22 | 2.0.0-beta.22+ | 桌面应用框架 |

## 安全考虑

### 已知漏洞检查
```bash
# 使用cargo-audit检查安全漏洞
cargo install cargo-audit
cargo audit

# 使用cargo-deny检查许可证和安全
cargo install cargo-deny
cargo deny check
```

### 依赖更新策略
```bash
# 检查过时依赖
cargo install cargo-outdated
cargo outdated

# 更新依赖
cargo update
```

## 构建优化

### 发布配置
```toml
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.release-with-debug]
inherits = "release"
debug = true
strip = false
```

### 开发配置
```toml
[profile.dev]
opt-level = 0
debug = true
split-debuginfo = "unpacked"
```

这个版本清单确保了所有依赖的兼容性和稳定性，为Fount项目的Rust实现提供了坚实的基础。
