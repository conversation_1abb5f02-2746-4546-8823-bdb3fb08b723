# Rust进程间通信库选型分析

## IPC需求概览

Fount项目需要IPC来实现多实例协调、命令传递、插件隔离等功能。我们需要选择合适的IPC机制来替代Node.js中的IPC服务器功能。

## IPC机制分析

### 1. 命名管道 (Named Pipes)
**平台**: Windows主要，Unix有限支持
**特点**: 高性能、双向通信、权限控制

#### Windows实现
```rust
// Windows命名管道
use tokio::net::windows::named_pipe::{NamedPipeServer, ClientOptions};

pub struct WindowsIpcServer {
    pipe_name: String,
}

impl WindowsIpcServer {
    pub async fn start(&self) -> Result<(), IpcError> {
        let server = NamedPipeServer::create(&self.pipe_name)?;
        
        loop {
            server.connect().await?;
            let stream = server.into_stream();
            
            tokio::spawn(async move {
                self.handle_client(stream).await;
            });
        }
    }
}
```

#### 优势
- **高性能**: 内核级通信，延迟极低
- **安全性**: 支持权限和访问控制
- **双向**: 支持双向通信

#### 劣势
- **平台限制**: 主要在Windows上支持
- **复杂性**: API相对复杂

### 2. Unix域套接字 (Unix Domain Sockets)
**平台**: Unix/Linux/macOS
**特点**: 高性能、文件系统命名、权限控制

#### Unix实现
```rust
// Unix域套接字
use tokio::net::{UnixListener, UnixStream};

pub struct UnixIpcServer {
    socket_path: PathBuf,
}

impl UnixIpcServer {
    pub async fn start(&self) -> Result<(), IpcError> {
        // 清理旧的socket文件
        let _ = std::fs::remove_file(&self.socket_path);
        
        let listener = UnixListener::bind(&self.socket_path)?;
        
        while let Ok((stream, _)) = listener.accept().await {
            tokio::spawn(async move {
                self.handle_client(stream).await;
            });
        }
        
        Ok(())
    }
}
```

#### 优势
- **高性能**: 内核级通信
- **文件系统**: 使用文件系统路径命名
- **权限**: 文件系统权限控制

#### 劣势
- **平台限制**: 不支持Windows

### 3. TCP本地套接字
**平台**: 跨平台
**特点**: 通用性好、网络协议栈

#### TCP实现
```rust
// TCP本地套接字
use tokio::net::{TcpListener, TcpStream};

pub struct TcpIpcServer {
    port: u16,
}

impl TcpIpcServer {
    pub async fn start(&self) -> Result<(), IpcError> {
        let listener = TcpListener::bind(("127.0.0.1", self.port)).await?;
        
        while let Ok((stream, addr)) = listener.accept().await {
            // 只接受本地连接
            if !addr.ip().is_loopback() {
                continue;
            }
            
            tokio::spawn(async move {
                self.handle_client(stream).await;
            });
        }
        
        Ok(())
    }
}
```

#### 优势
- **跨平台**: 所有平台支持
- **简单**: API简单易用
- **调试**: 容易调试和监控

#### 劣势
- **性能**: 相比本地IPC性能略低
- **安全**: 需要额外的安全措施

### 4. 共享内存
**平台**: 跨平台（需要库支持）
**特点**: 最高性能、复杂同步

#### 共享内存实现
```rust
// 共享内存
use shared_memory::*;

pub struct SharedMemoryIpc {
    shmem: Shmem,
}

impl SharedMemoryIpc {
    pub fn create(name: &str, size: usize) -> Result<Self, IpcError> {
        let shmem = ShmemConf::new()
            .size(size)
            .create()?;
            
        Ok(Self { shmem })
    }
    
    pub fn write_message(&mut self, data: &[u8]) -> Result<(), IpcError> {
        let ptr = self.shmem.as_ptr();
        unsafe {
            std::ptr::copy_nonoverlapping(data.as_ptr(), ptr, data.len());
        }
        Ok(())
    }
}
```

#### 优势
- **最高性能**: 直接内存访问
- **大数据**: 适合大量数据传输

#### 劣势
- **复杂性**: 需要复杂的同步机制
- **安全性**: 内存安全问题
- **调试**: 难以调试

## 候选库分析

### 1. interprocess
**开发者**: kotauskas
**版本**: 2.x (稳定)
**设计理念**: 跨平台、统一API

#### 核心特性
- **跨平台**: 统一的跨平台API
- **多种机制**: 支持多种IPC机制
- **异步支持**: 与Tokio集成
- **类型安全**: 强类型消息传递

#### API示例
```rust
use interprocess::local_socket::{LocalSocketListener, LocalSocketStream};

// 服务器端
let listener = LocalSocketListener::bind("/tmp/fount.sock")?;
for conn in listener.incoming() {
    let conn = conn?;
    tokio::spawn(async move {
        handle_connection(conn).await;
    });
}

// 客户端
let conn = LocalSocketStream::connect("/tmp/fount.sock")?;
```

#### 优势
- **统一API**: 跨平台统一接口
- **功能完整**: 支持多种IPC机制
- **文档良好**: 详细的文档和示例

### 2. ipc-channel
**开发者**: Servo项目
**版本**: 0.18.x (稳定)
**设计理念**: 高性能、类型安全

#### 核心特性
- **类型安全**: 强类型消息传递
- **序列化**: 基于serde的序列化
- **跨进程**: 支持跨进程通道
- **零拷贝**: 优化的内存使用

#### API示例
```rust
use ipc_channel::ipc;

// 创建通道
let (tx, rx) = ipc::channel()?;

// 发送消息
tx.send(Message::Command("hello".to_string()))?;

// 接收消息
let message = rx.recv()?;
```

#### 优势
- **类型安全**: 编译时类型检查
- **高性能**: 优化的序列化
- **Servo验证**: 在Servo浏览器中验证

#### 劣势
- **复杂性**: 相对复杂的API
- **依赖**: 较多的依赖

### 3. Tokio原生支持
**来源**: Tokio生态
**特点**: 与Tokio深度集成

#### 实现方式
```rust
// 使用Tokio原生IPC
#[cfg(windows)]
use tokio::net::windows::named_pipe;

#[cfg(unix)]
use tokio::net::UnixListener;

pub struct NativeIpcServer;

impl NativeIpcServer {
    #[cfg(windows)]
    pub async fn start_windows(&self) -> Result<(), IpcError> {
        let server = named_pipe::NamedPipeServer::create(r"\\.\pipe\fount")?;
        // Windows实现
    }
    
    #[cfg(unix)]
    pub async fn start_unix(&self) -> Result<(), IpcError> {
        let listener = UnixListener::bind("/tmp/fount.sock")?;
        // Unix实现
    }
}
```

## 最终选择：interprocess + Tokio原生

### 组合策略

我们选择以interprocess为主，Tokio原生为辅的组合策略：

- **interprocess**: 提供跨平台统一API
- **Tokio原生**: 在特定平台优化性能
- **TCP fallback**: 作为备用方案

### 选择理由

#### 1. 跨平台统一性
```rust
use interprocess::local_socket::{LocalSocketListener, LocalSocketStream};
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Debug)]
pub enum IpcMessage {
    Command { cmd: String, args: Vec<String> },
    Response { success: bool, data: String },
    Shutdown,
}

pub struct FountIpcServer {
    socket_path: String,
}

impl FountIpcServer {
    pub async fn start(&self) -> Result<(), IpcError> {
        let listener = LocalSocketListener::bind(&self.socket_path)?;
        
        for conn in listener.incoming() {
            let conn = conn?;
            tokio::spawn(async move {
                self.handle_connection(conn).await;
            });
        }
        
        Ok(())
    }
    
    async fn handle_connection(&self, mut conn: LocalSocketStream) -> Result<(), IpcError> {
        let mut buffer = vec![0u8; 4096];
        
        loop {
            let n = conn.read(&mut buffer).await?;
            if n == 0 { break; }
            
            let message: IpcMessage = serde_json::from_slice(&buffer[..n])?;
            let response = self.process_message(message).await?;
            
            let response_data = serde_json::to_vec(&response)?;
            conn.write_all(&response_data).await?;
        }
        
        Ok(())
    }
    
    async fn process_message(&self, message: IpcMessage) -> Result<IpcMessage, IpcError> {
        match message {
            IpcMessage::Command { cmd, args } => {
                match cmd.as_str() {
                    "runshell" => {
                        if let Some(shell_name) = args.get(0) {
                            self.run_shell(shell_name).await?;
                            Ok(IpcMessage::Response { 
                                success: true, 
                                data: "Shell started".to_string() 
                            })
                        } else {
                            Ok(IpcMessage::Response { 
                                success: false, 
                                data: "Missing shell name".to_string() 
                            })
                        }
                    }
                    "shutdown" => {
                        self.shutdown().await?;
                        Ok(IpcMessage::Shutdown)
                    }
                    _ => {
                        Ok(IpcMessage::Response { 
                            success: false, 
                            data: format!("Unknown command: {}", cmd) 
                        })
                    }
                }
            }
            _ => {
                Ok(IpcMessage::Response { 
                    success: false, 
                    data: "Invalid message type".to_string() 
                })
            }
        }
    }
}
```

#### 2. 客户端实现
```rust
pub struct FountIpcClient {
    socket_path: String,
}

impl FountIpcClient {
    pub async fn send_command(&self, cmd: &str, args: Vec<String>) -> Result<String, IpcError> {
        let mut conn = LocalSocketStream::connect(&self.socket_path)?;
        
        let message = IpcMessage::Command {
            cmd: cmd.to_string(),
            args,
        };
        
        let request_data = serde_json::to_vec(&message)?;
        conn.write_all(&request_data).await?;
        
        let mut buffer = vec![0u8; 4096];
        let n = conn.read(&mut buffer).await?;
        
        let response: IpcMessage = serde_json::from_slice(&buffer[..n])?;
        
        match response {
            IpcMessage::Response { success, data } => {
                if success {
                    Ok(data)
                } else {
                    Err(IpcError::CommandFailed(data))
                }
            }
            _ => Err(IpcError::InvalidResponse),
        }
    }
    
    pub async fn shutdown_server(&self) -> Result<(), IpcError> {
        self.send_command("shutdown", vec![]).await?;
        Ok(())
    }
    
    pub async fn run_shell(&self, shell_name: &str) -> Result<(), IpcError> {
        self.send_command("runshell", vec![shell_name.to_string()]).await?;
        Ok(())
    }
}
```

#### 3. 单实例检测
```rust
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;

pub struct SingleInstance {
    _lock_file: std::fs::File,
    is_running: Arc<AtomicBool>,
}

impl SingleInstance {
    pub fn new(name: &str) -> Result<Self, IpcError> {
        let lock_path = std::env::temp_dir().join(format!("{}.lock", name));
        
        let lock_file = std::fs::OpenOptions::new()
            .create(true)
            .write(true)
            .truncate(true)
            .open(&lock_path)?;
            
        // 尝试获取文件锁
        #[cfg(unix)]
        {
            use std::os::unix::fs::OpenOptionsExt;
            // Unix文件锁实现
        }
        
        #[cfg(windows)]
        {
            // Windows文件锁实现
        }
        
        Ok(Self {
            _lock_file: lock_file,
            is_running: Arc::new(AtomicBool::new(true)),
        })
    }
    
    pub fn is_single(&self) -> bool {
        self.is_running.load(Ordering::Relaxed)
    }
}
```

#### 4. 协议处理器集成
```rust
pub struct ProtocolHandler {
    ipc_client: FountIpcClient,
}

impl ProtocolHandler {
    pub async fn handle_protocol_url(&self, url: &str) -> Result<(), ProtocolError> {
        if let Some(command) = self.parse_protocol_url(url)? {
            match command {
                ProtocolCommand::RunShell { shell, args } => {
                    self.ipc_client.send_command("runshell", vec![shell]).await?;
                }
                ProtocolCommand::Install { url } => {
                    self.ipc_client.send_command("install", vec![url]).await?;
                }
                ProtocolCommand::Open => {
                    self.ipc_client.send_command("open", vec![]).await?;
                }
            }
        }
        
        Ok(())
    }
    
    fn parse_protocol_url(&self, url: &str) -> Result<Option<ProtocolCommand>, ProtocolError> {
        if !url.starts_with("fount://") {
            return Ok(None);
        }
        
        let url = &url[8..]; // 移除 "fount://"
        let parts: Vec<&str> = url.split('/').collect();
        
        match parts.get(0) {
            Some(&"runshell") => {
                if let Some(shell) = parts.get(1) {
                    Ok(Some(ProtocolCommand::RunShell {
                        shell: shell.to_string(),
                        args: parts[2..].iter().map(|s| s.to_string()).collect(),
                    }))
                } else {
                    Err(ProtocolError::InvalidUrl)
                }
            }
            Some(&"install") => {
                if let Some(install_url) = parts.get(1) {
                    Ok(Some(ProtocolCommand::Install {
                        url: install_url.to_string(),
                    }))
                } else {
                    Err(ProtocolError::InvalidUrl)
                }
            }
            Some(&"open") => {
                Ok(Some(ProtocolCommand::Open))
            }
            _ => Err(ProtocolError::UnknownCommand),
        }
    }
}

#[derive(Debug)]
enum ProtocolCommand {
    RunShell { shell: String, args: Vec<String> },
    Install { url: String },
    Open,
}
```

### 错误处理和日志

```rust
#[derive(Debug, thiserror::Error)]
pub enum IpcError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("Connection failed")]
    ConnectionFailed,
    
    #[error("Command failed: {0}")]
    CommandFailed(String),
    
    #[error("Invalid response")]
    InvalidResponse,
    
    #[error("Server not running")]
    ServerNotRunning,
}

// IPC操作日志
use tracing::{info, warn, error, debug};

impl FountIpcServer {
    async fn handle_connection(&self, conn: LocalSocketStream) -> Result<(), IpcError> {
        let peer_addr = conn.peer_addr().unwrap_or_else(|_| "unknown".to_string());
        info!("New IPC connection from: {}", peer_addr);
        
        match self.process_connection(conn).await {
            Ok(()) => {
                info!("IPC connection closed normally: {}", peer_addr);
            }
            Err(e) => {
                error!("IPC connection error from {}: {}", peer_addr, e);
            }
        }
        
        Ok(())
    }
}
```

### 性能优化

```rust
// 连接池
use tokio::sync::Mutex;
use std::collections::VecDeque;

pub struct IpcConnectionPool {
    connections: Mutex<VecDeque<LocalSocketStream>>,
    socket_path: String,
    max_size: usize,
}

impl IpcConnectionPool {
    pub async fn get_connection(&self) -> Result<LocalSocketStream, IpcError> {
        let mut connections = self.connections.lock().await;
        
        if let Some(conn) = connections.pop_front() {
            // 检查连接是否仍然有效
            if self.is_connection_valid(&conn).await {
                return Ok(conn);
            }
        }
        
        // 创建新连接
        LocalSocketStream::connect(&self.socket_path)
            .map_err(|e| IpcError::ConnectionFailed)
    }
    
    pub async fn return_connection(&self, conn: LocalSocketStream) {
        let mut connections = self.connections.lock().await;
        if connections.len() < self.max_size {
            connections.push_back(conn);
        }
        // 超过最大大小时自动丢弃连接
    }
}
```

这个选择确保了我们能够实现高效、可靠的进程间通信，同时保持跨平台兼容性和良好的开发体验。
