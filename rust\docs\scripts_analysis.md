# Fount脚本工具模块分析

## 工具模块概览

Fount的脚本工具模块提供了系统运行所需的各种基础工具和实用函数，涵盖了从文件操作到系统集成的各个方面。

## 核心工具模块

### 1. console.mjs - 增强控制台
**功能**: 提供增强的控制台输出功能
**主要特性**:
- 支持刷新行输出 (`freshLine`) - 在同一行更新内容
- 包装标准console方法 (log, dir, error)
- 使用ANSI转义序列控制光标

**关键依赖**:
- `ansi-escapes` - ANSI转义序列

**核心接口**:
```javascript
{
    log: (...args) => void,
    dir: (...args) => void, 
    error: (...args) => void,
    freshLine: (id, ...args) => void  // 可刷新的日志行
}
```

**Rust映射**: `console.rs` (使用crossterm crate)

### 2. json_loader.mjs - JSON文件操作
**功能**: JSON文件的读取、写入和安全处理
**主要接口**:
- `loadJsonFile(filename)` - 加载JSON文件
- `loadJsonFileIfExists(filename, defaultvalue)` - 安全加载，不存在时返回默认值
- `saveJsonFile(filename, json)` - 保存JSON文件，格式化输出

**特点**:
- 同步文件操作
- 错误处理和日志记录
- 格式化JSON输出 (制表符缩进)

**Rust映射**: `json_loader.rs` (使用serde_json)

### 3. i18n.mjs - 国际化系统
**功能**: 多语言支持和本地化
**主要职责**:
- 语言环境检测 (`localhostLocales`)
- 翻译文本获取 (`geti18n`)
- 参数插值支持
- 嵌套键值访问

**语言检测来源**:
- 环境变量 (LANG, LANGUAGE, LC_ALL)
- 系统locale命令
- 浏览器语言设置
- 默认回退到 'en-UK'

**核心接口**:
```javascript
geti18n(key, params = {}) => Promise<string>
getLocaleData(locales) => Promise<object>
```

**Rust映射**: `i18n.rs` (使用fluent或类似的i18n库)

### 4. exec.mjs - 进程执行
**功能**: 异步进程执行包装
**实现**: 将Node.js的`child_process.exec`转换为Promise
**接口**: `exec(command) => Promise<{stdout, stderr}>`

**Rust映射**: `exec.rs` (使用tokio::process)

### 5. escape.mjs - 字符串转义工具
**功能**: 各种字符串转义和反转义
**主要函数**:
- `escapeRegExp(string)` - 正则表达式转义
- `unescapeRegExp(string)` - 正则表达式反转义
- `unicodeEscapeToChar(str)` - Unicode转义序列转字符
- `unescapeUnicode(str)` - Unicode反转义

**Rust映射**: `escape.rs` (使用regex crate)

### 6. await_timeout.mjs - 超时控制
**功能**: 为Promise添加超时控制
**接口**: `with_timeout(ms, promise)` - 包装Promise添加超时

**Rust映射**: `await_timeout.rs` (使用tokio::time::timeout)

### 7. proxy.mjs - 代理工具
**功能**: 创建完全拦截的代理对象
**特点**:
- `FullProxy(base)` - 创建完全代理
- 拦截所有Reflect操作
- 动态目标对象获取

**用途**: 在插件系统中实现动态对象访问

**Rust映射**: 使用trait对象或动态分发实现

### 8. ms.mjs - 时间解析
**功能**: 时间字符串解析为毫秒
**支持格式**: "1s", "5m", "2h", "1d"
**接口**: `ms(duration) => number`

**Rust映射**: `ms.rs` (使用humantime crate)

### 9. env.mjs - 环境检测
**功能**: 运行环境检测
**检测项**:
- `in_docker` - 是否在Docker容器中
- `in_termux` - 是否在Termux环境中

**检测方法**:
- Docker: 检查 `/.dockerenv` 文件和 `/proc/1/cgroup`
- Termux: 检查 `/data/data/com.termux` 目录

**Rust映射**: `env.rs`

### 10. ratelimit.mjs - 速率限制和IP检测
**功能**: 网络请求速率限制和本地IP检测
**主要功能**:
- `is_local_ip(ip)` - 检测是否为本地IP
- `is_local_ip_from_req(req)` - 从请求中检测本地IP
- 支持Docker环境的host.docker.internal

**本地IP列表**:
- 127.0.0.1 (IPv4 localhost)
- ::1 (IPv6 localhost)
- Docker host.docker.internal

**Rust映射**: `ratelimit.rs` (使用governor crate)

### 11. tray.mjs - 系统托盘
**功能**: 系统托盘图标和菜单
**主要职责**:
- 创建系统托盘图标
- 处理托盘菜单点击
- 跨平台图标支持 (Windows .ico, 其他平台 .png)

**关键依赖**:
- `systray` - 系统托盘库

**功能**:
- 退出应用程序
- 显示应用状态

**Rust映射**: `tray.rs` (使用tray-icon crate)

### 12. notify.mjs - 系统通知
**功能**: 跨平台系统通知
**关键依赖**:
- `node-notifier` - 系统通知库

**特点**:
- 支持不同平台的原生通知
- Docker和Termux环境适配

**Rust映射**: `notify.rs` (使用notify-rust crate)

### 13. discordrpc.mjs - Discord Rich Presence
**功能**: Discord状态显示集成
**用途**: 在Discord中显示Fount运行状态

**Rust映射**: `discord_rpc.rs` (使用discord-rich-presence crate)

### 14. verifycode.mjs - 验证码系统
**功能**: 生成和验证一次性验证码
**用途**: 用户验证和安全操作

**Rust映射**: `verify_code.rs`

### 15. sentrytunnel.mjs - Sentry隧道
**功能**: Sentry错误报告代理
**用途**: 错误监控和报告

**Rust映射**: `sentry_tunnel.rs`

### 16. locale.mjs - 本地化工具
**功能**: 本地化信息处理和格式化
**配合i18n.mjs使用**

**Rust映射**: `locale.rs`

## 工具模块特点

### 1. 模块化设计
- 每个模块职责单一
- 最小化依赖
- 可独立使用

### 2. 异步优先
- 大部分操作支持异步
- Promise-based API
- 错误处理完善

### 3. 跨平台支持
- Windows/Linux/macOS兼容
- Docker和Termux环境适配
- 平台特定功能检测

### 4. 错误处理
- 统一的错误处理模式
- 详细的错误日志
- 优雅降级

## Rust移植策略

### 1. 依赖映射
```toml
[dependencies]
# 基础功能
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"

# 系统集成
crossterm = "0.27"          # console.mjs
notify-rust = "4.0"         # notify.mjs
tray-icon = "0.14"          # tray.mjs

# 网络和时间
reqwest = "0.11"            # HTTP客户端
humantime = "2.1"           # ms.mjs
governor = "0.6"            # ratelimit.mjs

# 国际化
fluent = "0.16"             # i18n.mjs
fluent-bundle = "0.15"

# 其他
regex = "1.0"               # escape.mjs
discord-rich-presence = "0.2" # discordrpc.mjs
```

### 2. 异步模型转换
- JavaScript Promise → Rust Future
- Node.js事件循环 → Tokio运行时
- 回调函数 → async/await

### 3. 错误处理转换
- JavaScript异常 → Rust Result<T, E>
- try-catch → match或?操作符
- 错误链 → anyhow::Error

### 4. 类型安全
- 动态类型 → 静态类型
- JSON对象 → Rust struct
- 类型验证和转换

## 关键挑战

1. **动态特性**: JavaScript的动态特性 vs Rust的静态类型
2. **异步模型**: 不同的异步编程模型
3. **系统集成**: 跨平台系统API调用
4. **依赖管理**: npm包 vs Rust crate
5. **性能优化**: 内存管理和性能考虑

## 实现优先级

1. **高优先级**: json_loader, console, i18n, exec
2. **中优先级**: env, ms, escape, await_timeout
3. **低优先级**: tray, notify, discordrpc, proxy
