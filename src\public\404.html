<!DOCTYPE html>
<html data-theme="dark">

<head>
	<meta charset="UTF-8">
	<meta name="darkreader-lock">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title data-i18n="404.title"></title>
	<link href="https://cdn.jsdelivr.net/npm/daisyui/daisyui.css" rel="stylesheet" type="text/css" />
	<link href="/base.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser"></script>
	<script type="module" src="/base.mjs"></script>
	<script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.2/dist/confetti.browser.min.js"></script>
	<audio id="explosion-sound" src="data:audio/mp3;base64,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" preload="auto"></audio>
	<audio id="click-sound" src="data:audio/mp3;base64,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" preload="auto"></audio>

	<style>
		/* Custom styles to override daisyUI or add specific game styles */
		#game-board td {
			border: 2px outset #c0c0c0;
			text-align: center;
			font-size: 1.25rem;
			/* Equivalent to 20px, using rem for accessibility */
			font-weight: bold;
			cursor: pointer;
		}

		#game-board td.clicked {
			border: 1px solid #808080;
			background-color: #e0e0e0;
		}

		#game-board td.flagged {
			color: red;
		}

		#game-board td.question {
			color: blue;
		}

		#game-board td.mine {
			color: red;
		}

		/* New Styles */
		#game-board td.correct-flag {
			color: green;
			/* Or any visual distinction you prefer */
		}

		#game-board td.wrong-flag {
			color: orange;
			/* Or any visual distinction you prefer */
		}

		.controls {
			width: 100%;
			max-width: 480px;
			/* Adjust as needed */
		}

		@media (max-width: 600px) {
			#game-board td {
				width: 25px;
				height: 25px;
				font-size: 1rem;
				/* Equivalent to 16px */
			}
		}
	</style>
</head>

<body>
	<div class="flex justify-center items-center h-screen px-4">
		<div class="text-center">
			<h1 class="text-6xl font-bold mb-4">404</h1>
			<p class="text-lg mb-8" data-i18n="404.pageNotFoundText"></p>

			<div class="container flex flex-col items-center gap-4 mb-8">
				<div class="controls flex justify-between items-center mb-2 w-full max-w-md">
					<div class="flex items-center gap-2">
						<label for="difficulty" class="w-full" data-i18n="404.MineSweeper.difficultyLabel"></label>
						<select id="difficulty" class="w-full select select-sm select-bordered">
							<option value="easy" selected data-i18n="404.MineSweeper.difficultyEasy"></option>
							<option value="medium" data-i18n="404.MineSweeper.difficultyMedium"></option>
							<option value="hard" data-i18n="404.MineSweeper.difficultyHard"></option>
							<option value="custom" data-i18n="404.MineSweeper.difficultyCustom"></option>
						</select>
					</div>
					<div class="flex items-center gap-2">
						<span data-i18n="404.MineSweeper.minesLeftLabel"></span>
						<span id="mines-left"></span>
					</div>
					<div class="flex items-center gap-2">
						<span data-i18n="404.MineSweeper.timeLabel"></span>
						<span id="timer">0</span>
					</div>
					<button id="restart-button" class="btn btn-sm" data-i18n="404.MineSweeper.restartButton"></button>
					<button id="sound-toggle" class="btn btn-sm">🔇</button>
				</div>

				<div id="custom-options" class="hidden flex flex-wrap justify-center gap-2 max-w-md">
					<div class="flex items-center gap-2">
						<label for="custom-rows" class="text-sm" data-i18n="404.MineSweeper.rowsLabel"></label>
						<input type="number" id="custom-rows" min="5" max="999" value="16" class="input input-sm input-bordered w-24">
					</div>
					<div class="flex items-center gap-2">
						<label for="custom-cols" class="text-sm" data-i18n="404.MineSweeper.colsLabel"></label>
						<input type="number" id="custom-cols" min="5" max="999" value="16" class="input input-sm input-bordered w-24">
					</div>
					<div class="flex items-center gap-2">
						<label for="custom-mines" class="text-sm" data-i18n="404.MineSweeper.minesCountLabel"></label>
						<input type="number" id="custom-mines" min="1" value="40" class="input input-sm input-bordered w-24">
					</div>
				</div>

				<div class="overflow-x-auto">
					<table id="game-board" class="table-compact"></table>
				</div>
				<div id="message" class="text-lg font-bold mt-2"></div>
			</div>

			<a href="/" class="btn btn-primary" data-i18n="404.homepageButton"></a>
		</div>
	</div>
	<script type="module">
		import { initTranslations, geti18n } from '/scripts/i18n.mjs'
		import { applyTheme } from '/scripts/theme.mjs'
		applyTheme()
		initTranslations('404')

		const boardElement = document.getElementById('game-board')
		const minesLeftElement = document.getElementById('mines-left')
		const timerElement = document.getElementById('timer')
		const messageElement = document.getElementById('message')
		const restartButton = document.getElementById('restart-button')
		const difficultySelect = document.getElementById('difficulty')
		const customOptions = document.getElementById('custom-options')
		const customRowsInput = document.getElementById('custom-rows')
		const customColsInput = document.getElementById('custom-cols')
		const customMinesInput = document.getElementById('custom-mines')
		const soundToggle = document.getElementById('sound-toggle')
		const clickSound = document.getElementById('click-sound') // 假设音效元素已定义，如果不需要可以删除
		const explosionSound = document.getElementById('explosion-sound') // 假设音效元素已定义，如果不需要可以删除

		let rows = 9
		let cols = 9
		let mines = 10
		let board = []
		let gameStarted = false
		let timerInterval
		let seconds = 0
		let flaggedMines = 0
		let isMuted = true
		let isGameOver = false // Track game over state
		customOptions.classList.toggle('hidden', difficultySelect.value !== 'custom')

		// 默认地雷比例
		const DEFAULT_MINE_RATIO = 0.15625

		function launchConfetti() {
			confetti({
				particleCount: 100,
				spread: 70,
				origin: { y: 0.6 }
			})
		}

		/**
		 * 更新单元格显示状态 (根据单元格数据更新 DOM 样式和内容)
		 * @param {number} row 行索引
		 * @param {number} col 列索引
		 */
		function updateCellDisplay(row, col) {
			const cell = board[row][col]
			const td = boardElement.rows[row].cells[col]
			td.classList.remove('clicked', 'flagged', 'question', 'correct-flag', 'wrong-flag', 'mine') // 移除所有状态 class
			td.textContent = '' // 重置文本内容

			if (isGameOver && cell.isFlagged)  // 游戏结束后旗帜的特殊显示
				if (!cell.isMine) {
					td.textContent = '❌'
					td.classList.add('wrong-flag')
				} else {
					td.textContent = '🚩'
					td.classList.add('correct-flag')
				}
			else if (cell.isRevealed) {
				td.classList.add('clicked')
				if (cell.isMine) {
					td.textContent = '💣'
					td.classList.add('mine')
				} else if (cell.adjacentMines > 0) {
					td.textContent = cell.adjacentMines
					td.style.color = ['blue', 'green', 'red', 'purple', 'maroon', 'turquoise', 'black', 'gray'][cell.adjacentMines - 1]
				}
			} else if (cell.isFlagged) {
				td.textContent = '🚩'
				td.classList.add('flagged')
			} else if (cell.isQuestion) {
				td.textContent = '❓'
				td.classList.add('question')
			}
		}

		/**
		 * 初始化游戏参数 (根据难度设置行数、列数、地雷数)
		 */
		function initGame() {
			const difficulty = difficultySelect.value
			switch (difficulty) {
				case 'easy':
					rows = 9
					cols = 9
					mines = 10
					break
				case 'medium':
					rows = 16
					cols = 16
					mines = 40
					break
				case 'hard':
					rows = 16
					cols = 30
					mines = 99
					break
				default: // custom 难度
					updateCustomSettings() // 更新自定义设置 (合并了 updateRowsCols 和 updateMines)
					break
			}
			flaggedMines = 0
			minesLeftElement.textContent = mines - flaggedMines
			resetTimer()
			gameStarted = false
			isGameOver = false // 重置游戏结束状态
			messageElement.textContent = ''
			setControlsEnabled(true)
		}

		/**
		 * 创建游戏棋盘 (生成 board 数组和 HTML table)
		 */
		function createBoard() {
			board = []
			boardElement.innerHTML = ''

			for (let i = 0; i < rows; i++) {
				const row = []
				const tr = document.createElement('tr')
				for (let j = 0; j < cols; j++) {
					const cell = {
						isMine: false,
						isRevealed: false,
						isFlagged: false,
						isQuestion: false,
						adjacentMines: 0
					}
					row.push(cell)

					const td = document.createElement('td')
					td.addEventListener('click', () => cellClickHandler(i, j)) // 使用统一的点击处理函数
					td.addEventListener('contextmenu', (event) => cellRightClickHandler(i, j, event)) // 使用统一的右键处理函数
					tr.appendChild(td)
					td.classList.add('w-8', 'h-8', 'p-0') // Tailwind sizing and padding
				}
				board.push(row)
				boardElement.appendChild(tr)
			}
		}

		/**
		 * 放置地雷 (随机放置，并确保第一次点击不是地雷)
		 * @param {number} firstClickRow 第一次点击的行索引
		 * @param {number} firstClickCol 第一次点击的列索引
		 */
		function placeMines(firstClickRow, firstClickCol) {
			const cells = []
			for (let i = 0; i < rows; i++)
				for (let j = 0; j < cols; j++)
					cells.push({ row: i, col: j })

			// Fisher-Yates 洗牌算法
			function shuffle(array) {
				for (let i = array.length - 1; i > 0; i--) {
					const j = Math.floor(Math.random() * (i + 1));
					[array[i], array[j]] = [array[j], array[i]]
				}
			}

			shuffle(cells)

			// Place mines in the first 'mines' cells of the shuffled array
			for (let i = 0; i < Math.min(mines, cells.length); i++) {
				const { row, col } = cells[i]
				board[row][col].isMine = true
			}

			// 确保第一次点击的格子不是地雷，如果是，则移动地雷
			if (board[firstClickRow][firstClickCol].isMine) {
				board[firstClickRow][firstClickCol].isMine = false // 移除第一次点击格子的地雷

				// 找到 shuffled 'cells' 数组中最后一个不是地雷的格子
				for (let i = cells.length - 1; i >= 0; i--) {
					const { row, col } = cells[i]
					if (!board[row][col].isMine) {
						board[row][col].isMine = true // 将地雷放在这里
						break // 移动一个地雷后退出循环
					}
				}
			}

			// 计算相邻地雷数
			for (let i = 0; i < rows; i++)
				for (let j = 0; j < cols; j++)
					if (!board[i][j].isMine)
						board[i][j].adjacentMines = countAdjacentMines(i, j)
		}

		/**
		 * 计算相邻地雷数
		 * @param {number} row 行索引
		 * @param {number} col 列索引
		 * @returns {number} 相邻地雷数
		 */
		function countAdjacentMines(row, col) {
			let count = 0
			for (let i = row - 1; i <= row + 1; i++)
				for (let j = col - 1; j <= col + 1; j++)
					if (i >= 0 && i < rows && j >= 0 && j < cols && board[i][j].isMine)
						count++
			return count
		}

		/**
		 * 单元格点击处理函数 (左键)
		 * @param {number} row 行索引
		 * @param {number} col 列索引
		 */
		function cellClickHandler(row, col) {
			startGameIfNecessary(row, col) // 启动游戏 (如果尚未启动)

			const cell = board[row][col]
			if (cell.isRevealed || cell.isFlagged || cell.isQuestion)
				return // 已揭开、已标记或问号，不处理


			if (!isMuted) clickSound?.play() // 使用可选链式调用，避免 clickSound 为 null 时报错

			if (cell.isMine) {
				if (!isMuted) explosionSound?.play() // 使用可选链式调用
				endGame(false) // 踩到地雷，游戏结束 (失败)
			} else {
				revealCell(row, col) // 揭开单元格
				if (checkWin())
					endGame(true) // 检查是否胜利
			}
		}

		/**
		 * 单元格右键点击处理函数
		 * @param {number} row 行索引
		 * @param {number} col 列索引
		 * @param {Event} event 事件对象
		 */
		function cellRightClickHandler(row, col, event) {
			event.preventDefault() // 阻止默认右键菜单
			startGameIfNecessary(row, col) // 启动游戏 (如果尚未启动)

			const cell = board[row][col]
			if (cell.isRevealed)
				return // 已揭开，不处理


			if (!isMuted) clickSound?.play() // 使用可选链式调用

			if (!cell.isFlagged && !cell.isQuestion) {
				cell.isFlagged = true // 标记为旗帜
				flaggedMines++
			} else if (cell.isFlagged) {
				cell.isFlagged = false // 取消旗帜
				cell.isQuestion = true // 标记为问号
				flaggedMines--
			} else
				cell.isQuestion = false // 取消问号


			updateCellDisplay(row, col)
			minesLeftElement.textContent = mines - flaggedMines
		}

		/**
		 * 启动游戏 (如果尚未启动)
		 * @param {number} row 第一次点击的行索引
		 * @param {number} col 第一次点击的列索引
		 */
		function startGameIfNecessary(row, col) {
			if (!gameStarted) {
				gameStarted = true
				placeMines(row, col) // 放置地雷
				startTimer() // 启动计时器
				setControlsEnabled(false) // 禁用难度选择等控件
			}
		}


		/**
		 * 揭开单元格 (递归揭开周围空白单元格)
		 * @param {number} row 行索引
		 * @param {number} col 列索引
		 */
		function revealCell(row, col) {
			if (row < 0 || row >= rows || col < 0 || col >= cols || board[row][col].isRevealed || board[row][col].isFlagged)
				return // 越界、已揭开或已标记，停止递归

			const cell = board[row][col]
			cell.isRevealed = true
			updateCellDisplay(row, col)

			if (cell.adjacentMines === 0)
				// 递归揭开周围单元格
				for (let i = row - 1; i <= row + 1; i++)
					for (let j = col - 1; j <= col + 1; j++)
						if (!(i === row && j === col))  // 避免重复揭开自身
							revealCell(i, j)
		}

		/**
		 * 检查是否胜利 (所有非地雷单元格都被揭开)
		 * @returns {boolean} 是否胜利
		 */
		function checkWin() {
			for (let i = 0; i < rows; i++)
				for (let j = 0; j < cols; j++)
					if (!board[i][j].isRevealed && !board[i][j].isMine)
						return false // 还有未揭开的非地雷单元格，未胜利
			return true // 所有非地雷单元格都被揭开，胜利
		}

		/**
		 * 揭示所有地雷 (游戏结束时显示所有地雷)
		 */
		function revealAllMines() {
			for (let i = 0; i < rows; i++)
				for (let j = 0; j < cols; j++) {
					const cell = board[i][j]
					cell.isRevealed = true
					updateCellDisplay(i, j)
				}
		}

		/**
		 * 结束游戏
		 * @param {boolean} isWin 是否胜利
		 */
		function endGame(isWin) {
			stopTimer()
			isGameOver = true // 设置游戏结束状态
			revealAllMines() // 显示所有地雷
			messageElement.textContent = geti18n(isWin ? '404.MineSweeper.winMessage' : '404.MineSweeper.loseMessage') // 显示游戏结果消息
			if (isWin) launchConfetti()
			setControlsEnabled(true) // 重新启用控件
			// 禁用所有格子的点击事件
			for (let i = 0; i < rows; i++)
				for (let j = 0; j < cols; j++)
					boardElement.rows[i].cells[j].style.pointerEvents = 'none'
		}

		/**
		 * 启动计时器
		 */
		function startTimer() {
			clearInterval(timerInterval)
			seconds = 0
			timerInterval = setInterval(() => {
				seconds++
				timerElement.textContent = seconds
			}, 1000)
		}

		/**
		 * 停止计时器
		 */
		function stopTimer() {
			clearInterval(timerInterval)
		}

		/**
		 * 重置计时器
		 */
		function resetTimer() {
			stopTimer()
			seconds = 0
			timerElement.textContent = seconds
		}

		/**
		 * 处理难度选择改变事件
		 */
		difficultySelect.addEventListener('change', () => {
			customOptions.classList.toggle('hidden', difficultySelect.value !== 'custom') // 切换自定义选项的显示/隐藏
			initGame() // 初始化游戏参数
			startNewGame() // 创建新棋盘
		})

		/**
		 * 设置控件启用状态
		 * @param {boolean} enabled 是否启用
		 */
		function setControlsEnabled(enabled) {
			difficultySelect.disabled = !enabled
			customRowsInput.disabled = !enabled
			customColsInput.disabled = !enabled
			customMinesInput.disabled = !enabled

			const classMethod = enabled ? 'remove' : 'add'
			difficultySelect.classList[classMethod]('disabled')
			customRowsInput.classList[classMethod]('disabled')
			customColsInput.classList[classMethod]('disabled')
			customMinesInput.classList[classMethod]('disabled')
		}

		/**
		 * 处理声音开关点击事件
		 */
		soundToggle.addEventListener('click', () => {
			isMuted = !isMuted
			soundToggle.textContent = isMuted ? '🔇' : '🔊'
			soundToggle.setAttribute('aria-label', isMuted ? geti18n('404.MineSweeper.soundOff') : geti18n('404.MineSweeper.soundOn')) // Accessibility
		})
		soundToggle.setAttribute('aria-label', geti18n('404.MineSweeper.soundOff')) // Default accessibility label

		/**
		 * 更新自定义设置 (行数、列数、地雷数) - 合并了 updateRowsCols 和 updateMines 的逻辑
		 */
		function updateCustomSettings() {
			if (difficultySelect.value !== 'custom') return

			rows = parseInt(customRowsInput.value, 10) || rows
			cols = parseInt(customColsInput.value, 10) || cols

			customRowsInput.value = rows
			customColsInput.value = cols

			let newMines = parseInt(customMinesInput.value, 10)
			const maxMines = rows * cols - 1

			if (isNaN(newMines) || newMines < 1)
				newMines = Math.floor(rows * cols * DEFAULT_MINE_RATIO)

			newMines = Math.max(1, Math.min(maxMines, newMines))

			customMinesInput.value = newMines
			mines = newMines
			minesLeftElement.textContent = mines - flaggedMines
		}


		customRowsInput.addEventListener('input', () => {
			updateCustomSettings() // 更新自定义设置
			startNewGame() // 创建新棋盘
		})

		customColsInput.addEventListener('input', () => {
			updateCustomSettings() // 更新自定义设置
			startNewGame() // 创建新棋盘
		})

		customMinesInput.addEventListener('input', () => {
			updateCustomSettings() // 更新自定义设置
			startNewGame() // 创建新棋盘
		})

		/**
		 * 处理重新开始按钮点击事件
		 */
		restartButton.addEventListener('click', () => {
			initGame()  // 初始化游戏设置
			startNewGame() // 开始新游戏（创建棋盘等）
		})

		/**
		 * 开始新游戏 (重置游戏状态并创建新棋盘)
		 */
		function startNewGame() {
			flaggedMines = 0
			minesLeftElement.textContent = mines - flaggedMines
			resetTimer()
			createBoard()
			gameStarted = false
			isGameOver = false // 重置游戏结束状态
			messageElement.textContent = ''
			setControlsEnabled(true)

			// 重新启用所有格子的点击事件 (游戏结束后需要重新启用)
			for (let i = 0; i < rows; i++)
				for (let j = 0; j < cols; j++)
					if (boardElement.rows[i] && boardElement.rows[i].cells[j])
						boardElement.rows[i].cells[j].style.pointerEvents = '' // Or 'auto'
		}

		// 页面加载完成时初始化游戏
		initGame()
		startNewGame()
	</script>
</body>

</html>
