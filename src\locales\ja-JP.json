{"lang": "ja-<PERSON>", "fountConsole": {"server": {"start": "サーバーを起動しています。", "starting": "サーバー起動中...", "ready": "サーバーが起動しました。", "usesdTime": "起動時間：${time}s", "showUrl": {"https": "HTTPSサーバーが ${url} で実行されています。", "http": "HTTPサーバーが ${url} で実行されています。"}, "standingBy": "スタンバイ中..."}, "jobs": {"restartingJob": "ユーザー「${username}」の${parttype}「${partname}」ジョブ（${uid}）を再起動しています。"}, "ipc": {"sendCommandFailed": "コマンドの送信に失敗しました：${error}", "invalidCommand": "無効なコマンドです。「fount runshell <ユーザー名> <シェル名> <パラメータ...>」を使用してください。", "runShellLog": "シェル「${shellname}」をユーザー「${username}」として実行、パラメータ：${args}", "invokeShellLog": "シェル「${shellname}」をユーザー「${username}」として呼び出し、パラメータ：${invokedata}", "unsupportedCommand": "サポートされていないコマンドタイプです。", "processMessageError": "IPCメッセージの処理中にエラーが発生しました：${error}", "invalidCommandFormat": "無効なコマンド形式です。", "socketError": "ソケットエラー：${error}", "instanceRunning": "別のインスタンスが既に実行中です。", "serverStartPrefix": "サーバー起動", "serverStarted": "IPCサーバーが起動しました。", "parseResponseFailed": "サーバー応答の解析に失敗しました：${error}", "cannotParseResponse": "サーバー応答を解析できません。", "unknownError": "不明なエラーが発生しました。"}, "partManager": {"git": {"noUpstream": "ブランチ「${currentBranch}」にアップストリームブランチが設定されていません。更新チェックをスキップします。", "dirtyWorkingDirectory": "作業ディレクトリがクリーンではありません。更新前に変更をスタッシュするかコミットしてください。", "updating": "リモートリポジトリから更新中です...", "localAhead": "ローカルブランチがリモートブランチよりも進んでいます。更新は不要です。", "diverged": "ローカルブランチとリモートブランチが分岐しました。強制的に更新しています...", "upToDate": "既に最新です。", "updateFailed": "リモートリポジトリから部品を更新できませんでした：${error}"}, "partInitTime": "${parttype}部品「${partname}」初期化時間：${time}s", "partLoadTime": "${parttype}部品「${partname}」読み込み時間：${time}s"}, "web": {"requestReceived": "リクエストを受信しました：${method} ${url}"}, "route": {"setLanguagePreference": "ユーザー「${username}」が優先言語を設定しました：${preferredLanguages}"}, "auth": {"tokenVerifyError": "トークン検証エラー：${error}", "refreshTokenError": "リフレッシュトークンエラー：${error}", "logoutRefreshTokenProcessError": "ログアウトリフレッシュトークン処理エラー：${error}", "revokeTokenNoJTI": "JTIのないトークンは取り消せません。", "accountLockedLog": "ログイン試行が複数回失敗したため、ユーザー「${username}」のアカウントはロックされました。"}, "verification": {"codeGeneratedLog": "認証コード：${code}（60秒後に期限切れとなります）", "codeNotifyTitle": "認証コード", "codeNotifyBody": "認証コード：${code}（60秒後に期限切れとなります）"}, "tray": {"readIconFailed": "アイコンファイルの読み込みに失敗しました：${error}", "createTrayFailed": "トレイアイコンの作成に失敗しました：${error}"}, "discordbot": {"botStarted": "キャラクター「${charname}」がDiscordボット「${botusername}」にログインしました。"}, "telegrambot": {"botStarted": "キャラクター「${charname}」がTelegramボット「${botusername}」にログインしました。"}}, "protocolhandler": {"title": "Fountプロトコルの処理", "processing": "プロトコルを処理中です...", "invalidProtocol": "無効なプロトコルです。", "insufficientParams": "パラメータが不足しています。", "unknownCommand": "不明なコマンドです。", "shellCommandSent": "シェルコマンドを送信しました。", "shellCommandFailed": "シェルコマンドの送信に失敗しました。", "shellCommandError": "シェルコマンドの送信中にエラーが発生しました。"}, "auth": {"title": "認証", "subtitle": "ユーザーデータはローカルに保存されます。", "usernameLabel": "ユーザー名:", "usernameInput": {"placeholder": "ユーザー名を入力してください"}, "passwordLabel": "パスワード:", "passwordInput": {"placeholder": "パスワードを入力してください"}, "confirmPasswordLabel": "パスワード確認:", "confirmPasswordInput": {"placeholder": "もう一度パスワードを入力してください"}, "verificationCodeLabel": "認証コード:", "verificationCodeInput": {"placeholder": "認証コードを入力してください"}, "sendCodeButton": "認証コードを送信", "login": {"title": "ログイン", "submitButton": "ログイン", "toggleLink": {"text": "アカウントをお持ちでない場合", "link": "今すぐ登録"}}, "register": {"title": "登録", "submitButton": "登録", "toggleLink": {"text": "既にアカウントをお持ちの場合", "link": "今すぐログイン"}}, "error": {"passwordMismatch": "パスワードが一致しません。", "loginError": "ログイン中にエラーが発生しました。", "registrationError": "登録中にエラーが発生しました。", "verificationCodeError": "認証コードが正しくないか、有効期限が切れています。", "verificationCodeSent": "認証コードを正常に送信しました。", "verificationCodeSendError": "認証コードの送信に失敗しました。", "verificationCodeRateLimit": "認証コードの送信リクエストが多すぎます。しばらくしてから再度お試しください。", "lowPasswordStrength": "パスワードの強度が低すぎます。", "accountAlreadyExists": "このアカウントは既に存在します。"}, "passwordStrength": {"veryWeak": "非常に弱い", "weak": "弱い", "normal": "普通", "strong": "強い", "veryStrong": "非常に強い"}}, "tutorial": {"title": "チュートリアルはいかがですか？", "modal": {"title": "Fountへようこそ！", "instruction": "入門チュートリアルを開始しますか？", "buttons": {"start": "チュートリアル開始", "skip": "スキップ"}}, "endScreen": {"title": "素晴らしい！チュートリアル完了！", "subtitle": "基本操作はこれで完了です！", "endButton": "始めましょう！"}, "progressMessages": {"mouseMove": "マウス${mouseIcon}を手で掴んで動かしてみてください。", "keyboardPress": "キーボード${keyboardIcon}で何かキーを押してみてください。", "mobileTouchMove": "スマートフォン${phoneIcon}の画面に指で触れて、そのままスライドさせてみてください。", "mobileClick": "スマートフォン${phoneIcon}の画面を指でタップしてみてください。"}}, "home": {"title": "ホーム", "escapeConfirm": "Fountを終了しますか？", "filterInput": {"placeholder": "検索..."}, "sidebarTitle": "詳細", "itemDescription": "詳細を表示するには、ここから項目を選択してください。", "noDescription": "説明はありません。", "alerts": {"fetchHomeRegistryFailed": "ホームレジストリ情報の取得に失敗しました。"}, "functionMenu": {"icon": {"alt": "機能メニュー"}}, "chars": {"tab": "キャラクター", "title": "キャラクター選択", "subtitle": "キャラクターを選択して、チャットを始めましょう！", "none": "何も表示するものがありません", "card": {"refreshButton": {"alt": "更新", "title": "更新"}, "noTags": "タグなし", "version": "バージョン", "author": "作成者", "homepage": "ホームページ", "issuepage": "問題報告ページ", "defaultCheckbox": {"title": "デフォルトキャラクターとして設定"}}}, "worlds": {"tab": "ワールド", "title": "ワールド選択", "subtitle": "ワールドを選んで、没入体験を！", "none": "何も表示するものがありません", "card": {"refreshButton": {"alt": "更新", "title": "更新"}, "noTags": "タグなし", "version": "バージョン", "author": "作成者", "homepage": "ホームページ", "issuepage": "問題報告ページ", "defaultCheckbox": {"title": "デフォルトワールドとして設定"}}}, "personas": {"tab": "ペルソナ", "title": "ペルソナ選択", "subtitle": "ペルソナを選択して、様々な役割を体験しましょう。", "none": "何も表示するものがありません", "card": {"refreshButton": {"alt": "更新", "title": "更新"}, "noTags": "タグなし", "version": "バージョン", "author": "作成者", "homepage": "ホームページ", "issuepage": "問題報告ページ", "defaultCheckbox": {"title": "デフォルトペルソナとして設定"}}}}, "themeManage": {"title": "テーマ管理", "instruction": "テーマを選択してください！", "themes": {"auto": "自動", "light": "ライト", "dark": "ダーク", "cupcake": "カップケーキ", "bumblebee": "バンブルビー", "emerald": "エメラルド", "corporate": "コーポレート", "synthwave": "シンセウェイヴ", "retro": "レトロ", "cyberpunk": "サイバーパンク", "valentine": "バレンタインデー", "halloween": "ハロウィーン", "garden": "ガーデン", "forest": "フォレスト", "aqua": "アクア", "lofi": "lo-fi", "pastel": "パステル", "fantasy": "ファンタジー", "wireframe": "ワイヤーフレーム", "black": "ブラック", "luxury": "ラグジュアリー", "dracula": "ドラキュラ", "cmyk": "CMYK", "autumn": "オータム", "business": "仕事", "acid": "アシッド", "lemonade": "レモネード", "night": "ナイト", "coffee": "コーヒー", "winter": "ウィンター", "dim": "ディム", "nord": "ノルディック", "sunset": "サンセット", "caramellatte": "キャラメルラテ", "abyss": "アビス", "silk": "シルク"}}, "import": {"title": "インポート", "tabs": {"fileImport": "ファイルからインポート", "textImport": "テキストからインポート"}, "dropArea": {"icon": {"alt": "アップロードアイコン"}, "text": "ここにファイルをドラッグ＆ドロップするか、クリックしてファイルを選択"}, "textArea": {"placeholder": "インポートするテキストを入力してください..."}, "buttons": {"import": "インポート"}, "alerts": {"importSuccess": "正常にインポートされました。", "importFailed": "インポートに失敗しました: ${error}", "unknownError": "不明なエラーが発生しました。"}, "errors": {"noFileSelected": "ファイルを選択してください。", "fileImportFailed": "ファイルのインポートに失敗しました: ${message}", "noTextContent": "テキスト内容を入力してください。", "textImportFailed": "テキストのインポートに失敗しました: ${message}", "unknownError": "不明なエラーが発生しました。", "handler": "ハンドラ", "error": "エラー"}, "fileItem": {"removeButton": {"title": "削除"}, "removeButtonIcon": {"alt": "削除"}}}, "aisource_editor": {"title": "AIソースエディタ", "fileList": {"title": "AIソース一覧", "addButton": {"title": "+"}}, "configTitle": "AIソース設定", "generatorSelect": {"label": "ジェネレータを選択", "placeholder": "選択してください"}, "buttons": {"save": "保存", "delete": "削除"}, "alerts": {"fetchFileListFailed": "ファイルリストの取得に失敗しました: ${error}", "fetchGeneratorListFailed": "ジェネレータリストの取得に失敗しました: ${error}", "fetchFileDataFailed": "ファイルデータの取得に失敗しました: ${error}", "noFileSelectedSave": "保存するファイルが選択されていません。", "saveFileFailed": "ファイルの保存に失敗しました: ${error}", "noFileSelectedDelete": "削除するファイルが選択されていません。", "deleteFileFailed": "ファイルの削除に失敗しました: ${error}", "invalidFileName": "ファイル名に次の文字は使用できません： / \\ : * ? \" < > |", "addFileFailed": "ファイルの追加に失敗しました: ${error}", "fetchConfigTemplateFailed": "設定テンプレートの取得に失敗しました。", "noGeneratorSelectedSave": "保存する前にジェネレータを選択してください。"}, "confirm": {"unsavedChanges": "保存されていない変更があります。変更を破棄しますか？", "deleteFile": "このファイルを削除してもよろしいですか？", "unsavedChangesBeforeUnload": "保存されていない変更があります。このページから移動しますか？"}, "prompts": {"newFileName": "新しいAIソースのファイル名を入力してください（拡張子なし）："}, "editor": {"disabledIndicator": "最初にジェネレータを選択してください。"}}, "part_config": {"title": "部品設定", "pageTitle": "部品設定", "labels": {"partType": "部品タイプを選択", "part": "部品を選択"}, "placeholders": {"partTypeSelect": "選択してください", "partSelect": "選択してください"}, "editor": {"title": "部品設定", "disabledIndicator": "この部品は設定に対応していません。", "buttons": {"save": "保存"}}, "errorMessage": {"icon": {"alt": "エラー"}}, "alerts": {"fetchPartTypesFailed": "部品タイプの取得に失敗しました。", "fetchPartsFailed": "部品リストの取得に失敗しました。", "loadEditorFailed": "エディタの読み込みに失敗しました。", "saveConfigFailed": "部品設定の保存に失敗しました。", "unsavedChanges": "保存されていない変更があります。変更を破棄しますか？", "beforeUnload": "保存されていない変更があります。このページから移動しますか？"}}, "uninstall": {"title": "アンインストール", "titleWithName": "${type}/${name} をアンインストール", "confirmMessage": "${type}: ${name} をアンインストールしてもよろしいですか？", "invalidParamsTitle": "無効なパラメータ", "infoMessage": {"icon": {"alt": "情報アイコン"}}, "errorMessage": {"icon": {"alt": "エラーアイコン"}}, "buttons": {"confirm": "アンインストール確定", "cancel": "キャンセル", "back": "戻る"}, "alerts": {"success": "${type}: ${name} は正常にアンインストールされました。", "failed": "アンインストールに失敗しました: ${error}", "invalidParams": "無効なリクエストパラメータです。", "httpError": "HTTPエラーが発生しました！ステータスコード: ${status}"}}, "chat": {"new": {"title": "新しいチャット"}, "title": "チャット", "sidebar": {"world": {"icon": {"alt": "ワールドアイコン"}, "title": "ワールド"}, "persona": {"icon": {"alt": "ユーザーペルソナアイコン"}, "title": "ユーザーペルソナ"}, "charList": {"icon": {"alt": "キャラクターリストアイコン"}, "title": "キャラクターリスト", "buttons": {"addChar": {"title": "キャラクター追加"}, "addCharIcon": {"alt": "キャラクター追加アイコン"}}}, "noSelection": "未選択", "noDescription": "説明なし"}, "chatArea": {"title": "チャット", "menuButton": {"title": "メニュー"}, "menuButtonIcon": {"alt": "メニューアイコン"}, "input": {"placeholder": "メッセージを入力...\\nCtrl+Enterで送信"}, "sendButton": {"title": "送信"}, "sendButtonIcon": {"alt": "送信アイコン"}, "uploadButton": {"title": "アップロード"}, "uploadButtonIcon": {"alt": "アップロードアイコン"}, "voiceButton": {"title": "音声入力"}, "voiceButtonIcon": {"alt": "音声入力アイコン"}, "photoButton": {"title": "写真"}, "photoButtonIcon": {"alt": "写真アイコン"}}, "rightSidebar": {"title": "詳細情報"}, "messageList": {"confirmDeleteMessage": "このメッセージを削除しますか？"}, "voiceRecording": {"errorAccessingMicrophone": "マイクへのアクセスに失敗しました。"}, "messageView": {"buttons": {"edit": {"title": "編集"}, "editIcon": {"alt": "編集アイコン"}, "delete": {"title": "削除"}, "deleteIcon": {"alt": "削除アイコン"}}}, "messageEdit": {"input": {"placeholder": "内容を入力してください..."}, "buttons": {"confirm": {"title": "確認"}, "confirmIcon": {"alt": "確認アイコン"}, "cancel": {"title": "キャンセル"}, "cancelIcon": {"alt": "キャンセルアイコン"}, "upload": {"title": "アップロード"}, "uploadIcon": {"alt": "アップロードアイコン"}}}, "attachment": {"buttons": {"download": {"title": "ダウンロード"}, "downloadIcon": {"alt": "ダウンロードアイコン"}, "delete": {"title": "削除"}, "deleteIcon": {"alt": "削除アイコン"}}}, "charCard": {"frequencyLabel": "頻度", "buttons": {"removeChar": {"title": "チャットから削除"}, "removeCharIcon": {"alt": "キャラクター削除アイコン"}, "forceReply": {"title": "強制返信"}, "forceReplyIcon": {"alt": "強制返信アイコン"}}}}, "chat_history": {"title": "チャット履歴", "pageTitle": "チャット履歴", "sortOptions": {"time_desc": "日時（新しい順）", "time_asc": "日時（古い順）"}, "filterInput": {"placeholder": "検索..."}, "selectAll": "すべて選択", "buttons": {"reverseSelect": "選択を反転", "deleteSelected": "選択項目を削除", "exportSelected": "選択項目をエクスポート"}, "confirmDeleteChat": "「${chars}」とのチャット履歴を削除してもよろしいですか？", "confirmDeleteMultiChats": "選択した${count}件のチャット履歴を削除してもよろしいですか？", "alerts": {"noChatSelectedForDeletion": "削除するチャット履歴を選択してください。", "noChatSelectedForExport": "エクスポートするチャット履歴を選択してください。", "copyError": "コピーが失敗しました", "deleteError": "削除が失敗しました", "exportError": "エクスポートは失敗しました"}, "chatItemButtons": {"continue": "続行", "copy": "コピー", "export": "エクスポート", "delete": "削除"}}, "discord_bots": {"title": "Discordボット", "cardTitle": "Discordボット", "buttons": {"newBot": "新規作成", "deleteBot": "削除"}, "configCard": {"title": "ボット設定", "labels": {"character": "キャラクター", "apiKey": "Discord APIキー", "config": "設定"}, "charSelectPlaceholder": "キャラクターを選択", "apiKeyInput": {"placeholder": "APIキーを入力"}, "toggleApiKeyIcon": {"alt": "APIキーの表示切り替え"}, "buttons": {"saveConfig": "設定を保存", "startBot": "起動", "stopBot": "停止"}}, "prompts": {"newBotName": "新しいボット名を入力してください："}, "alerts": {"botExists": "ボット名「${botname}」は既に存在します。別の名前を使用してください。", "unsavedChanges": "保存されていない変更があります。変更を破棄しますか？", "configSaved": "設定を正常に保存しました。", "httpError": "HTTPエラーが発生しました。", "beforeUnload": "保存されていない変更があります。このページから移動しますか？"}}, "telegram_bots": {"title": "Telegramボット", "cardTitle": "Telegramボット管理", "buttons": {"newBot": "新規作成", "deleteBot": "削除"}, "configCard": {"title": "ボット設定", "labels": {"character": "連携キャラクター", "botToken": "Telegramボットトークン", "config": "設定"}, "charSelectPlaceholder": "キャラクターを選択", "botTokenInput": {"placeholder": "Telegramボットトークンを入力"}, "toggleBotTokenIcon": {"alt": "ボットトークンの表示切り替え"}, "buttons": {"saveConfig": "設定を保存", "startBot": "起動", "stopBot": "停止"}}, "prompts": {"newBotName": "新しいボット名を入力してください："}, "alerts": {"botExists": "ボット名「${botname}」は既に存在します。別の名前を使用してください。", "unsavedChanges": "保存されていない変更があります。変更を破棄しますか？", "configSaved": "設定を正常に保存しました！", "httpError": "HTTPエラーが発生しました。", "beforeUnload": "保存されていない変更があります。このページから移動しますか？"}}, "terminal_assistant": {"title": "ターミナルアシスタント", "initialMessage": "Fountは、お気に入りのキャラクターをターミナルに導入し、コーディング作業をサポートします！", "initialMessageLink": "詳細はこちら"}, "access": {"title": "他のデバイスからFountにアクセス", "heading": "他のデバイスからFountにアクセスしますか？", "instruction": {"sameLAN": "デバイスとFountホストが同じローカルネットワーク上にあることを確認してください。", "accessthis": "次のURLにアクセスしてください："}, "copyButton": "URLをコピー", "copied": "URLをクリップボードにコピーしました！"}, "proxy": {"title": "APIプロキシ", "heading": "OpenAI API プロキシアドレス", "instruction": "Fount内のAIソースを使用するには、このアドレスをOpenAI API形式に対応したアプリケーションに入力してください！", "copyButton": "アドレスをコピー", "copied": "アドレスをクリップボードにコピーしました！"}, "404": {"title": "ページが見つかりません", "pageNotFoundText": "おっと！存在しないページにアクセスしたようです。", "homepageButton": "ホームページに戻る", "MineSweeper": {"difficultyLabel": "難易度:", "difficultyEasy": "簡単", "difficultyMedium": "中級", "difficultyHard": "難しい", "difficultyCustom": "カスタム", "minesLeftLabel": "残りの地雷:", "timeLabel": "時間:", "restartButton": "リスタート", "rowsLabel": "行:", "colsLabel": "列:", "minesCountLabel": "地雷の数:", "winMessage": "おめでとうございます、あなたの勝利です！", "loseMessage": "ゲームオーバー。地雷を踏んでしまいました！", "soundOn": "サウンド オン", "soundOff": "サウンド オフ"}}, "userSettings": {"title": "ユーザー設定", "PageTitle": "ユーザー設定", "apiError": "APIリクエストに失敗しました：${message}", "generalError": "エラーが発生しました：${message}", "userInfo": {"title": "ユーザー情報", "usernameLabel": "ユーザー名:", "creationDateLabel": "アカウント作成日:", "folderSizeLabel": "ユーザーデータサイズ:", "folderPathLabel": "ユーザーデータパス:", "copyPathBtnTitle": "パスをコピー", "copiedAlert": "パスをクリップボードにコピーしました！"}, "changePassword": {"title": "パスワード変更", "currentPasswordLabel": "現在のパスワード:", "newPasswordLabel": "新しいパスワード:", "confirmNewPasswordLabel": "新しいパスワード（確認）:", "submitButton": "パスワードを変更", "errorMismatch": "新しいパスワードが一致しません。", "success": "パスワードが正常に変更されました。"}, "renameUser": {"title": "ユーザー名変更", "newUsernameLabel": "新しいユーザー名:", "submitButton": "ユーザー名を変更", "confirmMessage": "ユーザー名を変更しますか？変更後は再度ログインが必要です。", "success": "ユーザー名を「${newUsername}」に正常に変更しました。ログアウトします。"}, "userDevices": {"title": "ユーザーデバイス・セッション", "refreshButtonTitle": "リストを更新", "noDevicesFound": "デバイスまたはセッションが見つかりませんでした。", "deviceInfo": "デバイスID：${deviceId}", "thisDevice": "このデバイス", "deviceDetails": "最終オンライン：${lastSeen} | IPアドレス：${ipAddress} | UA：${userAgent}", "revokeButton": "取り消し", "revokeConfirm": "このデバイス/セッションのアクセス権を取り消しますか？", "revokeSuccess": "デバイス/セッションを正常に取り消しました。"}, "logout": {"title": "ログアウト", "description": "現在のデバイスからアカウントをログアウトします。", "buttonText": "ログアウト", "confirmMessage": "ログアウトしますか？", "successMessage": "正常にログアウトしました。ログインページに移動します..."}, "deleteAccount": {"title": "アカウント削除", "warning": "警告：この操作を行うと、アカウントと関連する全てのデータが完全に削除され、元に戻すことはできません。", "submitButton": "アカウントを削除", "confirmMessage1": "警告！アカウントを完全に削除しますか？この操作は元に戻せません。", "confirmMessage2": "削除を確認するには、ユーザー名「${username}」を入力してください：", "usernameMismatch": "入力されたユーザー名が現在のアカウントと一致しません。削除処理はキャンセルされました。", "success": "アカウントが正常に削除されました。ログアウトします。"}, "passwordConfirm": {"title": "操作の確認", "message": "続行するには、現在のパスワードを入力してください。", "passwordLabel": "パスワード:", "confirmButton": "確認", "cancelButton": "キャンセル"}}}