<div class="chat-message chat chat-start mb-4" id="message-${safeTimeStamp}" data-template-type="message">
	<div class="chat-image avatar">
		<div class="w-10 mask mask-squircle rounded-full">
			<img src="${avatar}" alt="${name}" />
		</div>
	</div>
	<div class="chat-bubble relative chat-bubble ${
		content.match(/<(?<tag>video|iframe)\b[^>]*>.*?<\/\k<tag>>/gs) ? 'w-full' : ''
	}">
		<div class="button-group absolute top-1 right-1 join">
			<button class="edit-button btn btn-xs btn-primary join-item" data-i18n="chat.messageView.buttons.edit">
				<img src="https://api.iconify.design/line-md/edit.svg" data-i18n="chat.messageView.buttons.editIcon" />
			</button>
			<button class="delete-button btn btn-xs btn-error join-item" data-i18n="chat.messageView.buttons.delete">
				<img src="https://api.iconify.design/line-md/remove.svg" data-i18n="chat.messageView.buttons.deleteIcon" />
			</button>
		</div>
		<div class="text-sm font-bold text-primary-content flex items-center gap-2">
			${name}
		</div>
		<div class="message-content markdown-body mt-1">
			${content}
		</div>
		<div class="attachments"></div>
	</div>
	<div class="chat-footer opacity-50">
		${timeStamp}
	</div>
</div>
