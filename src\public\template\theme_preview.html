<div class="theme-preview-card cursor-pointer" data-theme="${theme}">
	<div class="bg-base-100 text-base-content w-full font-sans rounded-lg shadow-sm overflow-hidden">
		<div class="grid grid-cols-5 grid-rows-3">
			<div class="bg-base-200 col-start-1 row-span-2 row-start-1"></div>
			<div class="bg-base-300 col-start-1 row-start-3"></div>
			<div class="bg-base-100 col-span-4 col-start-2 row-span-3 row-start-1 flex flex-col p-2">
				<div class="font-bold text-lg">
					${geti18n(`themeManage.themes.${name || theme}`) || name || theme}
				</div>
				<div class="flex flex-wrap mt-2">
					<div class="bg-primary flex aspect-square w-1/6 items-center justify-center rounded-md mr-1 mb-1">
						<div class="text-primary-content text-sm font-bold">A</div>
					</div>
					<div class="bg-secondary flex aspect-square w-1/6 items-center justify-center rounded-md mr-1 mb-1">
						<div class="text-secondary-content text-sm font-bold">A</div>
					</div>
					<div class="bg-accent flex aspect-square w-1/6 items-center justify-center rounded-md mr-1 mb-1">
						<div class="text-accent-content text-sm font-bold">A</div>
					</div>
					<div class="bg-neutral flex aspect-square w-1/6 items-center justify-center rounded-md mr-1 mb-1">
						<div class="text-neutral-content text-sm font-bold">A</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
