//! 系统通知工具
//! 对应原文件: src/scripts/notify.mjs

use notify_rust::Notification;
use anyhow::Result;

/// 发送系统通知
pub fn send_notification(title: &str, message: &str) -> Result<()> {
    Notification::new()
        .summary(title)
        .body(message)
        .show()?;
    Ok(())
}

/// 发送带图标的通知
pub fn send_notification_with_icon(title: &str, message: &str, icon_path: &str) -> Result<()> {
    Notification::new()
        .summary(title)
        .body(message)
        .icon(icon_path)
        .show()?;
    Ok(())
}
