name: Set Executable Permissions

permissions:
  contents: write

on:
  push:
    paths:
      - '**.sh'
      - '**.fish'
      - '**.zsh'
      - '**.ps1'
      - 'path/**'
    tags-ignore:
      - '*'
  workflow_dispatch:

jobs:
  set-permissions:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set execute permission for sh files
        run: |
          find . -maxdepth 1 \( -name "*.sh" -o -name "*.ps1" -o -name "*.fish" -o -name "*.zsh" \) -print0 | xargs -0 chmod +x
          find ./path -maxdepth 1 -type f -print0 | xargs -0 chmod +x
      - name: git add all
        run: git add -A
      - name: Push changes
        uses: actions-go/push@master
        with:
          author-email: <EMAIL>
          author-name: Taromati2
          commit-message: 'file update~'
          remote: origin
          token: ${{ secrets.GITHUB_TOKEN }}
