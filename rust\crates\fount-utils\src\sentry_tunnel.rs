//! Sentry隧道工具
//! 对应原文件: src/scripts/sentrytunnel.mjs

use sentry::{ClientOptions, init};
use anyhow::Result;

/// 初始化Sentry
pub fn init_sentry(dsn: &str) -> Result<()> {
    let _guard = init((dsn, ClientOptions {
        release: sentry::release_name!(),
        ..Default::default()
    }));
    Ok(())
}

/// 发送错误到Sentry
pub fn capture_error(error: &anyhow::Error) {
    sentry::capture_error(error);
}

/// 发送消息到Sentry
pub fn capture_message(message: &str, level: sentry::Level) {
    sentry::capture_message(message, level);
}
