//! 验证码系统
//! 对应原文件: src/scripts/verifycode.mjs

use captcha::Captcha;
use rand::Rng;

/// 生成验证码
pub fn generate_captcha() -> (String, Vec<u8>) {
    let mut captcha = Captcha::new();
    captcha
        .add_chars(5)
        .apply_filter(captcha::filters::Noise::new(0.4))
        .apply_filter(captcha::filters::Wave::new(2.0, 20.0))
        .view(220, 120);
    
    let code = captcha.chars_as_string();
    let image = captcha.as_png().unwrap_or_default();
    
    (code, image)
}

/// 生成数字验证码
pub fn generate_numeric_code(length: usize) -> String {
    let mut rng = rand::thread_rng();
    (0..length)
        .map(|_| rng.gen_range(0..10).to_string())
        .collect()
}
