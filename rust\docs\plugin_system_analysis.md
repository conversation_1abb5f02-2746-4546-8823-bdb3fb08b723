# Fount插件系统架构分析

## 插件系统概览

Fount的插件系统是其核心特性，采用模块化、可扩展的架构设计。系统支持多种类型的插件（称为"Parts"），每种插件都有特定的用途和接口规范。

## 插件类型体系

### 1. 核心插件类型
```javascript
const partsList = [
    'shells',           // 交互界面插件
    'chars',            // AI角色插件
    'personas',         // 用户人格插件
    'worlds',           // 世界环境插件
    'AIsources',        // AI源插件
    'AIsourceGenerators', // AI源生成器插件
    'ImportHandlers'    // 导入处理器插件
]
```

### 2. 插件层次结构
```
插件系统
├── 核心插件 (Core Parts)
│   ├── chars - AI角色的核心逻辑
│   ├── worlds - 环境和世界设定
│   └── personas - 用户人格定义
├── 接口插件 (Interface Parts)
│   ├── shells - 用户交互界面
│   └── ImportHandlers - 数据导入处理
└── 服务插件 (Service Parts)
    ├── AIsources - AI服务实例
    └── AIsourceGenerators - AI服务生成器
```

## 插件生命周期

### 1. 标准生命周期方法
所有插件都遵循统一的生命周期模式：

```javascript
class PluginAPI {
    // 安装时调用，失败则删除所有文件
    Init(initArgs) => Promise<void>
    
    // 每次启动时调用，失败则显示错误
    Load(loadArgs) => Promise<void>
    
    // 卸载时调用
    Unload(reason) => Promise<void>
    
    // 卸载时调用
    Uninstall(reason, from) => Promise<void>
}
```

### 2. 生命周期流程
```
安装阶段: 下载 → 解压 → Init → 配置保存
启动阶段: Load → 接口注册 → 运行状态
运行阶段: 接口调用 → 事件处理 → 状态维护
卸载阶段: Unload → 资源清理 → Uninstall (可选)
```

### 3. 错误处理策略
- **Init失败**: 删除插件目录，回滚安装
- **Load失败**: 显示错误消息，不影响其他插件
- **运行时错误**: 记录日志，尝试恢复或禁用插件

## 插件接口系统

### 1. 通用接口
所有插件都可以实现的标准接口：

```javascript
interfaces: {
    // 信息更新接口
    info?: {
        UpdateInfo: (locales) => Promise<info_t>
    },
    
    // 配置管理接口
    config?: {
        GetData: () => Promise<any>,
        SetData: (data) => Promise<void>
    }
}
```

### 2. 特定接口
不同类型插件的专用接口：

#### chars (角色插件)
```javascript
interfaces: {
    chat?: {
        GetGreeting: (args, index) => Promise<chatReply_t>,
        GetPrompt: (args, prompt_struct, detail_level) => Promise<single_part_prompt_t>,
        GetReply: (args) => Promise<chatReply_t>,
        MessageEdit?: (args) => Promise<chatReply_t>
    },
    discord?: {
        OnceClientReady: (client, config) => Promise<void>,
        GetBotConfigTemplate: () => Promise<any>
    },
    telegram?: {
        BotSetup: (bot, config) => Promise<void>,
        GetBotConfigTemplate: () => Promise<any>
    }
}
```

#### shells (界面插件)
```javascript
interfaces: {
    invokes?: {
        ArgumentsHandler: (user, args) => Promise<void>,
        IPCInvokeHandler: (user, data) => Promise<any>
    }
}
```

#### worlds (世界插件)
```javascript
interfaces: {
    chat?: {
        GetPrompt: (args, prompt_struct, detail_level) => Promise<single_part_prompt_t>,
        GetChatLogForCharname: (args, charname) => Promise<chatLogEntry_t[]>,
        AddChatLogEntry: (args, entry) => Promise<void>
    }
}
```

## 插件存储结构

### 1. 文件系统布局
```
{userDirectory}/{parttype}/{partname}/
├── main.mjs              # 插件主模块
├── fount.json           # 插件元数据
├── package.json         # 依赖声明 (可选)
├── src/                 # 源代码目录
│   ├── server/          # 服务器端代码
│   └── public/          # 前端资源
├── assets/              # 静态资源
└── docs/                # 文档文件
```

### 2. 元数据格式 (fount.json)
```json
{
    "type": "chars",
    "dirname": "example_char",
    "data_files": ["config.json", "assets/"],
    "version": "1.0.0",
    "dependencies": {
        "required_parts": ["worlds/basic_world"],
        "optional_parts": ["shells/advanced_chat"]
    }
}
```

### 3. 路径解析优先级
1. 用户特定插件: `{userDirectory}/{parttype}/{partname}/`
2. 公共插件: `src/public/{parttype}/{partname}/`
3. 系统默认: 内置插件和回退机制

## 动态加载机制

### 1. 模块加载器
```javascript
// 基础模块加载
async function baseMjsPartLoader(path) {
    const module = await import(path + '/main.mjs')
    return module.default
}

// 完整插件加载
async function loadPartBase(username, parttype, partname, initArgs, options) {
    // 路径解析
    const path = GetPartPath(username, parttype, partname)
    
    // 模块加载
    const part = await baseMjsPartLoader(path)
    
    // 生命周期调用
    await part.Load?.(initArgs)
    
    // 配置应用
    await part.interfaces?.config?.SetData?.(config)
    
    return part
}
```

### 2. 代理机制
```javascript
// 使用FullProxy实现动态访问
function FullProxy(base) {
    return new Proxy({}, {
        get(target, prop, receiver) {
            return Reflect.get(base(), prop, receiver)
        },
        // ... 其他Reflect操作
    })
}
```

### 3. 热重载支持
- 插件卸载时清理所有引用
- 重新加载时重新导入模块
- 状态迁移和配置保持

## 插件间通信

### 1. 事件系统
```javascript
// 全局事件总线
events.on('BeforeUserDeleted', async ({ username }) => {
    // 卸载用户的所有插件
})

events.on('BeforeUserRenamed', async ({ oldUsername, newUsername }) => {
    // 处理用户重命名
})
```

### 2. 接口调用
```javascript
// 跨插件接口调用
const char = await loadPart(username, 'chars', 'example_char')
const reply = await char.interfaces.chat.GetReply(args)
```

### 3. 共享状态
- 通过配置系统共享数据
- 使用临时数据存储会话状态
- 事件驱动的状态同步

## 插件安全模型

### 1. 沙箱机制
- JavaScript模块隔离
- 文件系统访问限制
- 网络请求控制

### 2. 权限管理
- 用户级插件隔离
- 接口权限控制
- 资源访问限制

### 3. 验证机制
- 插件签名验证
- 依赖关系检查
- 版本兼容性验证

## 插件开发模式

### 1. 最小插件示例
```javascript
export default {
    info: {
        'en-US': {
            name: 'Example Plugin',
            description: 'A simple example plugin',
            version: '1.0.0',
            author: 'Developer'
        }
    },
    
    Init: async (args) => {
        // 初始化逻辑
    },
    
    Load: async (args) => {
        // 加载逻辑
    },
    
    Unload: async (reason) => {
        // 卸载逻辑
    },
    
    interfaces: {
        // 插件接口实现
    }
}
```

### 2. 复杂插件结构
```javascript
// 多文件插件组织
import { CharacterCore } from './src/character.mjs'
import { ChatInterface } from './src/chat.mjs'
import { ConfigManager } from './src/config.mjs'

export default {
    info: { /* ... */ },
    
    Init: async (args) => {
        this.core = new CharacterCore(args)
        this.config = new ConfigManager()
        await this.core.initialize()
    },
    
    Load: async (args) => {
        this.chat = new ChatInterface(this.core, args)
        await this.chat.setup()
    },
    
    interfaces: {
        chat: {
            GetReply: (args) => this.chat.generateReply(args),
            GetPrompt: (args) => this.chat.buildPrompt(args)
        },
        config: {
            GetData: () => this.config.getData(),
            SetData: (data) => this.config.setData(data)
        }
    }
}
```

## Rust移植策略

### 1. 插件架构设计
```rust
// 插件trait定义
#[async_trait]
trait Plugin: Send + Sync {
    async fn init(&mut self, args: InitArgs) -> Result<()>;
    async fn load(&mut self, args: LoadArgs) -> Result<()>;
    async fn unload(&mut self, reason: &str) -> Result<()>;
    
    fn interfaces(&self) -> &dyn PluginInterfaces;
}

// 插件管理器
struct PluginManager {
    plugins: HashMap<String, Box<dyn Plugin>>,
    loader: PluginLoader,
}
```

### 2. 动态加载方案
- **选项1**: 使用`libloading`进行动态库加载
- **选项2**: 使用WASM运行时进行沙箱化
- **选项3**: 使用Rust插件系统如`abi_stable`

### 3. 接口系统
```rust
// 类型安全的接口定义
trait ChatInterface {
    async fn get_reply(&self, args: &ChatArgs) -> Result<ChatReply>;
    async fn get_prompt(&self, args: &PromptArgs) -> Result<Prompt>;
}

// 接口注册表
struct InterfaceRegistry {
    chat_interfaces: HashMap<String, Box<dyn ChatInterface>>,
    config_interfaces: HashMap<String, Box<dyn ConfigInterface>>,
}
```

### 4. 配置和状态管理
```rust
// 配置管理
#[derive(Serialize, Deserialize)]
struct PluginConfig {
    enabled: bool,
    settings: serde_json::Value,
}

// 状态管理
struct PluginState {
    loaded: bool,
    last_error: Option<String>,
    load_time: Option<SystemTime>,
}
```

## 关键挑战

1. **动态加载**: JavaScript动态import vs Rust静态编译
2. **类型安全**: 动态接口 vs 静态trait系统
3. **内存管理**: JavaScript GC vs Rust所有权
4. **沙箱安全**: JavaScript隔离 vs Rust安全模型
5. **热重载**: 运行时模块替换的复杂性
