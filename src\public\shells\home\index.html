<!DOCTYPE html>
<html data-theme="dark">

<head>
	<meta charset="UTF-8">
	<meta name="darkreader-lock">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title data-i18n="home.title"></title>
	<link href="https://cdn.jsdelivr.net/npm/daisyui/daisyui.css" rel="stylesheet" type="text/css" />
	<link href="/base.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser"></script>
	<script type="module" src="https://cdn.jsdelivr.net/gh/steve02081504/fount/.statistic/scripts/home.mjs"></script>
	<script type="module" src="/base.mjs"></script>
	<link rel="stylesheet" href="./index.css" type="text/css" />
</head>

<body>
	<div class="drawer drawer-end lg:drawer-open">
		<input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
		<div class="drawer-content flex flex-col">
			<!-- Page Content -->
			<div class="container mx-auto p-4 w-full lg:w-3/4">
				<div class="flex justify-between items-center mb-4">
					<h1 class="text-2xl" id="page-title" data-i18n="home.chars.title"></h1>
					<!-- Desktop Tabs -->
					<div role="tablist" class="tabs tabs-lifted tabs-vertical flex">
						<a role="tab" class="tab" id="chars-tab-desktop" data-target="char-container" data-i18n="home.chars.tab"></a>
						<a role="tab" class="tab" id="worlds-tab-desktop" data-target="world-container" data-i18n="home.worlds.tab"></a>
						<a role="tab" class="tab" id="personas-tab-desktop" data-target="persona-container" data-i18n="home.personas.tab"></a>
					</div>
					<div class="dropdown dropdown-end">
						<div tabindex="0" role="button" class="btn btn-primary m-1">
							<img src="https://api.iconify.design/line-md/list-3-filled.svg" data-i18n="home.functionMenu.icon" />
						</div>
						<ul id="function-buttons-container" tabindex="0" class="dropdown-content z-[60] p-2 shadow-2xl bg-base-300 rounded-box w-52 menu">
						</ul>
					</div>
				</div>
				<p id="subtitle" class="mb-4" data-i18n="home.chars.subtitle"></p>
				<div class="flex justify-between items-center mb-4 flex-wrap">
					<div class="flex gap-2 w-full">
						<input type="text" id="filter-input" class="input w-full" data-i18n="home.filterInput" />
					</div>
				</div>

				<!-- Content Containers -->
				<div class="grid gap-4" id="char-container" style="grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));">
				</div>
				<div class="grid gap-4 hidden" id="world-container" style="grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));">
				</div>
				<div class="grid gap-4 hidden" id="persona-container" style="grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));">
				</div>
			</div>
		</div>

		<!-- Sidebar (drawer-side) -->
		<div class="drawer-side">
			<label for="drawer-toggle" aria-label="close sidebar" class="drawer-overlay"></label>
			<div class="p-4 w-80 min-h-full bg-base-300/95 sm:bg-base-300/25 text-base-content border-l-2 border-base-content flex flex-col">
				<h2 class="text-xl mt-4 lg:mt-2 mb-2" data-i18n="home.sidebarTitle"></h2>
				<article id="item-description" class="markdown-body" data-i18n="home.itemDescription"></article>
			</div>
		</div>
	</div>

	<script type="module" src="./index.mjs"></script>
</body>

</html>
