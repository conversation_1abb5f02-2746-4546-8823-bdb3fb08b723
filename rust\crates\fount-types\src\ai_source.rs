//! AI源类型定义
//! 对应原文件: src/decl/AIsource.ts

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use crate::{ApiResponse, Status};

/// AI源配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiSource {
    pub id: Uuid,
    pub name: String,
    pub provider: String,
    pub model: String,
    pub api_key: Option<String>,
    pub endpoint: String,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub frequency_penalty: Option<f32>,
    pub presence_penalty: Option<f32>,
    pub status: Status,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// AI源创建请求
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CreateAiSourceRequest {
    pub name: String,
    pub provider: String,
    pub model: String,
    pub api_key: Option<String>,
    pub endpoint: String,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub frequency_penalty: Option<f32>,
    pub presence_penalty: Option<f32>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// AI源更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAiSourceRequest {
    pub name: Option<String>,
    pub provider: Option<String>,
    pub model: Option<String>,
    pub api_key: Option<String>,
    pub endpoint: Option<String>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub frequency_penalty: Option<f32>,
    pub presence_penalty: Option<f32>,
    pub status: Option<Status>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// AI消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiMessage {
    pub role: String,
    pub content: String,
    pub timestamp: DateTime<Utc>,
}

/// AI对话请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiChatRequest {
    pub source_id: Uuid,
    pub messages: Vec<AiMessage>,
    pub character_id: Option<Uuid>,
    pub user_id: String,
    pub stream: Option<bool>,
}

/// AI对话响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiChatResponse {
    pub message: AiMessage,
    pub usage: Option<AiUsage>,
    pub finish_reason: Option<String>,
}

/// AI使用统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

/// AI源响应类型别名
pub type AiSourceResponse = ApiResponse<AiSource>;
pub type AiSourceListResponse = ApiResponse<Vec<AiSource>>;
pub type AiChatApiResponse = ApiResponse<AiChatResponse>;
