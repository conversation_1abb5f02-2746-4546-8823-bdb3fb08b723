# JavaScript/TypeScript到Rust精确文件映射表

## 映射原则

1. **结构一致性**: 保持与原项目完全相同的目录层级和逻辑组织
2. **功能等价性**: 每个原始文件都有对应的Rust文件，功能完全等价
3. **命名规范**: 遵循Rust命名约定，但保持语义一致性
4. **模块化**: 利用Rust的模块系统重现JavaScript的模块结构

## 核心映射表

### 1. 类型定义文件映射 (src/decl/ → crates/fount-types/src/)

| 原始文件 | Rust文件 | 说明 |
|---------|---------|------|
| `src/decl/basedefs.ts` | `crates/fount-types/src/base_defs.rs` | 基础类型定义 |
| `src/decl/charAPI.ts` | `crates/fount-types/src/char_api.rs` | 角色API类型 |
| `src/decl/AIsource.ts` | `crates/fount-types/src/ai_source.rs` | AI源类型 |
| `src/decl/AIsourceGeneretor.ts` | `crates/fount-types/src/ai_source_generator.rs` | AI源生成器类型 |
| `src/decl/UserAPI.ts` | `crates/fount-types/src/user_api.rs` | 用户API类型 |
| `src/decl/WorldAPI.ts` | `crates/fount-types/src/world_api.rs` | 世界API类型 |
| `src/decl/importHandlerAPI.ts` | `crates/fount-types/src/import_handler_api.rs` | 导入处理器API类型 |
| `src/decl/pluginAPI.ts` | `crates/fount-types/src/plugin_api.rs` | 插件API类型 |
| `src/decl/prompt_struct.ts` | `crates/fount-types/src/prompt_struct.rs` | 提示结构类型 |
| `src/decl/shellAPI.ts` | `crates/fount-types/src/shell_api.rs` | Shell API类型 |

### 2. 工具库文件映射 (src/scripts/ → crates/fount-utils/src/)

| 原始文件 | Rust文件 | 说明 |
|---------|---------|------|
| `src/scripts/console.mjs` | `crates/fount-utils/src/console.rs` | 增强控制台 |
| `src/scripts/json_loader.mjs` | `crates/fount-utils/src/json_loader.rs` | JSON文件操作 |
| `src/scripts/i18n.mjs` | `crates/fount-utils/src/i18n.rs` | 国际化工具 |
| `src/scripts/exec.mjs` | `crates/fount-utils/src/exec.rs` | 进程执行 |
| `src/scripts/proxy.mjs` | `crates/fount-utils/src/proxy.rs` | 代理工具 |
| `src/scripts/ratelimit.mjs` | `crates/fount-utils/src/ratelimit.rs` | 速率限制 |
| `src/scripts/ms.mjs` | `crates/fount-utils/src/ms.rs` | 时间解析 |
| `src/scripts/escape.mjs` | `crates/fount-utils/src/escape.rs` | 字符串转义 |
| `src/scripts/await_timeout.mjs` | `crates/fount-utils/src/await_timeout.rs` | 超时控制 |
| `src/scripts/notify.mjs` | `crates/fount-utils/src/notify.rs` | 系统通知 |
| `src/scripts/tray.mjs` | `crates/fount-utils/src/tray.rs` | 系统托盘 |
| `src/scripts/discordrpc.mjs` | `crates/fount-utils/src/discord_rpc.rs` | Discord RPC |
| `src/scripts/env.mjs` | `crates/fount-utils/src/env.rs` | 环境检测 |
| `src/scripts/locale.mjs` | `crates/fount-utils/src/locale.rs` | 本地化工具 |
| `src/scripts/verifycode.mjs` | `crates/fount-utils/src/verify_code.rs` | 验证码系统 |
| `src/scripts/sentrytunnel.mjs` | `crates/fount-utils/src/sentry_tunnel.rs` | Sentry隧道 |

### 3. 服务器核心文件映射 (src/server/ → crates/fount-server/src/)

| 原始文件 | Rust文件 | 说明 |
|---------|---------|------|
| `src/server/index.mjs` | `crates/fount-server/src/main.rs` | 服务器入口 |
| `src/server/base.mjs` | `crates/fount-server/src/base.rs` | 基础模块 |
| `src/server/server.mjs` | `crates/fount-server/src/server.rs` | 主Web服务器 |
| `src/server/auth.mjs` | `crates/fount-server/src/auth.rs` | 认证系统 |
| `src/server/endpoints.mjs` | `crates/fount-server/src/endpoints.rs` | API端点 |
| `src/server/events.mjs` | `crates/fount-server/src/events.rs` | 事件系统 |
| `src/server/ipc_server.mjs` | `crates/fount-server/src/ipc_server.rs` | IPC服务器 |
| `src/server/jobs.mjs` | `crates/fount-server/src/jobs.rs` | 任务管理 |
| `src/server/on_shutdown.mjs` | `crates/fount-server/src/on_shutdown.rs` | 关闭处理 |
| `src/server/parts_loader.mjs` | `crates/fount-server/src/parts_loader.rs` | 插件加载器 |
| `src/server/setting_loader.mjs` | `crates/fount-server/src/setting_loader.rs` | 设置加载器 |
| `src/server/timers.mjs` | `crates/fount-server/src/timers.rs` | 定时器 |

### 4. 管理器文件映射 (src/server/managers/ → crates/fount-server/src/managers/)

| 原始文件 | Rust文件 | 说明 |
|---------|---------|------|
| `src/server/managers/index.mjs` | `crates/fount-server/src/managers/mod.rs` | 管理器入口 |
| `src/server/managers/char_manager.mjs` | `crates/fount-server/src/managers/char_manager.rs` | 角色管理器 |
| `src/server/managers/shell_manager.mjs` | `crates/fount-server/src/managers/shell_manager.rs` | Shell管理器 |
| `src/server/managers/personas_manager.mjs` | `crates/fount-server/src/managers/personas_manager.rs` | 人格管理器 |
| `src/server/managers/world_manager.mjs` | `crates/fount-server/src/managers/world_manager.rs` | 世界管理器 |
| `src/server/managers/AIsources_manager.mjs` | `crates/fount-server/src/managers/ai_sources_manager.rs` | AI源管理器 |

### 5. 前端资源映射 (src/public/ → static/)

| 原始路径 | Rust路径 | 说明 |
|---------|---------|------|
| `src/public/` | `static/` | 静态文件根目录 |
| `src/public/index.html` | `static/index.html` | 主页面 |
| `src/public/base.css` | `static/base.css` | 基础样式 |
| `src/public/base.mjs` | `static/base.mjs` | 基础JavaScript |
| `src/public/service_worker.mjs` | `static/service_worker.mjs` | Service Worker |
| `src/public/404.html` | `static/404.html` | 404页面 |
| `src/public/favicon.ico` | `static/favicon.ico` | 网站图标 |
| `src/public/favicon.png` | `static/favicon.png` | PNG图标 |
| `src/public/scripts/` | `static/scripts/` | 前端脚本库 |
| `src/public/shells/` | `static/shells/` | Shell插件 |
| `src/public/template/` | `static/template/` | 模板文件 |
| `src/public/login/` | `static/login/` | 登录页面 |
| `src/public/protocolhandler/` | `static/protocolhandler/` | 协议处理器 |

### 6. 插件系统映射 (src/public/插件类型/ → static/插件类型/)

| 原始路径 | Rust路径 | 说明 |
|---------|---------|------|
| `src/public/AIsourceGenerators/` | `static/AIsourceGenerators/` | AI源生成器插件 |
| `src/public/ImportHandlers/` | `static/ImportHandlers/` | 导入处理器插件 |
| `src/public/shells/` | `static/shells/` | Shell插件 |

### 7. 配置文件映射

| 原始文件 | Rust文件 | 说明 |
|---------|---------|------|
| `deno.json` | `Cargo.toml` | 项目配置 |
| `default/config.json` | `default/config.json` | 默认配置 (保持不变) |
| `run.bat` | `run.bat` | Windows启动脚本 (适配) |
| `run.sh` | `run.sh` | Unix启动脚本 (适配) |
| `path/fount.ps1` | `scripts/fount.ps1` | PowerShell脚本 (适配) |
| `path/fount.sh` | `scripts/fount.sh` | Shell脚本 (适配) |

### 8. 国际化文件映射

| 原始路径 | Rust路径 | 说明 |
|---------|---------|------|
| `src/locales/` | `assets/locales/` | 国际化文件 |
| `src/locales/zh-CN.json` | `assets/locales/zh-CN.json` | 中文语言包 |
| `src/locales/en-UK.json` | `assets/locales/en-UK.json` | 英文语言包 |

### 9. 资源文件映射

| 原始路径 | Rust路径 | 说明 |
|---------|---------|------|
| `imgs/` | `assets/icons/` | 图标资源 |
| `docs/` | `docs/` | 文档文件 (保持不变) |
| `default/` | `default/` | 默认配置 (保持不变) |

## 模块结构映射

### 1. Cargo Workspace结构
```toml
# Cargo.toml (workspace root)
[workspace]
members = [
    "crates/fount-types",
    "crates/fount-utils", 
    "crates/fount-server",
    "crates/fount-desktop"
]
```

### 2. 类型定义Crate (fount-types)
```rust
// crates/fount-types/src/lib.rs
pub mod base_defs;
pub mod char_api;
pub mod ai_source;
pub mod world_api;
pub mod plugin_api;
// ... 其他类型模块

// 重新导出主要类型
pub use base_defs::*;
pub use char_api::*;
pub use ai_source::*;
```

### 3. 工具库Crate (fount-utils)
```rust
// crates/fount-utils/src/lib.rs
pub mod console;
pub mod json_loader;
pub mod i18n;
pub mod exec;
// ... 其他工具模块

// 重新导出主要函数
pub use console::*;
pub use json_loader::*;
pub use i18n::*;
```

### 4. 服务器Crate (fount-server)
```rust
// crates/fount-server/src/lib.rs
pub mod auth;
pub mod server;
pub mod endpoints;
pub mod events;
pub mod managers;
// ... 其他服务器模块

// main.rs作为二进制入口
// crates/fount-server/src/main.rs
use fount_server::*;

#[tokio::main]
async fn main() -> Result<()> {
    // 服务器启动逻辑
}
```

## 依赖关系映射

### 1. JavaScript依赖 → Rust Crate
| JavaScript包 | Rust Crate | 用途 |
|-------------|------------|------|
| `express` | `axum` | Web框架 |
| `jose` | `jsonwebtoken` | JWT处理 |
| `bcrypt` | `bcrypt` | 密码哈希 |
| `cookie-parser` | `cookie` | Cookie处理 |
| `cors` | `tower-http` | CORS中间件 |
| `express-fileupload` | `multer` | 文件上传 |
| `@sentry/deno` | `sentry` | 错误监控 |
| `systray` | `tray-icon` | 系统托盘 |
| `node-notifier` | `notify-rust` | 系统通知 |

### 2. 内部模块依赖
```rust
// 依赖层次 (从底层到上层)
fount-types (基础类型)
    ↑
fount-utils (工具库)
    ↑  
fount-server (服务器核心)
    ↑
fount-desktop (桌面应用)
```

## 特殊映射考虑

### 1. 动态导入处理
- JavaScript: `await import('./module.mjs')`
- Rust: 使用trait对象或枚举进行静态分发

### 2. 异步函数映射
- JavaScript: `async function name() {}`
- Rust: `async fn name() -> Result<T> {}`

### 3. 错误处理映射
- JavaScript: `try-catch`
- Rust: `Result<T, E>` + `?` 操作符

### 4. 类型转换映射
- JavaScript: 动态类型
- Rust: 静态类型 + `serde` 序列化

### 5. 模块导出映射
- JavaScript: `export default` / `export { }`
- Rust: `pub use` / `pub mod`

## 验证清单

### 1. 结构完整性检查
- [ ] 所有原始文件都有对应的Rust文件
- [ ] 目录结构层级保持一致
- [ ] 模块依赖关系正确映射

### 2. 功能等价性检查
- [ ] 每个函数都有对应的Rust实现
- [ ] 接口签名语义等价
- [ ] 错误处理逻辑一致

### 3. 配置兼容性检查
- [ ] 配置文件格式兼容
- [ ] 环境变量处理一致
- [ ] 启动参数兼容

### 4. 资源文件检查
- [ ] 静态文件正确复制
- [ ] 路径引用正确更新
- [ ] 权限设置正确

这个映射表确保了Rust版本与原JavaScript/TypeScript版本在结构和功能上的完全一致性，为后续的具体实现提供了清晰的指导。
