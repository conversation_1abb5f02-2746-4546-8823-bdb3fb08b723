<!DOCTYPE html>
<html data-theme="dark">

<head>
	<meta charset="UTF-8">
	<meta name="darkreader-lock">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title data-i18n="part_config.title"></title>
	<link href="https://cdn.jsdelivr.net/npm/daisyui/daisyui.css" rel="stylesheet" type="text/css" />
	<link href="/base.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser"></script>
	<script type="module" src="/base.mjs"></script>
	<link rel="stylesheet" href="./index.css" type="text/css" />
</head>

<body>
	<div class="container mx-auto p-4">
		<h1 class="text-3xl font-bold mb-4" data-i18n="part_config.pageTitle"></h1>

		<div class="flex flex-col md:flex-row gap-4 mb-4">
			<!-- 部件类型选择 -->
			<div class="w-full md:w-1/2">
				<label for="partTypeSelect" class="label">
					<span class="label-text" data-i18n="part_config.labels.partType"></span>
				</label>
				<select id="partTypeSelect" class="select select-bordered w-full">
					<option disabled selected data-i18n="part_config.placeholders.partTypeSelect"></option>
				</select>
			</div>

			<!-- 部件列表 -->
			<div class="w-full md:w-1/2">
				<label for="partSelect" class="label">
					<span class="label-text" data-i18n="part_config.labels.part"></span>
				</label>
				<select id="partSelect" class="select select-bordered w-full" disabled>
					<option disabled selected data-i18n="part_config.placeholders.partSelect"></option>
				</select>
			</div>
		</div>

		<!-- 编辑区域 -->
		<div id="editorArea" class="relative">
			<div id="disabledIndicator" class="absolute inset-0 z-10 bg-base-200 opacity-80 flex justify-center items-center rounded-lg hidden">
				<span class="text-lg text-error" data-i18n="part_config.editor.disabledIndicator"></span>
			</div>
			<h2 class="text-xl font-semibold mb-2" data-i18n="part_config.editor.title"></h2>
			<!-- JSON 编辑器容器 -->
			<div id="jsonEditor" class="jsoneditor-container" style="height: 400px; overflow: auto"></div>

			<div class="mt-4 flex justify-end">
				<button id="saveButton" class="btn btn-primary">
					<img id="saveStatusIcon" src="" class="w-6 h-6 mr-2 hidden" />
					<span id="saveStatusText" data-i18n="part_config.editor.buttons.save"></span>
				</button>
			</div>
		</div>
		<!-- 错误信息显示区域 -->
		<div id="errorMessage" class="alert alert-error hidden fixed bottom-4 m-0 p-0">
			<div>
				<img src="https://api.iconify.design/line-md/alert.svg" class="flex-shrink-0 w-6 h-6" data-i18n="part_config.errorMessage.icon" />
				<span id="errorMessageText"></span>
			</div>
		</div>
	</div>

	<script type="module" src="./index.mjs"></script>
</body>

</html>
