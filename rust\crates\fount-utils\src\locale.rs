//! 本地化工具
//! 对应原文件: src/scripts/locale.mjs

/// 获取系统默认语言
pub fn get_system_locale() -> String {
    std::env::var("LANG")
        .or_else(|_| std::env::var("LC_ALL"))
        .unwrap_or_else(|_| "en-US".to_string())
}

/// 解析语言标识符
pub fn parse_locale(locale: &str) -> (String, Option<String>) {
    if let Some(pos) = locale.find('-') {
        let language = locale[..pos].to_string();
        let region = Some(locale[pos + 1..].to_string());
        (language, region)
    } else {
        (locale.to_string(), None)
    }
}
