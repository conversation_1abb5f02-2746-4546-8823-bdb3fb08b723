//! 提示结构类型定义
//! 对应原文件: src/decl/prompt_struct.ts

use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 提示模板
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PromptTemplate {
    pub id: Uuid,
    pub name: String,
    pub template: String,
    pub variables: Vec<String>,
}

/// 提示参数
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PromptParams {
    pub template_id: Uuid,
    pub variables: serde_json::Value,
}
