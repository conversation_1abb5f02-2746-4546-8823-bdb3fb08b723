# Fount服务器核心模块分析

## 服务器模块概览

### 1. index.mjs - 服务器入口点
**功能**: 应用程序主入口，处理命令行参数和服务器初始化
**主要职责**:
- Sentry错误监控初始化
- 命令行参数解析 (runshell, shutdown)
- IPC命令处理
- 服务器实例管理
- 任务重启和定时器启动

**关键依赖**:
- `@sentry/deno` - 错误监控
- `./base.mjs` - 基础模块
- `./server.mjs` - 主服务器
- `./ipc_server.mjs` - IPC通信
- `./jobs.mjs` - 任务管理
- `./timers.mjs` - 定时器

**Rust映射**: `main.rs` + `lib.rs`

### 2. server.mjs - 主Web服务器
**功能**: Express.js Web服务器核心，HTTP/HTTPS服务器管理
**主要职责**:
- Express应用配置和中间件设置
- 路由器管理 (mainRouter, PartsRouter, FinalRouter)
- 静态文件服务
- HTTPS/HTTP服务器启动
- 配置文件管理
- 错误处理中间件
- 系统托盘和Discord RPC集成

**关键依赖**:
- `express@^5.0.1` - Web框架
- `cookie-parser@^1.4.0` - Cookie解析
- `express-fileupload@^1.5.0` - 文件上传
- `@sentry/deno` - 错误监控

**Rust映射**: `server.rs` (使用Axum框架)

### 3. auth.mjs - 认证和授权系统
**功能**: JWT认证、用户管理、会话管理
**主要职责**:
- JWT密钥对生成和管理 (ES256算法)
- 用户注册、登录、登出
- Access Token和Refresh Token管理
- 密码哈希和验证 (bcrypt)
- 账户锁定机制
- 设备会话管理
- 中间件认证

**关键依赖**:
- `jose` - JWT处理
- `bcrypt` - 密码哈希
- `../scripts/ms.mjs` - 时间处理

**Rust映射**: `auth.rs` (使用jsonwebtoken, bcrypt crates)

### 4. endpoints.mjs - API端点定义
**功能**: RESTful API端点注册和处理
**主要职责**:
- 用户认证端点 (/api/auth/*)
- 部件管理端点 (/api/parts/*)
- 文件上传端点
- 国际化端点
- 验证码端点
- IPC命令端点
- CORS配置

**关键依赖**:
- `express@^5.0.1` - 路由
- `cors` - 跨域处理
- `./auth.mjs` - 认证
- `./managers/index.mjs` - 部件管理

**Rust映射**: `endpoints.rs` (使用Axum路由)

### 5. base.mjs - 基础工具模块
**功能**: 基础常量和工具函数
**主要职责**:
- 项目根目录路径定义 (__dirname)
- 启动时间记录
- 基础配置

**Rust映射**: `base.rs`

### 6. events.mjs - 事件系统
**功能**: 简单的事件发布订阅系统
**主要职责**:
- 事件监听器注册 (on)
- 事件触发 (emit)
- 事件监听器移除 (off)
- 异步事件处理

**Rust映射**: `events.rs` (使用tokio::sync::broadcast或自定义事件系统)

### 7. ipc_server.mjs - 进程间通信服务器
**功能**: IPC通信管理，支持多实例协调
**主要职责**:
- IPC服务器启动和管理
- 命令处理 (runshell, shutdown)
- 实例检测和单例模式
- 进程间消息传递

**Rust映射**: `ipc_server.rs` (使用tokio IPC或命名管道)

### 8. jobs.mjs - 任务管理系统
**功能**: 后台任务和作业管理
**主要职责**:
- 任务启动和停止 (StartJob, EndJob)
- 任务重启机制
- 用户任务管理
- 任务持久化

**关键依赖**:
- `./auth.mjs` - 用户管理
- `./managers/index.mjs` - 部件管理
- `./events.mjs` - 事件系统

**Rust映射**: `jobs.rs` (使用tokio任务管理)

### 9. on_shutdown.mjs - 关闭处理
**功能**: 优雅关闭和清理资源
**主要职责**:
- 关闭回调注册
- 信号处理 (SIGINT, SIGTERM)
- 资源清理
- 优雅关闭流程

**Rust映射**: `on_shutdown.rs` (使用tokio::signal)

### 10. parts_loader.mjs - 插件加载器
**功能**: 动态插件系统核心
**主要职责**:
- 插件路径解析
- 动态模块加载和卸载
- 插件生命周期管理 (Load, Unload, Init, Uninstall)
- 插件依赖管理
- 插件列表获取

**关键依赖**:
- `./auth.mjs` - 用户管理
- `../scripts/proxy.mjs` - 代理工具
- `../scripts/exec.mjs` - 执行工具

**Rust映射**: `parts_loader.rs` (使用libloading或wasm-runtime)

### 11. setting_loader.mjs - 配置加载器
**功能**: 用户配置和数据管理
**主要职责**:
- 用户数据加载和保存
- Shell数据管理
- 临时数据管理
- JSON文件操作
- 配置持久化

**关键依赖**:
- `./auth.mjs` - 用户管理
- `../scripts/json_loader.mjs` - JSON处理

**Rust映射**: `setting_loader.rs`

### 12. timers.mjs - 定时器管理
**功能**: 定时任务和心跳管理
**主要职责**:
- 心跳定时器
- 定期任务调度
- 定时器生命周期管理

**Rust映射**: `timers.rs` (使用tokio::time)

## 服务器启动流程

1. **初始化阶段** (index.mjs)
   - Sentry初始化
   - 基础模块设置
   - 命令行参数解析

2. **服务器启动** (server.mjs)
   - 配置加载
   - 认证系统初始化
   - IPC服务器启动
   - Express应用配置
   - 中间件注册
   - 路由注册
   - HTTP/HTTPS服务器启动

3. **后台服务启动**
   - 任务重启
   - 定时器启动
   - 系统托盘创建
   - Discord RPC启动

## 架构特点

### 模块化设计
- 每个模块职责单一
- 松耦合设计
- 依赖注入模式

### 插件系统
- 动态加载机制
- 生命周期管理
- 多类型插件支持 (shells, chars, personas, worlds, AIsources)

### 认证安全
- JWT双令牌机制
- 设备会话管理
- 账户锁定保护
- 本地IP免验证

### 事件驱动
- 异步事件处理
- 发布订阅模式
- 生命周期事件

## Rust移植策略

### Web框架选择
- **Axum**: 替代Express.js
- **Tower**: 中间件系统
- **Serde**: JSON序列化
- **Tokio**: 异步运行时

### 认证系统
- **jsonwebtoken**: JWT处理
- **bcrypt**: 密码哈希
- **cookie**: Cookie管理

### 插件系统
- **libloading**: 动态库加载
- **wasmtime**: WASM运行时 (可选)
- **serde_json**: 配置序列化

### 数据存储
- **serde_json**: JSON文件操作
- **tokio::fs**: 异步文件操作
- **dirs**: 目录管理

### 系统集成
- **tokio::signal**: 信号处理
- **tokio::net**: IPC通信
- **tray-icon**: 系统托盘

## 关键挑战

1. **动态插件加载**: JavaScript的动态import vs Rust的静态编译
2. **异步模型转换**: Node.js事件循环 vs Tokio运行时
3. **类型安全**: JavaScript动态类型 vs Rust静态类型
4. **错误处理**: JavaScript异常 vs Rust Result类型
5. **内存管理**: JavaScript GC vs Rust所有权系统
