#errorMessage {
	transition: opacity 0.5s ease-in-out;
	opacity: 1;
}

#errorMessage.hidden {
	opacity: 0;
	pointer-events: none;
}

#errorMessage.fade-in {
	animation: fade-in 0.5s ease-in-out forwards;
}

#errorMessage.fade-out {
	animation: fade-out 2s ease-in-out forwards;
}

@keyframes fade-in {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

@keyframes fade-out {
	from {
		opacity: 1;
	}

	to {
		opacity: 0;
	}
}
