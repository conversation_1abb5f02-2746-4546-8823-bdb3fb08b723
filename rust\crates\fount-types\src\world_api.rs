//! 世界API类型定义
//! 对应原文件: src/decl/WorldAPI.ts

use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use crate::Status;

/// 世界信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct World {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub creator_id: String,
    pub status: Status,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 世界创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateWorldRequest {
    pub name: String,
    pub description: String,
}
