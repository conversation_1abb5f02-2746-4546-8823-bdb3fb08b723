<!DOCTYPE html>
<html data-theme="dark">

<head>
	<meta charset="UTF-8">
	<meta name="darkreader-lock">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>fount!</title>
	<link href="https://cdn.jsdelivr.net/npm/daisyui/themes.css" rel="stylesheet" type="text/css" />
	<link href="https://cdn.jsdelivr.net/npm/daisyui/daisyui.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser"></script>
	<script>
		const urlParams = new URLSearchParams(window.location.search)
		const theme = urlParams.get('theme') ?? localStorage.getItem('fountTheme') ?? 'dark'
		document.documentElement.setAttribute('data-theme', theme)
		localStorage.setItem('fountTheme', theme)
	</script>
	<script type="module">
		import { getFountHostUrl } from '../scripts/fountHostGetter.mjs'
		const fountProtocolUrl = urlParams.get('url')

		async function main() {
			const hostUrl = await getFountHostUrl()

			if (hostUrl) {
				const redirectUrl = fountProtocolUrl
					? new URL('/protocolhandler', hostUrl)
					: new URL('/shells/home', hostUrl)
				if (fountProtocolUrl) {
					redirectUrl.searchParams.set('url', fountProtocolUrl)
					redirectUrl.searchParams.set('from', 'jumppage')
				}
				window.location.href = redirectUrl.href
			}
			else {
				alert('awww :(\n\nfount not found, maybe you need to install one first?')
				window.location.href = 'https://github.com/steve02081504/fount'
			}
		}

		main()
	</script>
</head>

<body class="flex justify-center items-center h-screen">
	<span class="loading loading-ring loading-lg"></span>
</body>

</html>
