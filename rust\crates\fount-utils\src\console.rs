//! 增强控制台工具
//! 对应原文件: src/scripts/console.mjs

use tracing::{info, warn, error, debug};
use crossterm::{
    style::{Color, SetForegroundColor, ResetColor},
    execute,
};
use std::io::{self, Write};

/// 控制台颜色
pub enum ConsoleColor {
    Red,
    Green,
    Yellow,
    Blue,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
}

impl From<ConsoleColor> for Color {
    fn from(color: ConsoleColor) -> Self {
        match color {
            ConsoleColor::Red => Color::Red,
            ConsoleColor::Green => Color::Green,
            ConsoleColor::Yellow => Color::Yellow,
            ConsoleColor::Blue => Color::Blue,
            ConsoleColor::Magenta => Color::Magenta,
            ConsoleColor::Cyan => Color::<PERSON><PERSON>,
            ConsoleColor::White => Color::White,
        }
    }
}

/// 增强的控制台输出
pub struct EnhancedConsole;

impl EnhancedConsole {
    /// 彩色输出
    pub fn print_colored(message: &str, color: ConsoleColor) -> io::Result<()> {
        let mut stdout = io::stdout();
        execute!(stdout, SetForegroundColor(color.into()))?;
        write!(stdout, "{}", message)?;
        execute!(stdout, ResetColor)?;
        stdout.flush()?;
        Ok(())
    }
    
    /// 成功消息
    pub fn success(message: &str) {
        info!("✓ {}", message);
        let _ = Self::print_colored(&format!("✓ {}\n", message), ConsoleColor::Green);
    }
    
    /// 错误消息
    pub fn error(message: &str) {
        error!("✗ {}", message);
        let _ = Self::print_colored(&format!("✗ {}\n", message), ConsoleColor::Red);
    }
    
    /// 警告消息
    pub fn warning(message: &str) {
        warn!("⚠ {}", message);
        let _ = Self::print_colored(&format!("⚠ {}\n", message), ConsoleColor::Yellow);
    }
    
    /// 信息消息
    pub fn info(message: &str) {
        info!("ℹ {}", message);
        let _ = Self::print_colored(&format!("ℹ {}\n", message), ConsoleColor::Blue);
    }
    
    /// 调试消息
    pub fn debug(message: &str) {
        debug!("🐛 {}", message);
        let _ = Self::print_colored(&format!("🐛 {}\n", message), ConsoleColor::Cyan);
    }
}
