{"lang": "pt-PT", "fountConsole": {"server": {"start": "A iniciar o servidor", "starting": "A iniciar o servidor...", "ready": "Ser<PERSON>or iniciado.", "usesdTime": "Tempo de arranque: ${time}s", "showUrl": {"https": "Servidor HTTPS a correr em ${url}", "http": "Servidor HTTP a correr em ${url}"}, "standingBy": "Em espera..."}, "jobs": {"restartingJob": "A reiniciar a tarefa ${partname} (${parttype}) do utilizador ${username}: ${uid}"}, "ipc": {"sendCommandFailed": "Falha ao enviar o comando: ${error}", "invalidCommand": "Comando inválido. Por favor, use \"fount runshell <nome_utilizador> <nome_shell> <parâmetros...>\"", "runShellLog": "A executar shell ${shellname} como ${username}, parâmetros: ${args}", "invokeShellLog": "A invocar shell ${shellname} como ${username}, parâmetros: ${invokedata}", "unsupportedCommand": "Tipo de comando não suportado.", "processMessageError": "Erro ao processar a mensagem IPC: ${error}", "invalidCommandFormat": "Formato de comando inválido.", "socketError": "Erro de socket: ${error}", "instanceRunning": "Outra instância já está em execução.", "serverStartPrefix": "Início do servidor", "serverStarted": "Servidor IPC iniciado.", "parseResponseFailed": "Falha ao analisar a resposta do servidor: ${error}", "cannotParseResponse": "Não é possível analisar a resposta do servidor.", "unknownError": "<PERSON><PERSON> desconhecido."}, "partManager": {"git": {"noUpstream": "Nenhum ramo upstream configurado para o ramo '${currentBranch}', ignorando a verificação de atualizações.", "dirtyWorkingDirectory": "O diretório de trabalho não está limpo. Por favor, faça 'stash' ou 'commit' das suas alterações antes de atualizar.", "updating": "A atualizar do repositório remoto...", "localAhead": "O ramo local está à frente do ramo remoto. Não é necessária qualquer atualização.", "diverged": "Os ramos local e remoto divergiram. A forçar a atualização...", "upToDate": "Já está atualizado.", "updateFailed": "Falha ao atualizar os componentes do repositório remoto: ${error}"}, "partInitTime": "${parttype} Componente ${partname} Tempo de inicialização: ${time}s", "partLoadTime": "${parttype} Componente ${partname} Tempo de carregamento: ${time}s"}, "web": {"requestReceived": "Pedido recebido: ${method} ${url}"}, "route": {"setLanguagePreference": "O utilizador ${username} definiu o idioma preferencial: ${preferredLanguages}"}, "auth": {"tokenVerifyError": "Erro na verificação do token: ${error}", "refreshTokenError": "Erro no token de atualização: ${error}", "logoutRefreshTokenProcessError": "Erro ao processar o token de atualização de logout: ${error}", "revokeTokenNoJTI": "Não é possível revogar um token sem JTI.", "accountLockedLog": "A conta do utilizador ${username} foi bloqueada devido a múltiplas tentativas de login falhadas."}, "verification": {"codeGeneratedLog": "Código de verificação: ${code} (expira em 60 segundos).", "codeNotifyTitle": "Código de verificação", "codeNotifyBody": "Código de verificação: ${code} (expira em 60 segundos)."}, "tray": {"readIconFailed": "Falha ao ler o ficheiro do ícone: ${error}", "createTrayFailed": "Falha ao criar o ícone na área de notificação: ${error}"}, "discordbot": {"botStarted": "O personagem ${charname} ligou-se ao bot Discord ${botusername}."}, "telegrambot": {"botStarted": "O personagem ${charname} ligou-se ao bot Telegram ${botusername}."}}, "protocolhandler": {"title": "A processar o protocolo Fount", "processing": "A processar o protocolo...", "invalidProtocol": "Protocolo inválido.", "insufficientParams": "Parâmetros insuficientes.", "unknownCommand": "Comando desconhecido.", "shellCommandSent": "Comando shell enviado.", "shellCommandFailed": "Falha ao enviar o comando shell.", "shellCommandError": "Erro ao enviar o comando shell."}, "auth": {"title": "Autenticação", "subtitle": "Os dados do utilizador são armazenados localmente.", "usernameLabel": "Nome de utilizador:", "usernameInput": {"placeholder": "Introduza o nome de utilizador"}, "passwordLabel": "Palavra-passe:", "passwordInput": {"placeholder": "Introduza a palavra-passe"}, "confirmPasswordLabel": "Confirmar palavra-passe:", "confirmPasswordInput": {"placeholder": "Introduza novamente a palavra-passe"}, "verificationCodeLabel": "Código de verificação:", "verificationCodeInput": {"placeholder": "Introduza o código de verificação"}, "sendCodeButton": "Enviar código", "login": {"title": "<PERSON><PERSON><PERSON>", "submitButton": "<PERSON><PERSON><PERSON>", "toggleLink": {"text": "Não tem conta?", "link": "Registe-se agora"}}, "register": {"title": "Registar", "submitButton": "Registar", "toggleLink": {"text": "Já tem uma conta?", "link": "Iniciar se<PERSON><PERSON> agora"}}, "error": {"passwordMismatch": "As palavras-passe não correspondem.", "loginError": "Erro ao iniciar sessão.", "registrationError": "Erro ao registar.", "verificationCodeError": "O código de verificação está incorreto ou expirou.", "verificationCodeSent": "Código de verificação enviado com sucesso.", "verificationCodeSendError": "Falha ao enviar o código de verificação.", "verificationCodeRateLimit": "Demasiados pedidos de código de verificação. Por favor, tente novamente mais tarde.", "lowPasswordStrength": "A segurança da palavra-passe é demasiado fraca.", "accountAlreadyExists": "A conta já existe."}, "passwordStrength": {"veryWeak": "<PERSON><PERSON> fraca", "weak": "Fraca", "normal": "Normal", "strong": "Forte", "veryStrong": "<PERSON><PERSON> forte"}}, "tutorial": {"title": "Que tal um tutorial?", "modal": {"title": "Bem-vindo ao Fount!", "instruction": "Gostaria de seguir o tutorial para principiantes?", "buttons": {"start": "In<PERSON><PERSON>", "skip": "Saltar"}}, "endScreen": {"title": "Incrível! Tutorial Concluído!", "subtitle": "Agora já aprendeu a utilizar!", "endButton": "Come<PERSON><PERSON>!"}, "progressMessages": {"mouseMove": "Por favor, tente mover o rato ${mouseIcon} com a sua mão.", "keyboardPress": "Por favor, prima uma tecla no seu teclado ${keyboardIcon}.", "mobileTouchMove": "Por favor, toque no ecrã do seu telemóvel ${phoneIcon} com um dedo e depois deslize-o.", "mobileClick": "Por favor, toque no ecrã do seu telemóvel ${phoneIcon} com um dedo."}}, "home": {"title": "Início", "escapeConfirm": "Tem a certeza que quer sair do Fount?", "filterInput": {"placeholder": "Pesquisar..."}, "sidebarTitle": "<PERSON><PERSON><PERSON>", "itemDescription": "Selecione um item aqui para ver os detalhes.", "noDescription": "Nenhuma descrição disponível.", "alerts": {"fetchHomeRegistryFailed": "Falha ao obter as informações do registo da página inicial."}, "functionMenu": {"icon": {"alt": "Menu de funções"}}, "chars": {"tab": "Personagens", "title": "Seleção de Personagem", "subtitle": "Selecione um personagem e comece a conversar!", "none": "Nada para mostrar", "card": {"refreshButton": {"alt": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "noTags": "Sem etiquetas", "version": "Vers<PERSON>", "author": "Autor", "homepage": "Página inicial", "issuepage": "Comunicar problemas", "defaultCheckbox": {"title": "Definir como personagem padrão"}}}, "worlds": {"tab": "Mundos", "title": "Seleção de Mundo", "subtitle": "Escolha um mundo e mergulhe nele!", "none": "Nada para mostrar", "card": {"refreshButton": {"alt": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "noTags": "Sem etiquetas", "version": "Vers<PERSON>", "author": "Autor", "homepage": "Página inicial", "issuepage": "Comunicar problemas", "defaultCheckbox": {"title": "Definir como mundo padrão"}}}, "personas": {"tab": "Personas", "title": "Seleção de Persona", "subtitle": "Selecione uma persona e experimente uma nova vida.", "none": "Nada para mostrar", "card": {"refreshButton": {"alt": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "noTags": "Sem etiquetas", "version": "Vers<PERSON>", "author": "Autor", "homepage": "Página inicial", "issuepage": "Comunicar problemas", "defaultCheckbox": {"title": "Definir como persona padrão"}}}}, "themeManage": {"title": "Gestão de Temas", "instruction": "Escolha um tema!", "themes": {"auto": "Automático", "light": "<PERSON><PERSON><PERSON>", "dark": "Escuro", "cupcake": "Cupcake", "bumblebee": "Bumblebee", "emerald": "Esm<PERSON><PERSON>", "corporate": "Corporativo", "synthwave": "Synthwave", "retro": "Retro", "cyberpunk": "Cyberpunk", "valentine": "Dia dos Namorados", "halloween": "Halloween", "garden": "Jardim", "forest": "Flores<PERSON>", "aqua": "Aqua", "lofi": "lo-fi", "pastel": "Pastel", "fantasy": "Fantasia", "wireframe": "Wireframe", "black": "Preto", "luxury": "Luxo", "dracula": "<PERSON><PERSON><PERSON>", "cmyk": "CMYK", "autumn": "Outono", "business": "<PERSON>eg<PERSON><PERSON><PERSON>", "acid": "Acid", "lemonade": "Limonada", "night": "Noite", "coffee": "Café", "winter": "Inverno", "dim": "Ténue", "nord": "Nórdico", "sunset": "Sunset", "caramellatte": "Caramel Latte", "abyss": "Abismo", "silk": "Seda"}}, "import": {"title": "Importar", "tabs": {"fileImport": "Importar Ficheiro", "textImport": "Importar Texto"}, "dropArea": {"icon": {"alt": "Ícone de Carregamento"}, "text": "Arraste e solte ficheiros aqui ou clique para selecionar ficheiros."}, "textArea": {"placeholder": "Digite o texto para importar..."}, "buttons": {"import": "Importar"}, "alerts": {"importSuccess": "Importação bem-sucedida.", "importFailed": "Falha na importação: ${error}", "unknownError": "<PERSON><PERSON> desconhecido."}, "errors": {"noFileSelected": "Por favor, selecione um ficheiro.", "fileImportFailed": "Falha na importação do ficheiro: ${message}", "noTextContent": "Por favor, introduza o conteúdo do texto.", "textImportFailed": "Falha na importação do texto: ${message}", "unknownError": "<PERSON><PERSON> desconhecido.", "handler": "<PERSON><PERSON><PERSON><PERSON>", "error": "Erro"}, "fileItem": {"removeButton": {"title": "Remover"}, "removeButtonIcon": {"alt": "Remover"}}}, "aisource_editor": {"title": "Editor de Fonte de IA", "fileList": {"title": "Lista de Fontes de IA", "addButton": {"title": "+"}}, "configTitle": "Configuração da Fonte de IA", "generatorSelect": {"label": "Selecionar Gerador", "placeholder": "Por favor, selecione"}, "buttons": {"save": "Guardar", "delete": "Eliminar"}, "alerts": {"fetchFileListFailed": "Falha ao obter a lista de ficheiros: ${error}", "fetchGeneratorListFailed": "Falha ao obter a lista de geradores: ${error}", "fetchFileDataFailed": "Falha ao obter os dados do ficheiro: ${error}", "noFileSelectedSave": "Nenhum ficheiro selecionado para guardar.", "saveFileFailed": "Falha ao guardar o ficheiro: ${error}", "noFileSelectedDelete": "Nenhum ficheiro selecionado para eliminar.", "deleteFileFailed": "Falha ao eliminar o ficheiro: ${error}", "invalidFileName": "O nome do ficheiro não pode conter os seguintes caracteres: / \\ : * ? \" < > |", "addFileFailed": "Falha ao adicionar o ficheiro: ${error}", "fetchConfigTemplateFailed": "Falha ao obter o modelo de configuração.", "noGeneratorSelectedSave": "Por favor, selecione um gerador antes de guardar."}, "confirm": {"unsavedChanges": "Tem alterações não guardadas. <PERSON><PERSON><PERSON> des<PERSON> as alterações?", "deleteFile": "Tem a certeza que quer eliminar o ficheiro?", "unsavedChangesBeforeUnload": "Tem alterações não guardadas. Tem a certeza que quer sair desta página?"}, "prompts": {"newFileName": "Por favor, insira um novo nome de ficheiro de fonte de IA (sem extensão):"}, "editor": {"disabledIndicator": "Por favor, selecione primeiro um gerador."}}, "part_config": {"title": "Configuração de Componente", "pageTitle": "Configuração de Componente", "labels": {"partType": "Selecionar Tipo de Componente", "part": "Selecionar Componente"}, "placeholders": {"partTypeSelect": "Por favor, selecione", "partSelect": "Por favor, selecione"}, "editor": {"title": "Configuração de Componente", "disabledIndicator": "Este componente não suporta configuração.", "buttons": {"save": "Guardar"}}, "errorMessage": {"icon": {"alt": "Mensagem de Erro"}}, "alerts": {"fetchPartTypesFailed": "Falha ao obter os tipos de componente.", "fetchPartsFailed": "Falha ao obter a lista de componentes.", "loadEditorFailed": "<PERSON><PERSON><PERSON> ao carregar o editor.", "saveConfigFailed": "Falha ao guardar a configuração do componente.", "unsavedChanges": "Tem alterações não guardadas. <PERSON><PERSON><PERSON> des<PERSON> as alterações?", "beforeUnload": "Tem alterações não guardadas. Tem a certeza que quer sair?"}}, "uninstall": {"title": "<PERSON><PERSON><PERSON><PERSON>", "titleWithName": "Desinstalar ${type}/${name}", "confirmMessage": "Tem a certeza que pretende desinstalar ${type}: ${name}?", "invalidParamsTitle": "<PERSON><PERSON>â<PERSON><PERSON>", "infoMessage": {"icon": {"alt": "Ícone de Informação"}}, "errorMessage": {"icon": {"alt": "Í<PERSON><PERSON>rro"}}, "buttons": {"confirm": "Confirma<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "back": "Voltar"}, "alerts": {"success": "${type}: ${name} desinstalado com sucesso.", "failed": "Falha ao desinstalar: ${error}", "invalidParams": "Parâmetros de pedido inválidos.", "httpError": "Erro HTTP! Código de estado: ${status}"}}, "chat": {"new": {"title": "Nova conversa"}, "title": "Cha<PERSON>", "sidebar": {"world": {"icon": {"alt": "Ícone de Mundo"}, "title": "Mundo"}, "persona": {"icon": {"alt": "Ícone de Persona do utilizador"}, "title": "Persona do utilizador"}, "charList": {"icon": {"alt": "Ícone de Lista de Personagens"}, "title": "Lista de Personagens", "buttons": {"addChar": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "addCharIcon": {"alt": "Ícone de Adicionar <PERSON>"}}}, "noSelection": "Não selecionado", "noDescription": "Nenhuma descrição."}, "chatArea": {"title": "Cha<PERSON>", "menuButton": {"title": "<PERSON><PERSON>"}, "menuButtonIcon": {"alt": "Í<PERSON><PERSON> de <PERSON>u"}, "input": {"placeholder": "Escreva uma mensagem...\\nCtrl+Enter para enviar"}, "sendButton": {"title": "Enviar"}, "sendButtonIcon": {"alt": "Ícone de Enviar"}, "uploadButton": {"title": "<PERSON><PERSON><PERSON>"}, "uploadButtonIcon": {"alt": "Ícone de Carregamento"}, "voiceButton": {"title": "Voz"}, "voiceButtonIcon": {"alt": "Ícone de Voz"}, "photoButton": {"title": "Foto"}, "photoButtonIcon": {"alt": "Ícone de Foto"}}, "rightSidebar": {"title": "<PERSON><PERSON><PERSON>"}, "messageList": {"confirmDeleteMessage": "Confirmar a eliminação desta mensagem?"}, "voiceRecording": {"errorAccessingMicrophone": "Erro ao aceder ao microfone."}, "messageView": {"buttons": {"edit": {"title": "<PERSON><PERSON>"}, "editIcon": {"alt": "Ícone de Editar"}, "delete": {"title": "Eliminar"}, "deleteIcon": {"alt": "Ícone de Eliminar"}}}, "messageEdit": {"input": {"placeholder": "Introduza o conteúdo..."}, "buttons": {"confirm": {"title": "Confirmar"}, "confirmIcon": {"alt": "Ícone de Confirmar"}, "cancel": {"title": "<PERSON><PERSON><PERSON>"}, "cancelIcon": {"alt": "Ícone de Cancelar"}, "upload": {"title": "<PERSON><PERSON><PERSON>"}, "uploadIcon": {"alt": "Ícone de Carregamento"}}}, "attachment": {"buttons": {"download": {"title": "Transferir"}, "downloadIcon": {"alt": "Ícone de Transferência"}, "delete": {"title": "Eliminar"}, "deleteIcon": {"alt": "Ícone de Eliminar"}}}, "charCard": {"frequencyLabel": "Frequência", "buttons": {"removeChar": {"title": "Remover da conversa"}, "removeCharIcon": {"alt": "Ícone de Remover Personagem"}, "forceReply": {"title": "<PERSON><PERSON><PERSON>"}, "forceReplyIcon": {"alt": "Ícone de Forçar Resposta"}}}}, "chat_history": {"title": "Histórico de Conversas", "pageTitle": "Histórico de Conversas", "sortOptions": {"time_desc": "Data (mais recentes)", "time_asc": "Data (mais antigas)"}, "filterInput": {"placeholder": "Pesquisar..."}, "selectAll": "Selecionar Tudo", "buttons": {"reverseSelect": "Inverter Seleção", "deleteSelected": "Eliminar Selecionados", "exportSelected": "Exportar Selecionados"}, "confirmDeleteChat": "Tem a certeza que quer eliminar o histórico de conversas com ${chars}?", "confirmDeleteMultiChats": "Tem a certeza que quer eliminar os ${count} históricos de conversas selecionados?", "alerts": {"noChatSelectedForDeletion": "Por favor, selecione os históricos de conversas para eliminar.", "noChatSelectedForExport": "Por favor, selecione os históricos de conversas para exportar.", "copyError": "Cópia falhou", "deleteError": "A exclusão falhou", "exportError": "A exportação falhou"}, "chatItemButtons": {"continue": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copiar", "export": "Exportar", "delete": "Eliminar"}}, "discord_bots": {"title": "Bots do Discord", "cardTitle": "Bots do Discord", "buttons": {"newBot": "Novo", "deleteBot": "Eliminar"}, "configCard": {"title": "Configuração do Bot", "labels": {"character": "Personagem", "apiKey": "<PERSON><PERSON> da <PERSON>", "config": "Configuração"}, "charSelectPlaceholder": "Selecionar Personagem", "apiKeyInput": {"placeholder": "Introduza a Chave da <PERSON>"}, "toggleApiKeyIcon": {"alt": "Alternar Visibilidade da Chave da API"}, "buttons": {"saveConfig": "Guardar Configuração", "startBot": "Iniciar", "stopBot": "<PERSON><PERSON>"}}, "prompts": {"newBotName": "Por favor, insira um novo nome para o Bot:"}, "alerts": {"botExists": "Um bot com o nome \"${botname}\" já existe. Por favor, use outro nome.", "unsavedChanges": "Tem alterações não guardadas. <PERSON><PERSON><PERSON> des<PERSON> as alterações?", "configSaved": "Configuração guardada com sucesso.", "httpError": "Erro HTTP.", "beforeUnload": "Tem alterações não guardadas. Tem a certeza que quer sair?"}}, "telegram_bots": {"title": "Bots do Telegram", "cardTitle": "Gestão de Bots do Telegram", "buttons": {"newBot": "Novo", "deleteBot": "Eliminar"}, "configCard": {"title": "Configuração do Bot", "labels": {"character": "Personagem associado", "botToken": "Token do Bot Telegram", "config": "Configuração"}, "charSelectPlaceholder": "Selecionar Personagem", "botTokenInput": {"placeholder": "Introduza o Token do Bot Telegram"}, "toggleBotTokenIcon": {"alt": "Alternar visibilidade do token do bot"}, "buttons": {"saveConfig": "Guardar Configuração", "startBot": "Iniciar", "stopBot": "<PERSON><PERSON>"}}, "prompts": {"newBotName": "Por favor, insira um novo nome para o Bot:"}, "alerts": {"botExists": "Um bot com o nome \"${botname}\" já existe. Por favor, use outro nome.", "unsavedChanges": "Tem alterações não guardadas. Quer mesmo descartá-las?", "configSaved": "A configuração foi guardada com sucesso!", "httpError": "Erro HTTP.", "beforeUnload": "Tem alterações não guardadas. Tem a certeza que quer sair desta página?"}}, "terminal_assistant": {"title": "Assistente de Terminal", "initialMessage": "O Fount suporta a implementação dos seus personagens favoritos no seu terminal para o ajudar na programação!", "initialMessageLink": "Clique aqui para saber mais."}, "access": {"title": "Aceder ao Fount noutros dispositivos", "heading": "Quer aceder ao Fount noutros dispositivos?", "instruction": {"sameLAN": "Certifique-se de que o dispositivo e o anfitrião do Fount estão na mesma rede local.", "accessthis": "Visite este URL:"}, "copyButton": "Copiar URL", "copied": "URL copiado para a área de transferência!"}, "proxy": {"title": "Proxy de API", "heading": "Endereço do Proxy da API OpenAI", "instruction": "Introduza o endereço seguinte em qualquer aplicação que necessite do formato da API OpenAI para usar as fontes de IA no Fount!", "copyButton": "<PERSON><PERSON><PERSON>", "copied": "Endereço copiado para a área de transferência!"}, "404": {"title": "Página não encontrada", "pageNotFoundText": "Ups! Parece que acedeu a uma página que não existe.", "homepageButton": "Voltar à página inicial", "MineSweeper": {"difficultyLabel": "Dificuldade:", "difficultyEasy": "F<PERSON><PERSON>l", "difficultyMedium": "Médio", "difficultyHard": "Dif<PERSON><PERSON>l", "difficultyCustom": "Personalizado", "minesLeftLabel": "Minas restantes:", "timeLabel": "Tempo:", "restartButton": "<PERSON><PERSON><PERSON><PERSON>", "rowsLabel": "Linhas:", "colsLabel": "Colunas:", "minesCountLabel": "Nº de minas:", "winMessage": "Para<PERSON><PERSON><PERSON>, ganhou!", "loseMessage": "<PERSON><PERSON> de jogo, pisou numa mina!", "soundOn": "Som ligado", "soundOff": "<PERSON>m des<PERSON>do"}}, "userSettings": {"title": "Definições do Utilizador", "PageTitle": "Definições do Utilizador", "apiError": "Falha no pedido à API: ${message}", "generalError": "Ocorreu um erro: ${message}", "userInfo": {"title": "Informações do Utilizador", "usernameLabel": "Nome de utilizador:", "creationDateLabel": "Data de criação da conta:", "folderSizeLabel": "Tamanho dos dados do utilizador:", "folderPathLabel": "Caminho dos dados do utilizador:", "copyPathBtnTitle": "<PERSON><PERSON><PERSON> caminho", "copiedAlert": "<PERSON>inho copiado para a área de transferência!"}, "changePassword": {"title": "Alterar palavra-passe", "currentPasswordLabel": "Palavra-passe atual:", "newPasswordLabel": "Nova palavra-passe:", "confirmNewPasswordLabel": "Confirmar nova palavra-passe:", "submitButton": "Alterar palavra-passe", "errorMismatch": "As novas palavras-passe não coincidem.", "success": "Palavra-passe alterada com sucesso."}, "renameUser": {"title": "Mudar nome de utilizador", "newUsernameLabel": "Novo nome de utilizador:", "submitButton": "Mudar nome de utilizador", "confirmMessage": "Tem a certeza que quer mudar o seu nome de utilizador? Será necessário iniciar sessão novamente.", "success": "Nome de utilizador alterado para \"${newUsername}\" com sucesso. Agora a sua sessão será terminada."}, "userDevices": {"title": "Dispositivos/Sessões do utilizador", "refreshButtonTitle": "<PERSON><PERSON><PERSON><PERSON> lista", "noDevicesFound": "Nenhum dispositivo ou sessão encontrado.", "deviceInfo": "ID do dispositivo: ${deviceId}", "thisDevice": "Este dispositivo", "deviceDetails": "Última vez online: ${lastSeen} | IP: ${ipAddress} | UA: ${userAgent}", "revokeButton": "<PERSON><PERSON><PERSON>", "revokeConfirm": "Tem a certeza que quer revogar o acesso para este dispositivo/sessão?", "revokeSuccess": "Dispositivo/Sessão revogado com sucesso."}, "logout": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Isto irá terminar a sessão da sua conta no dispositivo atual.", "buttonText": "<PERSON><PERSON><PERSON><PERSON>", "confirmMessage": "Tem a certeza que quer terminar a sessão?", "successMessage": "Sessão terminada com sucesso. A redirecionar para a página de início de sessão..."}, "deleteAccount": {"title": "Eliminar conta", "warning": "Atenção: esta ação irá eliminar permanentemente a sua conta e todos os dados relacionados, não podendo ser revertida.", "submitButton": "Eliminar a minha conta", "confirmMessage1": "Atenção! Tem a certeza que quer eliminar permanentemente a sua conta? Esta ação não pode ser revertida.", "confirmMessage2": "Para confirmar a eliminação, introduza o seu nome de utilizador \"${username}\":", "usernameMismatch": "O nome de utilizador introduzido não corresponde ao utilizador atual. A eliminação foi cancelada.", "success": "Conta eliminada com sucesso. Agora a sua sessão será terminada."}, "passwordConfirm": {"title": "Confirmar operação", "message": "Para continuar, introduza a sua palavra-passe atual:", "passwordLabel": "Palavra-passe:", "confirmButton": "Confirmar", "cancelButton": "<PERSON><PERSON><PERSON>"}}}