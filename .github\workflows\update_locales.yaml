name: Update locale files

permissions:
  contents: write

on:
  push:
    paths:
      - 'src/locales/**.json'
      - '**/home_registry.json'
    tags-ignore:
      - '*'
  workflow_dispatch:

jobs:
  update-locales:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: run script to update locale files
        run: |
          pip install deep_translator
          python ./.esh/commands/update-locales.py
      - name: git add all
        run: git add -A
      - name: Push changes
        uses: actions-go/push@master
        with:
          author-email: <EMAIL>
          author-name: Taromati2
          commit-message: 'file update~'
          remote: origin
          token: ${{ secrets.GITHUB_TOKEN }}
