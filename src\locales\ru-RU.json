{"lang": "ru-RU", "fountConsole": {"server": {"start": "Сервер запускается.", "starting": "Сервер запускается...", "ready": "Сервер запущен.", "usesdTime": "Время запуска: ${time}s", "showUrl": {"https": "HTTPS-сервер запущен на ${url}", "http": "HTTP-сервер запущен на ${url}"}, "standingBy": "В режиме ожидания..."}, "jobs": {"restartingJob": "Перезапуск задачи ${partname} (${parttype}) для пользователя ${username}: ${uid}"}, "ipc": {"sendCommandFailed": "Не удалось отправить команду: ${error}", "invalidCommand": "Неверная команда. Используйте \"fount runshell <имя_пользователя> <имя_оболочки> <параметры...>\"", "runShellLog": "Запуск оболочки ${shellname} от имени ${username}, параметры: ${args}", "invokeShellLog": "Вызов оболочки ${shellname} от имени ${username}, параметры: ${invokedata}", "unsupportedCommand": "Неподдерживаемый тип команды.", "processMessageError": "Ошибка при обработке IPC-сообщения: ${error}", "invalidCommandFormat": "Неверный формат команды.", "socketError": "Ошибка сокета: ${error}", "instanceRunning": "Другой экземпляр уже запущен.", "serverStartPrefix": "Запуск сервера", "serverStarted": "IPC-сервер запущен.", "parseResponseFailed": "Не удалось обработать ответ сервера: ${error}", "cannotParseResponse": "Невозможно обработать ответ сервера.", "unknownError": "Неизвестная ошибка."}, "partManager": {"git": {"noUpstream": "Для ветки '${currentBranch}' не настроена вышестоящая ветка. Проверка обновлений пропущена.", "dirtyWorkingDirectory": "Рабочий каталог нечист. Пожалуйста, сделайте 'stash' или 'commit' ваших изменений перед обновлением.", "updating": "Обновление из удаленного репозитория...", "localAhead": "Локальная ветка опережает удаленную. Обновление не требуется.", "diverged": "Локальная и удаленная ветки разошлись. Выполняется принудительное обновление...", "upToDate": "Уже установлена последняя версия.", "updateFailed": "Не удалось обновить компоненты из удаленного репозитория: ${error}"}, "partInitTime": "${parttype} компонент ${partname} время инициализации: ${time}s", "partLoadTime": "${parttype} компонент ${partname} время загрузки: ${time}s"}, "web": {"requestReceived": "Получен запрос: ${method} ${url}"}, "route": {"setLanguagePreference": "Пользователь ${username} установил предпочитаемый язык: ${preferredLanguages}"}, "auth": {"tokenVerifyError": "Ошибка проверки токена: ${error}", "refreshTokenError": "Ошибка токена обновления: ${error}", "logoutRefreshTokenProcessError": "Ошибка обработки токена обновления при выходе из системы: ${error}", "revokeTokenNoJTI": "Невозможно отозвать токен без JTI.", "accountLockedLog": "Учетная запись пользователя ${username} заблокирована из-за многочисленных неудачных попыток входа."}, "verification": {"codeGeneratedLog": "Проверочный код: ${code} (истекает через 60 секунд).", "codeNotifyTitle": "Проверочный код", "codeNotifyBody": "Проверочный код: ${code} (истекает через 60 секунд)."}, "tray": {"readIconFailed": "Не удалось прочитать файл значка: ${error}", "createTrayFailed": "Не удалось создать значок в области уведомлений: ${error}"}, "discordbot": {"botStarted": "Персонаж ${charname} вошел в Discord бот ${botusername}."}, "telegrambot": {"botStarted": "Персонаж ${charname} вошел в Telegram бот ${botusername}."}}, "protocolhandler": {"title": "Обработка протокола Fount", "processing": "Обработка протокола...", "invalidProtocol": "Недопустимый протокол.", "insufficientParams": "Недостаточно параметров.", "unknownCommand": "Неизвестная команда.", "shellCommandSent": "Команда оболочки отправлена.", "shellCommandFailed": "Не удалось отправить команду оболочки.", "shellCommandError": "Ошибка при отправке команды оболочки."}, "auth": {"title": "Аутентификация", "subtitle": "Данные пользователя хранятся локально.", "usernameLabel": "Имя пользователя:", "usernameInput": {"placeholder": "Введите имя пользователя"}, "passwordLabel": "Пароль:", "passwordInput": {"placeholder": "Введите пароль"}, "confirmPasswordLabel": "Подтвердите пароль:", "confirmPasswordInput": {"placeholder": "Введите пароль еще раз"}, "verificationCodeLabel": "Проверочный код:", "verificationCodeInput": {"placeholder": "Введите проверочный код"}, "sendCodeButton": "Отправить код", "login": {"title": "Войти", "submitButton": "Войти", "toggleLink": {"text": "Нет аккаунта?", "link": "Зарегистрироваться"}}, "register": {"title": "Регистрация", "submitButton": "Зарегистрироваться", "toggleLink": {"text": "Уже есть аккаунт?", "link": "Войти"}}, "error": {"passwordMismatch": "Пароли не совпадают.", "loginError": "Ошибка входа.", "registrationError": "Ошибка регистрации.", "verificationCodeError": "Проверочный код неверный или устарел.", "verificationCodeSent": "Проверочный код успешно отправлен.", "verificationCodeSendError": "Не удалось отправить проверочный код.", "verificationCodeRateLimit": "Слишком много запросов на отправку проверочного кода. Пожалуйста, попробуйте позже.", "lowPasswordStrength": "Пароль слишком простой.", "accountAlreadyExists": "Аккаунт уже существует."}, "passwordStrength": {"veryWeak": "Очень слабый", "weak": "Слабый", "normal": "Средний", "strong": "Надежный", "veryStrong": "Очень надежный"}}, "tutorial": {"title": "Как насчет обучения?", "modal": {"title": "Добро пожаловать в Fount!", "instruction": "Хотите пройти вводное обучение?", "buttons": {"start": "Начать обучение", "skip": "Пропустить"}}, "endScreen": {"title": "Отлично! Обучение завершено!", "subtitle": "Теперь вы знаете, как им пользоваться!", "endButton": "Начать!"}, "progressMessages": {"mouseMove": "Пожалуйста, попробуйте взять мышь ${mouseIcon} рукой и подвигать ею.", "keyboardPress": "Пожалуйста, найдите клавиатуру ${keyboardIcon}<br/>и нажмите любую клавишу.", "mobileTouchMove": "Пожалуйста, коснитесь экрана телефона ${phoneIcon} пальцем<br/>и проведите им по экрану.", "mobileClick": "Пожалуйста, коснитесь экрана телефона ${phoneIcon} пальцем и отпустите."}}, "home": {"title": "Главная", "escapeConfirm": "Вы уверены, что хотите выйти из Fount?", "filterInput": {"placeholder": "Поиск..."}, "sidebarTitle": "Подробности", "itemDescription": "Выберите элемент для просмотра подробностей.", "noDescription": "Описание отсутствует.", "alerts": {"fetchHomeRegistryFailed": "Не удалось получить информацию из реестра главной страницы."}, "functionMenu": {"icon": {"alt": "Меню функций"}}, "chars": {"tab": "Персо<PERSON><PERSON>и", "title": "Выбор персонажа", "subtitle": "Выберите персонажа и начните общение!", "none": "Здесь ничего нет", "card": {"refreshButton": {"alt": "Обновить", "title": "Обновить"}, "noTags": "Нет тегов", "version": "Версия", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "Домашняя страница", "issuepage": "Сообщить о проблеме", "defaultCheckbox": {"title": "Сделать персонажем по умолчанию"}}}, "worlds": {"tab": "М<PERSON><PERSON><PERSON>", "title": "Выбор мира", "subtitle": "Выберите мир и погрузитесь в него!", "none": "Здесь ничего нет", "card": {"refreshButton": {"alt": "Обновить", "title": "Обновить"}, "noTags": "Нет тегов", "version": "Версия", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "Домашняя страница", "issuepage": "Сообщить о проблеме", "defaultCheckbox": {"title": "Сделать миром по умолчанию"}}}, "personas": {"tab": "Персоны", "title": "Выбор персоны", "subtitle": "Выберите персону и проживите новую жизнь.", "none": "Здесь ничего нет", "card": {"refreshButton": {"alt": "Обновить", "title": "Обновить"}, "noTags": "Нет тегов", "version": "Версия", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "Домашняя страница", "issuepage": "Сообщить о проблеме", "defaultCheckbox": {"title": "Сделать персоной по умолчанию"}}}}, "themeManage": {"title": "Управление темами", "instruction": "Выберите тему!", "themes": {"auto": "Автоматически", "light": "Светлая", "dark": "Темная", "cupcake": "Капкейк", "bumblebee": "Бамблби", "emerald": "Изумруд", "corporate": "Корпоративная", "synthwave": "Синтвейв", "retro": "Ретро", "cyberpunk": "К<PERSON>б<PERSON>р<PERSON><PERSON>нк", "valentine": "День святого Валентина", "halloween": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garden": "Сад", "forest": "<PERSON><PERSON><PERSON>", "aqua": "Аква", "lofi": "Lo-Fi", "pastel": "Пастельная", "fantasy": "Фэнтези", "wireframe": "Кар<PERSON><PERSON><PERSON>", "black": "Черная", "luxury": "<PERSON><PERSON><PERSON><PERSON>", "dracula": "Дракула", "cmyk": "CMYK", "autumn": "Осень", "business": "Бизнес", "acid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lemonade": "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "night": "Ночь", "coffee": "Кофе", "winter": "Зима", "dim": "Тусклая", "nord": "Нордическая", "sunset": "Закат", "caramellatte": "Карамельный Латте", "abyss": "Бездна", "silk": "Шелковая"}}, "import": {"title": "Импорт", "tabs": {"fileImport": "Импорт из файла", "textImport": "Импорт текста"}, "dropArea": {"icon": {"alt": "Значок загрузки"}, "text": "Перетащите файлы сюда или нажмите, чтобы выбрать файлы."}, "textArea": {"placeholder": "Введите текст для импорта..."}, "buttons": {"import": "Импортировать"}, "alerts": {"importSuccess": "Импорт успешно завершен.", "importFailed": "Ошибка импорта: ${error}", "unknownError": "Неизвестная ошибка."}, "errors": {"noFileSelected": "Пожалуйста, выберите файл.", "fileImportFailed": "Ошибка импорта файла: ${message}", "noTextContent": "Пожалуйста, введите текст.", "textImportFailed": "Ошибка импорта текста: ${message}", "unknownError": "Неизвестная ошибка.", "handler": "Обработчик", "error": "Ошибка"}, "fileItem": {"removeButton": {"title": "Удалить"}, "removeButtonIcon": {"alt": "удалить"}}}, "aisource_editor": {"title": "Редактор источников ИИ", "fileList": {"title": "Список источников ИИ", "addButton": {"title": "+"}}, "configTitle": "Настройка источника ИИ", "generatorSelect": {"label": "Выберите генератор", "placeholder": "Пожалуйста, выберите"}, "buttons": {"save": "Сохранить", "delete": "Удалить"}, "alerts": {"fetchFileListFailed": "Не удалось получить список файлов: ${error}", "fetchGeneratorListFailed": "Не удалось получить список генераторов: ${error}", "fetchFileDataFailed": "Не удалось получить данные файла: ${error}", "noFileSelectedSave": "Файл для сохранения не выбран.", "saveFileFailed": "Не удалось сохранить файл: ${error}", "noFileSelectedDelete": "Файл для удаления не выбран.", "deleteFileFailed": "Не удалось удалить файл: ${error}", "invalidFileName": "Имя файла не может содержать следующие символы: / \\ : * ? \" < > |", "addFileFailed": "Не удалось добавить файл: ${error}", "fetchConfigTemplateFailed": "Не удалось получить шаблон конфигурации.", "noGeneratorSelectedSave": "Пожалуйста, выберите генератор перед сохранением."}, "confirm": {"unsavedChanges": "У вас есть несохраненные изменения. Хотите их отменить?", "deleteFile": "Вы уверены, что хотите удалить этот файл?", "unsavedChangesBeforeUnload": "У вас есть несохраненные изменения. Вы уверены, что хотите покинуть эту страницу?"}, "prompts": {"newFileName": "Пожалуйста, введите имя нового файла источника ИИ (без расширения):"}, "editor": {"disabledIndicator": "Пожалуйста, сначала выберите генератор."}}, "part_config": {"title": "Настройка компонента", "pageTitle": "Настройка компонента", "labels": {"partType": "Выберите тип компонента", "part": "Выберите компонент"}, "placeholders": {"partTypeSelect": "Пожалуйста, выберите", "partSelect": "Пожалуйста, выберите"}, "editor": {"title": "Настройка компонента", "disabledIndicator": "Этот компонент не поддерживает настройку.", "buttons": {"save": "Сохранить"}}, "errorMessage": {"icon": {"alt": "Сообщение об ошибке"}}, "alerts": {"fetchPartTypesFailed": "Не удалось получить типы компонентов.", "fetchPartsFailed": "Не удалось получить список компонентов.", "loadEditorFailed": "Не удалось загрузить редактор.", "saveConfigFailed": "Не удалось сохранить настройку компонента.", "unsavedChanges": "У вас есть несохраненные изменения. Хотите их отменить?", "beforeUnload": "У вас есть несохраненные изменения. Вы уверены, что хотите покинуть страницу?"}}, "uninstall": {"title": "Удалить", "titleWithName": "Удалить ${type}/${name}", "confirmMessage": "Вы уверены, что хотите удалить ${type}: ${name}?", "invalidParamsTitle": "Недопустимые параметры", "infoMessage": {"icon": {"alt": "Значок информации"}}, "errorMessage": {"icon": {"alt": "Значок ошибки"}}, "buttons": {"confirm": "Подтвердить удаление", "cancel": "Отмена", "back": "Назад"}, "alerts": {"success": "${type}: ${name} успешно удален(о).", "failed": "Не удалось удалить: ${error}", "invalidParams": "Недопустимые параметры запроса.", "httpError": "Ошибка HTTP! Код состояния: ${status}"}}, "chat": {"new": {"title": "Новый чат"}, "title": "Чат", "sidebar": {"world": {"icon": {"alt": "Значок мира"}, "title": "<PERSON><PERSON><PERSON>"}, "persona": {"icon": {"alt": "Значок персоны пользователя"}, "title": "Персона пользователя"}, "charList": {"icon": {"alt": "Значок списка персонажей"}, "title": "Список персонажей", "buttons": {"addChar": {"title": "Добавить персонажа"}, "addCharIcon": {"alt": "Значок добавления персонажа"}}}, "noSelection": "Не выбрано", "noDescription": "Описание отсутствует."}, "chatArea": {"title": "Чат", "menuButton": {"title": "<PERSON>е<PERSON><PERSON>"}, "menuButtonIcon": {"alt": "Значок меню"}, "input": {"placeholder": "Введите сообщение...\\nCtrl+Enter для отправки"}, "sendButton": {"title": "Отправить"}, "sendButtonIcon": {"alt": "Значок отправки"}, "uploadButton": {"title": "Загрузить"}, "uploadButtonIcon": {"alt": "Значок загрузки"}, "voiceButton": {"title": "Голосовой ввод"}, "voiceButtonIcon": {"alt": "Значок голосового ввода"}, "photoButton": {"title": "Фото"}, "photoButtonIcon": {"alt": "Значок фото"}}, "rightSidebar": {"title": "Подробности"}, "messageList": {"confirmDeleteMessage": "Вы уверены, что хотите удалить это сообщение?"}, "voiceRecording": {"errorAccessingMicrophone": "Не удалось получить доступ к микрофону."}, "messageView": {"buttons": {"edit": {"title": "Редактировать"}, "editIcon": {"alt": "Значок редактирования"}, "delete": {"title": "Удалить"}, "deleteIcon": {"alt": "Значок удаления"}}}, "messageEdit": {"input": {"placeholder": "Введите текст..."}, "buttons": {"confirm": {"title": "Подтвердить"}, "confirmIcon": {"alt": "Значок подтверждения"}, "cancel": {"title": "Отмена"}, "cancelIcon": {"alt": "Значок отмены"}, "upload": {"title": "Загрузить"}, "uploadIcon": {"alt": "Значок загрузки"}}}, "attachment": {"buttons": {"download": {"title": "Скачать"}, "downloadIcon": {"alt": "Значок скачивания"}, "delete": {"title": "Удалить"}, "deleteIcon": {"alt": "Значок удаления"}}}, "charCard": {"frequencyLabel": "Частота", "buttons": {"removeChar": {"title": "Удалить из чата"}, "removeCharIcon": {"alt": "Значок удаления персонажа"}, "forceReply": {"title": "Принудительный ответ"}, "forceReplyIcon": {"alt": "Значок принудительного ответа"}}}}, "chat_history": {"title": "История чатов", "pageTitle": "История чатов", "sortOptions": {"time_desc": "По времени (сначала новые)", "time_asc": "По времени (сначала старые)"}, "filterInput": {"placeholder": "Поиск..."}, "selectAll": "Выбрать все", "buttons": {"reverseSelect": "Обратить выбор", "deleteSelected": "Удалить выбранные", "exportSelected": "Экспортировать выбранные"}, "confirmDeleteChat": "Вы уверены, что хотите удалить историю чата с ${chars}?", "confirmDeleteMultiChats": "Вы уверены, что хотите удалить выбранные ${count} истории чатов?", "alerts": {"noChatSelectedForDeletion": "Пожалуйста, выберите чаты для удаления.", "noChatSelectedForExport": "Пожалуйста, выберите чаты для экспорта.", "copyError": "Копия не удалась", "deleteError": "Удаление не удалось", "exportError": "Экспорт не удался"}, "chatItemButtons": {"continue": "Продолжить", "copy": "Копировать", "export": "Экспортировать", "delete": "Удалить"}}, "discord_bots": {"title": "Discord боты", "cardTitle": "Discord боты", "buttons": {"newBot": "Создать нового", "deleteBot": "Удалить"}, "configCard": {"title": "Настройка бота", "labels": {"character": "Перс<PERSON><PERSON><PERSON>", "apiKey": "API-клю<PERSON> Discord", "config": "Конфигурация"}, "charSelectPlaceholder": "Выберите персонажа", "apiKeyInput": {"placeholder": "Введите API-ключ"}, "toggleApiKeyIcon": {"alt": "Показать/скрыть API-ключ"}, "buttons": {"saveConfig": "Сохранить конфигурацию", "startBot": "Запустить", "stopBot": "Остановить"}}, "prompts": {"newBotName": "Пожалуйста, введите имя нового бота:"}, "alerts": {"botExists": "Бот с именем «${botname}» уже существует. Пожалуйста, используйте другое имя.", "unsavedChanges": "У вас есть несохраненные изменения. Хотите их отменить?", "configSaved": "Конфигурация успешно сохранена.", "httpError": "Ошибка HTTP.", "beforeUnload": "У вас есть несохраненные изменения. Вы уверены, что хотите покинуть страницу?"}}, "telegram_bots": {"title": "Боты Telegram", "cardTitle": "Управление ботами Telegram", "buttons": {"newBot": "Создать нового", "deleteBot": "Удалить"}, "configCard": {"title": "Настройка бота", "labels": {"character": "Связанный персонаж", "botToken": "Токен Telegram бота", "config": "Конфигурация"}, "charSelectPlaceholder": "Выберите персонажа", "botTokenInput": {"placeholder": "Введите токен Telegram бота"}, "toggleBotTokenIcon": {"alt": "Показать/скрыть токен бота"}, "buttons": {"saveConfig": "Сохранить конфигурацию", "startBot": "Запустить", "stopBot": "Остановить"}}, "prompts": {"newBotName": "Пожалуйста, введите имя нового бота:"}, "alerts": {"botExists": "Бот с именем «${botname}» уже существует. Пожалуйста, используйте другое имя.", "unsavedChanges": "У вас есть несохраненные изменения. Вы уверены, что хотите их отменить?", "configSaved": "Конфигурация успешно сохранена!", "httpError": "Ошибка HTTP.", "beforeUnload": "У вас есть несохраненные изменения. Вы уверены, что хотите покинуть эту страницу?"}}, "terminal_assistant": {"title": "Терминальный помощник", "initialMessage": "Fount поддерживает развертывание ваших любимых персонажей в терминале для помощи в написании кода!", "initialMessageLink": "Нажмите здесь, чтобы узнать больше."}, "access": {"title": "Доступ к Fount на других устройствах", "heading": "Хотите получить доступ к Fount на других устройствах?", "instruction": {"sameLAN": "Убедитесь, что устройство и хост Fount находятся в одной локальной сети.", "accessthis": "Перейдите по этому URL:"}, "copyButton": "Копировать URL", "copied": "URL скопирован в буфер обмена!"}, "proxy": {"title": "API прокси", "heading": "Адрес прокси OpenAI API", "instruction": "Введите следующий адрес в любое приложение, требующее формат OpenAI API, чтобы использовать источники ИИ в Fount!", "copyButton": "Копировать адрес", "copied": "Адрес скопирован в буфер обмена!"}, "404": {"title": "Страница не найдена", "pageNotFoundText": "Ой! Кажется, вы зашли на несуществующую страницу.", "homepageButton": "Вернуться на главную страницу", "MineSweeper": {"difficultyLabel": "Сложность:", "difficultyEasy": "Легко", "difficultyMedium": "Средне", "difficultyHard": "Сложно", "difficultyCustom": "Настроить", "minesLeftLabel": "Осталось мин:", "timeLabel": "Время:", "restartButton": "Начать заново", "rowsLabel": "Строк:", "colsLabel": "Столбцов:", "minesCountLabel": "Количество мин:", "winMessage": "Поздравляем, вы победили!", "loseMessage": "Игра окончена, вы подорвались на мине!", "soundOn": "Звук включен", "soundOff": "Звук выключен"}}, "userSettings": {"title": "Настройки пользователя", "PageTitle": "Настройки пользователя", "apiError": "Ошибка запроса API: ${message}", "generalError": "Произошла ошибка: ${message}", "userInfo": {"title": "Информация о пользователе", "usernameLabel": "Имя пользователя:", "creationDateLabel": "Дата создания учетной записи:", "folderSizeLabel": "Размер данных пользователя:", "folderPathLabel": "Путь к данным пользователя:", "copyPathBtnTitle": "Копировать путь", "copiedAlert": "Путь скопирован в буфер обмена!"}, "changePassword": {"title": "Изменить пароль", "currentPasswordLabel": "Текущий пароль:", "newPasswordLabel": "Новый пароль:", "confirmNewPasswordLabel": "Подтвердите новый пароль:", "submitButton": "Изменить пароль", "errorMismatch": "Новые пароли не совпадают.", "success": "Пароль успешно изменен."}, "renameUser": {"title": "Переименовать пользователя", "newUsernameLabel": "Новое имя пользователя:", "submitButton": "Переименовать пользователя", "confirmMessage": "Вы уверены, что хотите изменить имя пользователя? Потребуется повторный вход в систему.", "success": "Имя пользователя успешно изменено на «${newUsername}». Теперь будет выполнен выход из системы."}, "userDevices": {"title": "Устройства и сеансы пользователя", "refreshButtonTitle": "Обновить список", "noDevicesFound": "Устройства или сеансы не найдены.", "deviceInfo": "ID устройства: ${deviceId}", "thisDevice": "Это устройство", "deviceDetails": "Последний онлайн: ${lastSeen} | IP: ${ipAddress} | UA: ${userAgent}", "revokeButton": "Отозвать", "revokeConfirm": "Вы уверены, что хотите отозвать доступ для этого устройства/сеанса?", "revokeSuccess": "Устройство/сеанс успешно отозван."}, "logout": {"title": "Выйти", "description": "Это выполнит выход из вашей учетной записи на текущем устройстве.", "buttonText": "Выйти", "confirmMessage": "Вы уверены, что хотите выйти?", "successMessage": "Выход выполнен успешно. Перенаправление на страницу входа..."}, "deleteAccount": {"title": "Удалить учетную запись", "warning": "Внимание: это действие навсегда удалит вашу учетную запись и все связанные с ней данные и не может быть отменено.", "submitButton": "Удалить мою учетную запись", "confirmMessage1": "Внимание! Вы уверены, что хотите навсегда удалить свою учетную запись? Это действие необратимо.", "confirmMessage2": "Для подтверждения удаления введите ваше имя пользователя «${username}»:", "usernameMismatch": "Введенное имя пользователя не совпадает с текущим. Операция удаления отменена.", "success": "Учетная запись успешно удалена. Теперь будет выполнен выход из системы."}, "passwordConfirm": {"title": "Подтверждение операции", "message": "Для продолжения введите текущий пароль:", "passwordLabel": "Пароль:", "confirmButton": "Подтвердить", "cancelButton": "Отмена"}}}