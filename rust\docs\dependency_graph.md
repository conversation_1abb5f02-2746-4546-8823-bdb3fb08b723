# Fount模块间依赖关系图

## 依赖关系概览

Fount项目采用分层架构设计，模块间依赖关系清晰，遵循从底层基础设施到上层应用逻辑的层次结构。

## 核心依赖层次

### 1. 依赖层次图
```
应用层 (Application Layer)
├── fount-desktop (Tauri桌面应用)
└── fount-server (Web服务器)
    ├── managers/ (管理器模块)
    ├── endpoints.rs (API端点)
    ├── server.rs (Web服务器核心)
    └── auth.rs (认证系统)

服务层 (Service Layer)  
├── fount-server/src/
│   ├── parts_loader.rs (插件加载器)
│   ├── events.rs (事件系统)
│   ├── jobs.rs (任务管理)
│   ├── ipc_server.rs (IPC通信)
│   └── timers.rs (定时器)

工具层 (Utility Layer)
├── fount-utils (工具库)
│   ├── json_loader.rs (JSON操作)
│   ├── i18n.rs (国际化)
│   ├── console.rs (控制台)
│   ├── exec.rs (进程执行)
│   └── [其他工具模块]

基础层 (Foundation Layer)
└── fount-types (类型定义)
    ├── base_defs.rs (基础类型)
    ├── char_api.rs (角色API)
    ├── plugin_api.rs (插件API)
    └── [其他类型定义]
```

## 详细依赖关系

### 1. fount-types (基础类型层)
**依赖**: 无 (最底层)
**被依赖**: 所有其他模块

**内部模块依赖**:
```rust
base_defs.rs (基础类型)
    ↑
├── char_api.rs (角色API类型)
├── world_api.rs (世界API类型)  
├── plugin_api.rs (插件API类型)
├── shell_api.rs (Shell API类型)
├── ai_source.rs (AI源类型)
├── prompt_struct.rs (提示结构)
└── import_handler_api.rs (导入处理器API)
```

**关键类型导出**:
- `timeStamp_t`, `locale_t`, `role_t`, `info_t`
- `charAPI_t`, `worldAPI_t`, `pluginAPI_t`
- `AIsource_t`, `prompt_struct_t`

### 2. fount-utils (工具库层)
**依赖**: `fount-types`
**被依赖**: `fount-server`, `fount-desktop`

**内部模块依赖**:
```rust
fount-types
    ↑
base_utils (基础工具)
├── json_loader.rs
├── console.rs  
├── ms.rs
├── escape.rs
└── await_timeout.rs
    ↑
system_utils (系统工具)
├── exec.rs
├── env.rs
├── notify.rs
├── tray.rs
└── discord_rpc.rs
    ↑
advanced_utils (高级工具)
├── i18n.rs (依赖json_loader)
├── proxy.rs
├── ratelimit.rs
├── locale.rs (依赖i18n)
└── verify_code.rs
```

**关键功能导出**:
- JSON文件操作
- 国际化支持
- 系统集成 (托盘、通知)
- 进程管理
- 网络工具

### 3. fount-server (服务器核心层)
**依赖**: `fount-types`, `fount-utils`
**被依赖**: `fount-desktop`

**内部模块依赖**:
```rust
fount-types + fount-utils
    ↑
base.rs (基础模块)
    ↑
├── events.rs (事件系统)
├── setting_loader.rs (配置加载)
└── on_shutdown.rs (关闭处理)
    ↑
├── auth.rs (认证系统)
├── parts_loader.rs (插件加载器)
├── jobs.rs (任务管理)
├── timers.rs (定时器)
└── ipc_server.rs (IPC服务器)
    ↑
├── managers/ (管理器模块)
│   ├── mod.rs
│   ├── char_manager.rs
│   ├── shell_manager.rs
│   ├── world_manager.rs
│   ├── personas_manager.rs
│   └── ai_sources_manager.rs
    ↑
├── endpoints.rs (API端点)
└── server.rs (Web服务器)
    ↑
main.rs (应用入口)
```

### 4. 管理器模块内部依赖
```rust
parts_loader.rs + auth.rs + setting_loader.rs
    ↑
managers/mod.rs (管理器注册表)
    ↑
├── char_manager.rs
├── world_manager.rs  
├── personas_manager.rs
├── ai_sources_manager.rs
└── shell_manager.rs (依赖server.rs的路由)
```

### 5. fount-desktop (桌面应用层)
**依赖**: `fount-server`, `fount-utils`, `fount-types`
**被依赖**: 无 (最上层)

**内部依赖**:
```rust
fount-server (完整服务器)
    ↑
desktop/
├── main.rs (Tauri主进程)
├── tray.rs (系统托盘集成)
├── protocol.rs (协议处理)
└── window.rs (窗口管理)
```

## 外部依赖关系

### 1. 核心运行时依赖
```rust
// 异步运行时
tokio = { version = "1.0", features = ["full"] }

// 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

// 错误处理
anyhow = "1.0"
thiserror = "1.0"
```

### 2. Web服务器依赖
```rust
// Web框架
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["fs", "cors"] }

// HTTP客户端
reqwest = { version = "0.11", features = ["json"] }

// 认证
jsonwebtoken = "9.0"
bcrypt = "0.15"
```

### 3. 系统集成依赖
```rust
// 系统托盘
tray-icon = "0.14"

// 系统通知  
notify-rust = "4.0"

// 跨平台路径
dirs = "5.0"

// 配置管理
config = "0.14"
```

### 4. 桌面应用依赖
```rust
// Tauri框架
tauri = { version = "1.0", features = ["api-all"] }

// 单实例
single-instance = "0.3"
```

## 循环依赖检测

### 1. 潜在循环依赖点
- **managers ↔ parts_loader**: 管理器使用parts_loader，但parts_loader不应依赖具体管理器
- **server ↔ shell_manager**: shell_manager需要注册路由到server，但server不应直接依赖shell_manager
- **events ↔ managers**: 事件系统和管理器可能相互依赖

### 2. 循环依赖解决方案
```rust
// 使用trait抽象打破循环依赖
trait PartManager {
    async fn load_part(&self, username: &str, name: &str) -> Result<()>;
    async fn unload_part(&self, username: &str, name: &str) -> Result<()>;
}

// 使用事件系统解耦
struct EventBus {
    subscribers: HashMap<String, Vec<Box<dyn EventHandler>>>,
}

// 使用依赖注入
struct ServerContext {
    auth: Arc<AuthService>,
    parts_loader: Arc<PartsLoader>,
    event_bus: Arc<EventBus>,
}
```

## 启动依赖顺序

### 1. 系统启动流程
```rust
1. 基础设施初始化
   ├── 日志系统 (tracing)
   ├── 配置加载 (config)
   └── 错误监控 (sentry)

2. 核心服务启动  
   ├── 事件系统 (events)
   ├── 认证系统 (auth)
   ├── 配置加载器 (setting_loader)
   └── 插件加载器 (parts_loader)

3. 管理器初始化
   ├── 管理器注册表 (managers/mod)
   ├── 各类型管理器 (char, world, shell等)
   └── 插件预加载

4. 网络服务启动
   ├── IPC服务器 (ipc_server)
   ├── Web服务器 (server)
   ├── API端点注册 (endpoints)
   └── 静态文件服务

5. 后台服务启动
   ├── 任务管理器 (jobs)
   ├── 定时器 (timers)
   ├── 系统托盘 (tray)
   └── Discord RPC
```

### 2. 关闭依赖顺序
```rust
1. 停止接受新请求
2. 完成正在处理的请求
3. 关闭后台服务 (jobs, timers)
4. 卸载所有插件 (按依赖顺序)
5. 关闭网络服务 (server, ipc)
6. 清理资源和保存状态
7. 关闭基础设施
```

## 依赖注入设计

### 1. 服务容器
```rust
#[derive(Clone)]
pub struct ServiceContainer {
    pub auth: Arc<AuthService>,
    pub events: Arc<EventBus>,
    pub parts_loader: Arc<PartsLoader>,
    pub config: Arc<ConfigManager>,
    pub i18n: Arc<I18nService>,
}

impl ServiceContainer {
    pub async fn new() -> Result<Self> {
        let config = Arc::new(ConfigManager::new()?);
        let events = Arc::new(EventBus::new());
        let auth = Arc::new(AuthService::new(config.clone()).await?);
        let i18n = Arc::new(I18nService::new(config.clone()).await?);
        let parts_loader = Arc::new(PartsLoader::new(
            config.clone(),
            events.clone(),
            auth.clone(),
        ).await?);
        
        Ok(Self {
            auth,
            events, 
            parts_loader,
            config,
            i18n,
        })
    }
}
```

### 2. 管理器工厂
```rust
pub struct ManagerFactory {
    container: ServiceContainer,
}

impl ManagerFactory {
    pub fn create_char_manager(&self) -> CharManager {
        CharManager::new(
            self.container.parts_loader.clone(),
            self.container.auth.clone(),
            self.container.events.clone(),
        )
    }
    
    pub fn create_shell_manager(&self, router: Router) -> ShellManager {
        ShellManager::new(
            self.container.parts_loader.clone(),
            self.container.auth.clone(),
            router,
        )
    }
}
```

## 测试依赖管理

### 1. 单元测试隔离
```rust
// 使用mock对象隔离依赖
#[cfg(test)]
mod tests {
    use super::*;
    use mockall::predicate::*;
    
    #[tokio::test]
    async fn test_char_manager() {
        let mut mock_parts_loader = MockPartsLoader::new();
        mock_parts_loader
            .expect_load_part()
            .with(eq("user"), eq("chars"), eq("test_char"))
            .times(1)
            .returning(|_, _, _| Ok(()));
            
        let char_manager = CharManager::new(
            Arc::new(mock_parts_loader),
            Arc::new(MockAuthService::new()),
            Arc::new(MockEventBus::new()),
        );
        
        // 测试逻辑
    }
}
```

### 2. 集成测试
```rust
// 使用测试容器进行集成测试
#[tokio::test]
async fn test_full_system_integration() {
    let test_container = TestServiceContainer::new().await?;
    
    // 测试完整的请求流程
    let response = test_container
        .server
        .request("/api/chars/test_char")
        .await?;
        
    assert_eq!(response.status(), 200);
}
```

这个依赖关系图确保了Rust版本的模块组织清晰、依赖关系明确，避免了循环依赖，并为系统的可测试性和可维护性奠定了基础。
