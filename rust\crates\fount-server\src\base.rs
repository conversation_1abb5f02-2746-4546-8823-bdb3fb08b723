//! 服务器基础模块
//! 对应原文件: src/server/base.mjs

use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use anyhow::Result;

/// 服务器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub debug: bool,
    pub static_dir: PathBuf,
    pub data_dir: PathBuf,
    pub log_level: String,
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 3000,
            debug: false,
            static_dir: PathBuf::from("static"),
            data_dir: PathBuf::from("data"),
            log_level: "info".to_string(),
        }
    }
}

/// 加载配置
pub async fn load_config() -> Result<ServerConfig> {
    // 从配置文件加载配置
    let config = ServerConfig::default();
    Ok(config)
}
