# Rust模板引擎和静态文件服务选型分析

## 需求概览

Fount项目需要模板引擎来动态生成HTML页面，以及高效的静态文件服务来提供前端资源。我们需要选择合适的解决方案来替代Express.js的模板和静态文件功能。

## 模板引擎分析

### 1. Askama
**开发者**: djc
**版本**: 0.12.x (稳定)
**设计理念**: 编译时模板、类型安全、高性能

#### 核心特性
- **编译时**: 模板在编译时解析和验证
- **类型安全**: 强类型模板参数
- **语法简洁**: 类似Jinja2的语法
- **零运行时**: 无运行时模板解析开销
- **自动转义**: 自动HTML转义

#### API示例
```rust
use askama::Template;

#[derive(Template)]
#[template(path = "index.html")]
struct IndexTemplate {
    title: String,
    user: User,
    items: Vec<Item>,
}

// 使用模板
let template = IndexTemplate {
    title: "Fount Dashboard".to_string(),
    user: current_user,
    items: user_items,
};

let html = template.render()?;
```

#### 模板语法
```html
<!-- templates/index.html -->
<!DOCTYPE html>
<html>
<head>
    <title>{{ title }}</title>
</head>
<body>
    <h1>Welcome, {{ user.name }}!</h1>
    <ul>
    {% for item in items %}
        <li>{{ item.name }} - {{ item.description }}</li>
    {% endfor %}
    </ul>
</body>
</html>
```

#### 优势
- **最高性能**: 编译时优化
- **类型安全**: 编译时错误检查
- **零开销**: 无运行时解析
- **简单**: 学习曲线平缓

### 2. Tera
**开发者**: Keats
**版本**: 1.x (稳定)
**设计理念**: 运行时模板、功能丰富、Django风格

#### 核心特性
- **运行时**: 运行时模板解析
- **功能丰富**: 大量内置过滤器和函数
- **继承**: 模板继承和包含
- **国际化**: 内置i18n支持
- **热重载**: 开发时模板热重载

#### API示例
```rust
use tera::{Tera, Context};

// 初始化Tera
let mut tera = Tera::new("templates/**/*")?;

// 创建上下文
let mut context = Context::new();
context.insert("title", "Fount Dashboard");
context.insert("user", &current_user);
context.insert("items", &user_items);

// 渲染模板
let html = tera.render("index.html", &context)?;
```

#### 优势
- **功能丰富**: 大量内置功能
- **灵活性**: 运行时动态性
- **热重载**: 开发友好
- **Django风格**: 熟悉的语法

#### 劣势
- **性能**: 运行时解析开销
- **类型安全**: 缺乏编译时检查

### 3. Handlebars
**开发者**: sunng87
**版本**: 4.x (稳定)
**设计理念**: JavaScript Handlebars兼容

#### 核心特性
- **兼容性**: 与JavaScript Handlebars兼容
- **逻辑少**: 最小化模板逻辑
- **助手函数**: 自定义助手函数
- **部分模板**: 模板组合

#### API示例
```rust
use handlebars::Handlebars;
use serde_json::json;

let mut handlebars = Handlebars::new();
handlebars.register_template_file("index", "templates/index.hbs")?;

let data = json!({
    "title": "Fount Dashboard",
    "user": current_user,
    "items": user_items
});

let html = handlebars.render("index", &data)?;
```

#### 优势
- **兼容性**: 与前端Handlebars兼容
- **简单**: 语法简单
- **逻辑分离**: 强制逻辑与视图分离

#### 劣势
- **功能有限**: 内置功能较少
- **性能**: 运行时解析

### 4. minijinja
**开发者**: mitsuhiko
**版本**: 1.x (稳定)
**设计理念**: Jinja2兼容、轻量级

#### 核心特性
- **Jinja2兼容**: 与Python Jinja2兼容
- **轻量级**: 最小的依赖
- **沙箱**: 安全的模板执行
- **扩展性**: 丰富的扩展机制

## 静态文件服务分析

### 1. tower-http ServeDir
**来源**: Tower生态
**特点**: 与Axum深度集成

#### API示例
```rust
use axum::routing::get_service;
use tower_http::services::ServeDir;

let app = Router::new()
    .nest_service("/static", get_service(ServeDir::new("static")))
    .nest_service("/", get_service(ServeDir::new("public")));
```

#### 优势
- **集成度**: 与Axum完美集成
- **性能**: 高效的文件服务
- **缓存**: 内置缓存支持
- **压缩**: 支持gzip/brotli压缩

### 2. rust-embed
**开发者**: pyros2097
**特点**: 编译时嵌入静态文件

#### API示例
```rust
use rust_embed::RustEmbed;

#[derive(RustEmbed)]
#[folder = "static/"]
struct Assets;

// 获取嵌入的文件
let file = Assets::get("index.html").unwrap();
let content = std::str::from_utf8(file.data.as_ref())?;
```

#### 优势
- **单文件**: 所有资源打包到可执行文件
- **性能**: 内存中直接访问
- **部署**: 简化部署流程

#### 劣势
- **体积**: 增加可执行文件大小
- **灵活性**: 无法动态更新资源

## 最终选择：Askama + tower-http

### 组合策略

我们选择Askama作为主要模板引擎，tower-http作为静态文件服务：

- **Askama**: 处理动态HTML生成
- **tower-http**: 提供静态文件服务
- **rust-embed**: 可选的资源嵌入

### 选择理由

#### 1. 最佳性能组合
```rust
use askama::Template;
use axum::{response::Html, routing::get, Router};
use tower_http::services::ServeDir;

#[derive(Template)]
#[template(path = "shell.html")]
struct ShellTemplate {
    shell_name: String,
    user: User,
    config: ShellConfig,
    i18n: HashMap<String, String>,
}

async fn render_shell(
    Path(shell_name): Path<String>,
    Extension(user): Extension<User>,
) -> Result<Html<String>, AppError> {
    let config = load_shell_config(&shell_name).await?;
    let i18n = load_i18n_data(&user.locale).await?;
    
    let template = ShellTemplate {
        shell_name,
        user,
        config,
        i18n,
    };
    
    let html = template.render()
        .map_err(|e| AppError::TemplateError(e.to_string()))?;
    
    Ok(Html(html))
}

// 路由配置
let app = Router::new()
    .route("/shells/:name", get(render_shell))
    .nest_service("/static", get_service(ServeDir::new("static")))
    .nest_service("/", get_service(ServeDir::new("public")));
```

#### 2. 模板系统设计
```rust
// 基础模板结构
#[derive(Template)]
#[template(path = "base.html")]
struct BaseTemplate {
    title: String,
    user: Option<User>,
    locale: String,
    theme: String,
    csrf_token: String,
}

// Shell模板
#[derive(Template)]
#[template(path = "shells/shell_base.html", escape = "html")]
struct ShellBaseTemplate {
    base: BaseTemplate,
    shell_name: String,
    shell_config: Value,
    shell_data: Value,
}

// 特定Shell模板
#[derive(Template)]
#[template(path = "shells/chat/index.html", escape = "html")]
struct ChatShellTemplate {
    base: ShellBaseTemplate,
    characters: Vec<Character>,
    chat_history: Vec<ChatMessage>,
    active_character: Option<String>,
}
```

#### 3. 模板继承和组件
```html
<!-- templates/base.html -->
<!DOCTYPE html>
<html lang="{{ locale }}" data-theme="{{ theme }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ title }}{% endblock %}</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui/daisyui.css" rel="stylesheet" />
    <link href="/static/base.css" rel="stylesheet" />
    {% block extra_css %}{% endblock %}
    
    <!-- Meta -->
    <meta name="csrf-token" content="{{ csrf_token }}">
    {% block extra_meta %}{% endblock %}
</head>
<body>
    {% block header %}
    <header class="navbar bg-base-100">
        <div class="navbar-start">
            <h1 class="text-xl font-bold">Fount</h1>
        </div>
        <div class="navbar-end">
            {% if user %}
                <div class="dropdown dropdown-end">
                    <label tabindex="0" class="btn btn-ghost btn-circle avatar">
                        <div class="w-10 rounded-full">
                            <img src="{{ user.avatar_url }}" alt="{{ user.name }}" />
                        </div>
                    </label>
                    <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                        <li><a href="/settings">Settings</a></li>
                        <li><a href="/logout">Logout</a></li>
                    </ul>
                </div>
            {% else %}
                <a href="/login" class="btn btn-primary">Login</a>
            {% endif %}
        </div>
    </header>
    {% endblock %}
    
    <main>
        {% block content %}{% endblock %}
    </main>
    
    {% block footer %}
    <footer class="footer footer-center p-4 bg-base-300 text-base-content">
        <div>
            <p>© 2024 Fount. All rights reserved.</p>
        </div>
    </footer>
    {% endblock %}
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser"></script>
    <script type="module" src="/static/base.mjs"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>

<!-- templates/shells/chat/index.html -->
{% extends "base.html" %}

{% block title %}Chat - {{ base.shell_name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/shells/chat/index.css" />
{% endblock %}

{% block content %}
<div class="container mx-auto p-4">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <!-- Character List -->
        <div class="lg:col-span-1">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-header">
                    <h2 class="card-title">Characters</h2>
                </div>
                <div class="card-body">
                    {% for character in characters %}
                    <div class="character-item {% if active_character == character.name %}active{% endif %}"
                         data-character="{{ character.name }}">
                        <div class="avatar">
                            <div class="w-12 rounded-full">
                                <img src="{{ character.avatar_url }}" alt="{{ character.name }}" />
                            </div>
                        </div>
                        <div class="character-info">
                            <h3>{{ character.name }}</h3>
                            <p class="text-sm opacity-70">{{ character.description|truncate(50) }}</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <!-- Chat Area -->
        <div class="lg:col-span-3">
            <div class="card bg-base-100 shadow-xl h-96">
                <div class="card-body">
                    <div class="chat-messages" id="chat-messages">
                        {% for message in chat_history %}
                        <div class="chat {% if message.role == 'user' %}chat-end{% else %}chat-start{% endif %}">
                            <div class="chat-image avatar">
                                <div class="w-10 rounded-full">
                                    <img src="{{ message.avatar_url }}" alt="{{ message.sender }}" />
                                </div>
                            </div>
                            <div class="chat-header">
                                {{ message.sender }}
                                <time class="text-xs opacity-50">{{ message.timestamp|date }}</time>
                            </div>
                            <div class="chat-bubble">{{ message.content|markdown }}</div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="chat-input mt-4">
                        <form id="chat-form" class="flex gap-2">
                            <input type="text" 
                                   id="message-input" 
                                   class="input input-bordered flex-1" 
                                   placeholder="Type your message..." 
                                   autocomplete="off" />
                            <button type="submit" class="btn btn-primary">Send</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script type="module" src="/static/shells/chat/index.mjs"></script>
{% endblock %}
```

#### 4. 自定义过滤器和函数
```rust
use askama::filters;

// 自定义过滤器
pub fn markdown(s: &str) -> askama::Result<String> {
    // 使用markdown解析器
    let parser = pulldown_cmark::Parser::new(s);
    let mut html = String::new();
    pulldown_cmark::html::push_html(&mut html, parser);
    Ok(html)
}

pub fn truncate(s: &str, len: usize) -> askama::Result<String> {
    if s.len() <= len {
        Ok(s.to_string())
    } else {
        Ok(format!("{}...", &s[..len]))
    }
}

pub fn date(timestamp: &SystemTime) -> askama::Result<String> {
    let datetime: DateTime<Local> = (*timestamp).into();
    Ok(datetime.format("%Y-%m-%d %H:%M").to_string())
}

// 注册过滤器
mod filters {
    pub use super::{markdown, truncate, date};
}
```

#### 5. 国际化集成
```rust
#[derive(Template)]
#[template(path = "i18n_base.html")]
struct I18nTemplate {
    locale: String,
    messages: HashMap<String, String>,
}

impl I18nTemplate {
    pub fn new(locale: &str, i18n_manager: &I18nManager) -> Self {
        let messages = i18n_manager.get_all_messages(locale);
        Self {
            locale: locale.to_string(),
            messages,
        }
    }
    
    pub fn t(&self, key: &str) -> &str {
        self.messages.get(key).map(|s| s.as_str()).unwrap_or(key)
    }
}

// 在模板中使用
// {{ t("welcome.message") }}
```

#### 6. 静态文件优化
```rust
use tower_http::services::{ServeDir, ServeFile};
use tower_http::compression::CompressionLayer;
use tower_http::set_header::SetResponseHeaderLayer;

// 静态文件服务配置
let static_service = get_service(
    ServeDir::new("static")
        .append_index_html_on_directories(false)
        .precompressed_gzip()
        .precompressed_br()
).layer(CompressionLayer::new())
 .layer(SetResponseHeaderLayer::if_not_present(
     header::CACHE_CONTROL,
     HeaderValue::from_static("public, max-age=31536000"),
 ));

// 特殊文件处理
let app = Router::new()
    .route("/favicon.ico", get_service(ServeFile::new("static/favicon.ico")))
    .route("/robots.txt", get_service(ServeFile::new("static/robots.txt")))
    .nest_service("/static", static_service)
    .nest_service("/", get_service(ServeDir::new("public")));
```

#### 7. 开发模式热重载
```rust
#[cfg(debug_assertions)]
mod dev_mode {
    use notify::{Watcher, RecursiveMode, watcher};
    use std::sync::mpsc;
    use std::time::Duration;
    
    pub fn watch_templates() {
        let (tx, rx) = mpsc::channel();
        let mut watcher = watcher(tx, Duration::from_secs(1)).unwrap();
        
        watcher.watch("templates", RecursiveMode::Recursive).unwrap();
        
        tokio::spawn(async move {
            loop {
                match rx.recv() {
                    Ok(event) => {
                        println!("Template changed: {:?}", event);
                        // 在开发模式下重新编译模板
                    }
                    Err(e) => println!("Watch error: {:?}", e),
                }
            }
        });
    }
}
```

### 错误处理

```rust
#[derive(Debug, thiserror::Error)]
pub enum TemplateError {
    #[error("Template render error: {0}")]
    RenderError(String),
    
    #[error("Template not found: {0}")]
    NotFound(String),
    
    #[error("Template compilation error: {0}")]
    CompilationError(String),
    
    #[error("I18n error: {0}")]
    I18nError(String),
}

impl IntoResponse for TemplateError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            TemplateError::NotFound(_) => (StatusCode::NOT_FOUND, "Template not found"),
            _ => (StatusCode::INTERNAL_SERVER_ERROR, "Template error"),
        };
        
        (status, error_message).into_response()
    }
}
```

这个选择确保了我们能够获得最佳的性能和类型安全性，同时保持模板的简洁性和可维护性，完美替代Express.js的模板和静态文件功能。
