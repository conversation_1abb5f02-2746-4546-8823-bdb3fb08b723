<!DOCTYPE html>
<html data-theme="dark">

<head>
	<meta charset="UTF-8">
	<meta name="darkreader-lock">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title data-i18n="chat.title"></title>
	<link href="https://cdn.jsdelivr.net/npm/daisyui/daisyui.css" rel="stylesheet" type="text/css" />
	<link href="/base.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser"></script>
	<script type="module" src="https://cdn.jsdelivr.net/gh/steve02081504/fount/.statistic/scripts/chat.mjs"></script>
	<script type="module" src="/base.mjs"></script>
	<link rel="stylesheet" href="./index.css" type="text/css" />
</head>

<body class="flex h-screen">
	<!-- Left Sidebar -->
	<div class="absolute top-0 left-0 z-40" id="left-drawer-container">
		<div class="drawer h-full">
			<input id="left-drawer" type="checkbox" class="drawer-toggle" />
			<div class="drawer-side z-40">
				<label for="left-drawer" class="drawer-overlay"></label>
				<div class="w-80 bg-base-200 text-base-content p-4 overflow-y-auto">
					<ul class="menu w-full">
						<!-- Sidebar content here -->
						<li id="world-info">
							<div class="flex items-center justify-between mb-2">
								<div class="flex items-center">
									<img src="https://api.iconify.design/line-md/map-marker.svg" class="w-8 h-8 mr-2 icon" data-i18n="chat.sidebar.world.icon" />
									<h3 class="text-lg font-bold" data-i18n="chat.sidebar.world.title"></h3>
								</div>
							</div>
							<select class="select border w-full" id="world-select">
								<!-- 世界信息选项 -->
							</select>
							<div id="world-details" class="sidebar-item-details">
								<!-- 世界信息详情 -->
							</div>
						</li>
						<li id="persona-info">
							<div class="flex items-center justify-between mt-6 mb-2">
								<div class="flex items-center">
									<img src="https://api.iconify.design/line-md/emoji-grin.svg" class="w-8 h-8 mr-2 icon" data-i18n="chat.sidebar.persona.icon" />
									<h3 class="text-lg font-bold" data-i18n="chat.sidebar.persona.title"></h3>
								</div>
							</div>
							<select class="select border w-full" id="persona-select">
								<!-- 角色信息选项 -->
							</select>
							<div id="persona-details" class="sidebar-item-details">
								<!-- 角色信息详情 -->
							</div>
						</li>
						<li id="char-list">
							<div class="flex items-center justify-between mt-6 mb-2">
								<div class="flex items-center">
									<img src="https://api.iconify.design/line-md/person.svg" class="w-8 h-8 mr-2 icon" data-i18n="chat.sidebar.charList.icon" />
									<h3 class="text-lg font-bold" data-i18n="chat.sidebar.charList.title"></h3>
								</div>
							</div>
							<div class="flex justify-between">
								<select class="select border w-full" id="char-select">
									<!-- 角色信息选项 -->
								</select>
								<div class="join">
									<button id="add-char-button" class="btn btn-sm btn-primary join-item" data-i18n="chat.sidebar.charList.buttons.addChar">
										<img src="https://api.iconify.design/line-md:account-add.svg" data-i18n="chat.sidebar.charList.buttons.addCharIcon" />
									</button>
								</div>
							</div>
							<div id="char-details" class="flex flex-col gap-2 sidebar-item-details">
								<!-- 角色信息详情 -->
							</div>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>

	<!-- Main Content -->
	<div class="flex-grow flex flex-col items-center justify-start h-full lg:pt-4">
		<!-- Chat Area -->
		<div class="container mx-auto lg:p-4 w-full lg:w-3/4 flex flex-col h-full">
			<div class="chat-container card card-border bg-base-100/65 shadow-xl h-full flex flex-col">
				<div class="chat-header card-title lg:flex justify-between items-center p-4">
					<h2 class="flex-grow text-lg font-bold" data-i18n="chat.chatArea.title"></h2>
					<div class="flex-none">
						<label for="left-drawer" class="btn btn-square btn-ghost" data-i18n="chat.chatArea.menuButton">
							<img src="https://api.iconify.design/line-md/list-3-solid.svg" class="text-icon" data-i18n="chat.chatArea.menuButtonIcon" />
						</label>
					</div>
				</div>
				<div class="chat-messages flex-grow overflow-y-auto flex flex-col lg:p-8" id="chat-messages">
				</div>
				<div class="chat-input join p-4">
					<button id="upload-button" class="join-item btn btn-secondary h-full" data-i18n="chat.chatArea.uploadButton"><img src="https://api.iconify.design/line-md/upload.svg" data-i18n="chat.chatArea.uploadButtonIcon" /></button>
					<div class="flex flex-col">
						<button id="voice-button" class="btn btn-accent" data-i18n="chat.chatArea.voiceButton"><img src="https://api.iconify.design/material-symbols/mic.svg" data-i18n="chat.chatArea.voiceButtonIcon" /></button>
						<button id="photo-button" class="btn btn-accent" data-i18n="chat.chatArea.photoButton"><img src="https://api.iconify.design/proicons/photo.svg" data-i18n="chat.chatArea.photoButtonIcon" /></button>
					</div>
					<textarea id="message-input" class="join-item textarea flex-grow" data-i18n="chat.chatArea.input"></textarea>
					<button id="send-button" class="join-item btn btn-primary h-full" data-i18n="chat.chatArea.sendButton"><img src="https://api.iconify.design/line-md/chat-round-twotone.svg" data-i18n="chat.chatArea.sendButtonIcon" /></button>
				</div>
				<input type="file" id="file-input" class="hidden" multiple />
				<div id="attachment-preview" class="p-4 flex gap-4 overflow-x-auto">
					<!-- 附件预览 -->
				</div>
			</div>
		</div>
	</div>

	<!-- Right Sidebar (Description) -->
	<div class="absolute top-0 right-0 z-40 w-80 h-full hidden" id="right-sidebar-container">
		<div class="bg-base-300 text-base-content border-l-2 border-base-content h-full" id="right-sidebar">
			<div class="p-4 h-full overflow-y-auto">
				<h2 class="text-xl mb-2" data-i18n="chat.rightSidebar.title"></h2>
				<article id="item-description" class="markdown-body"></article>
			</div>
		</div>
	</div>

	<script type="module" src="./index.mjs"></script>
</body>

</html>
